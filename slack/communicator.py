import json
import requests

headers = {"content-type": "application/json; charset=utf-8"}


def send_slack_message(message: str, chat_id: str, token: str = None, image: bytes = None, image_name: str = None):
    """
    Sends a message to a Slack channel using a bot.

    Parameters:
    - 'message' (str): Required message to send.
    - 'chat_id' (str): Required - Slack channel ID.
    - 'token' (str): Optional - Slack bot token  (Web Seeker bot is used if not provided)
    - 'image' (str): Optional - image in bytes with UTF-8 format.
    - 'image_name' (str): Optional - image name (e.g.'booking-image.png')

    Returns:
    - dict: Response from Slack API.
    """
    post_message = {
        'message': message,
        'token': token,
        'chat_id': chat_id,
        'image': image,
        'image_name': image_name
    }

    response = requests.post('https://europe-west1-build-tools-2.cloudfunctions.net/slack_notify',
                             headers=headers,
                             data=json.dumps(post_message))
    return response


def send_slack_message_v1(chat_id: str, message_data: dict, token: str = None):
    """
    Sends a message (template v1) to a Slack channel using a bot.

    Parameters:
    - 'chat_id' (str): Required - Slack channel ID.
    - 'message_data' (dict): Required - message (slack block) containing the following keys:
        - 'header' (str): Required - content of the header
        - 'hotel_name' (str): Required - content of the name
        - 'hotel_code' (str): Required - content of the code
        - 'bold_content' (str): Optional - bold content
        - 'italic_content' (str): Optional - italic content
        - 'url' (str): Optional - italic content
        - 'image' (str): Optional - image in bytes with UTF-8 format.
        - 'image_name' (str): Image name (e.g.'booking-image.png')
    - 'token' (str): Optional - Slack bot token  (Web Seeker bot is used if not provided)

    Returns:
    - dict: Response from Slack API.
    """
    data_to_send = {
        'message_v1': message_data,
        'token': token,
        'chat_id': chat_id,
    }

    response = requests.post('https://europe-west1-build-tools-2.cloudfunctions.net/slack_notify', headers=headers,
                             data=json.dumps(data_to_send))
    return response
