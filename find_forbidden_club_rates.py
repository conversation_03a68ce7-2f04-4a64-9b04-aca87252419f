"""
This module handles the logic for managing club rates visibility in hotels.
It provides functionality to:
- Determine which rates should be visible/hidden based on club membership
- Handle rate visibility for different user levels
- Manage agency-specific rate configurations
- Process club level configurations and priorities

The main entry point is find_forbidden_club_rates(), which returns a list of rates
that should be hidden for the current user based on various conditions.
"""

import logging
from typing import Dict, List, Optional, Tuple
from paraty_commons_3.decorators.cache.managers_cache import manager_cache
from paraty_commons_3.language_utils import SPANISH
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value, get_hotel_web_config_item, get_web_section, get_pictures_for_entity, id_to_entity_key, get_web_page_property
from paraty_commons_3.common_data.data_management.rates_utils import get_all_rates
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


# ===================== CONSTANTS =====================
# Club configuration constants
MEMBERS_CLUB = "Members Club"  # Indicates if a hotel has Club (activates multiple functionalities)
MEMBERS_CLUB_LEVELS = 'Members Club Levels'
MEMBERS_CLUB_NAMESPACE = "memberclub namespace"  # Namespace where member users are stored
DISABLED_CLUB_FOR_AGENCY = "Disable club with agency"
CLUB_LOCK_RATES = 'Club lock rates'
ONLY_LEVELS = 'only_levels'

# User rates configuration
RATES_LOGIN_USERS_CLUB = "Login club rates"  # Rates for logged users (CLUB VERSION)
RATES_LOGIN_USERS = "Login user rates"
RATES_LOGIN_HIDDEN = "Login hidden rates"  # If populated, rates in "Login user rates" are hidden for non-users
AVOID_CLUB = 'avoid_club'

# Agency configuration
AGENCY_INFO = "AgencyConfiguration"

# =====================================================

def get_pictures_from_section_name(hotel: Dict, section_name: str) -> List[Dict]:
    """
    Retrieves pictures and their properties for a given section name in the hotel.
    
    Args:
        hotel: Hotel entity containing application ID
        section_name: Name of the section to get pictures from
        
    Returns:
        List of picture dictionaries with their properties
    """
    entity_key = get_web_section(hotel, section_name, "es", set_languages=False)
    if not entity_key:
        return []
        
    main_key = id_to_entity_key(hotel.get("applicationId"), entity_key.key)
    pictures_entity = get_pictures_for_entity(hotel.get("applicationId"), main_key, "", False)

    for picture in pictures_entity:
        properties = get_web_page_property(hotel, picture.get('key'), "SPANISH")
        for property in properties:
            picture.update({property.get('mainKey'): property.get('value')})
        
    return pictures_entity


def get_club_lock_rates(hotel: Dict, revert: bool = False) -> Dict[str, str]:
    """
    Retrieves club lock rates configuration for the hotel.
    
    Args:
        hotel: Hotel entity to get lock rates from
        revert: If True, returns rates with keys and values swapped
        
    Returns:
        Dictionary mapping rate names to their locked status
    """
    configurations = get_hotel_web_config_item(hotel, CLUB_LOCK_RATES)
    if not configurations:
        return {}
   
    if revert:
        return {v.strip(): k.strip() for k, v in configurations.items()}
        
    return {k.strip(): v.strip() for k, v in configurations.items()}


def get_rates_to_login(rates_user_rates_club: Optional[str]) -> List[str]:
    """
    Parses the rates configuration string into a list of rates.
    
    Args:
        rates_user_rates_club: String containing rates separated by '@@'
        
    Returns:
        List of rate names
    """
    if not rates_user_rates_club:
        return []
        
    return rates_user_rates_club.split("@@")


def get_all_club_rates(hotel_code: str, rates_login_user_club: Optional[str]) -> List[str]:
    """
    Gets all club rates for a hotel, combining logged user rates and locked rates.
    
    Args:
        hotel_code: Hotel application ID
        rates_login_user_club: Configuration string for logged user rates
        
    Returns:
        List of all club rate names
    """
    rates_list = []
    
    # Add logged user rates
    rates_logged_users = get_rates_to_login(rates_login_user_club)
    if rates_logged_users:
        rates_list.extend(rates_logged_users)

    # Add locked rates
    lock_rates = get_club_lock_rates(hotel_code)
    if lock_rates:
        rates_list.extend(lock_rates.keys())

    return rates_list

def club_is_active(hotel: Dict, rates_login_users_club: Optional[str]) -> bool:
    """
    Check if the club functionality is active for a hotel based on configuration settings.
    
    The club is considered active if either:
    - The hotel has club configuration enabled
    - The hotel has club namespace configuration
    - The hotel has club rates configuration for logged users
    
    Args:
        hotel: Hotel entity to check club status
        rates_login_users_club: Configuration string for logged user rates
        
    Returns:
        bool: True if club is active, False otherwise
    """
    hotel_code = hotel.get('applicationId')
    
    # Check club configuration
    club_config = get_hotel_advance_config_value(hotel_code, MEMBERS_CLUB)
    club_namespace = get_hotel_advance_config_value(hotel_code, MEMBERS_CLUB_NAMESPACE)
    login_club_rates = get_hotel_advance_config_value(hotel_code, RATES_LOGIN_USERS_CLUB)

    return bool(club_config or club_namespace or login_club_rates)

def get_level_from_points(points: int, user_levels_config: Optional[str]) -> Optional[str]:
    """
    Determines the user's club level based on their points and the hotel's level configuration.
    
    Args:
        points: User's current points
        hotel_code: Hotel application ID
        user_levels_config: Configuration string containing level definitions
        
    Returns:
        str: Name of the user's level, or None if no level matches
    """
    if not user_levels_config:
        return None

    # Extract levels configuration
    levels_config = 'levels'
    if levels_config not in user_levels_config:
        return None

    # Parse level configuration
    level_config = next(
        (x for x in user_levels_config.split(";") if levels_config in x),
        None
    )
    if not level_config:
        return None

    # Format levels into a dictionary
    level_config = level_config.split('=')[1]
    formatted_levels = {}
    
    for level_info in level_config.split("|"):
        points_range, name_level = level_info.split("-")
        min_points, max_points = map(int, points_range.split(":"))
        formatted_levels[name_level] = (min_points, max_points)

    # Find matching level
    for level_name, (min_points, max_points) in formatted_levels.items():
        if min_points <= points <= max_points:
            return level_name

    return None

def retrieve_all_information_of_club_levels(hotel: Dict, language: str = SPANISH) -> Dict[str, Dict]:
    """
    Retrieves and formats all club level information for a hotel.
    
    This function:
    1. Gets club levels from the hotel's configuration
    2. Processes each level's properties (priority, nights, points, etc.)
    3. Merges individual level configurations if they exist
    
    Args:
        hotel: Hotel entity containing application ID
        language: Language code for retrieving level information (default: SPANISH)
        
    Returns:
        Dictionary mapping level titles to their configuration data
    """
    hotel_code = hotel.get('applicationId')
    master_club_namespace = get_hotel_advance_config_value(hotel_code, MEMBERS_CLUB_NAMESPACE)
  
    # Get base club levels
    club_levels = get_pictures_from_section_name(hotel, '_club_levels')    
    levels_info = {}

    # Process each level's properties
    for level_element in club_levels:
        if not level_element.get('title'):
            continue
            
        level_data = {
            'name': level_element.get('description'),
            'title': level_element.get('title')
        }

        # Add optional properties if they exist
        optional_properties = [
            'priority',
            'level_nights',
            'level_points_range',
            'level_spent_money',
            'rates',
            'confirmation_header_club',
            'confirmation_footer_club',
            'booking_benefits'
        ]
        
        for prop in optional_properties:
            if level_element.get(prop):
                if prop == 'rates':
                    level_data[prop] = level_element[prop].split("@@")
                elif prop in ['level_nights', 'level_points_range', 'level_spent_money']:
                    level_data[prop.replace('level_', '')] = level_element[prop].split("-")
                else:
                    level_data[prop] = level_element[prop]

        levels_info[level_element['title']] = level_data

    # Merge individual level configurations
    individual_club_levels = get_pictures_from_section_name(hotel, '_club_levels')
    individual_club_levels = {level['title']: level for level in individual_club_levels}
    
    for level_key in levels_info:
        if individual_club_levels.get(level_key, {}).get('rates'):
            levels_info[level_key]['rates'] = individual_club_levels[level_key]['rates'].split("@@")

    return levels_info


def rates_has_user_level(hotel_code: str, user_levels_config: Optional[str], rates_login_hidden: Optional[str]) -> Optional[Dict[str, List[str]]]:
    """
    Determines if a hotel has user level rates configured and returns their configuration.
    
    This function handles two cases:
    1. Traditional configuration: Rates are defined in the user_levels_config string
    2. Form.io configuration: Rates are defined in the club levels
    
    Args:
        hotel_code: Hotel application ID
        user_levels_config: Configuration string containing rate definitions
        rates_login_hidden: Configuration for hidden rates
        
    Returns:
        Dictionary mapping level names to their allowed rates, or None if no configuration exists
    """
    # Early return if no configuration exists
    if not user_levels_config and rates_login_hidden and rates_login_hidden != ONLY_LEVELS:
        return None

    # Try to get rates from traditional configuration
    rates_levels_config = 'rates_levels'
    rates_config = None

    if user_levels_config and rates_levels_config in user_levels_config:
        rates_config = [x for x in user_levels_config.split(";") if rates_levels_config in x]

    # Handle Form.io configuration
    if not rates_config and user_levels_config == 'True':
        levels_info = retrieve_all_information_of_club_levels(hotel_code)
        return any(level_info.get('rates') for level_info in levels_info.values())

    if not rates_config:
        return None

    # Parse and format rates configuration
    rates_config = rates_config[0].split("=")[1]
    return {
        level: rates.split(",")
        for category_rate in rates_config.split("|")
        for level, rates in [category_rate.split(":")]
    }


def separate_allowed_forbidden_club_rates(hotel, rates_login_list, user_levels_config, rates_login_hidden):
    try:
        all_available_rates = rates_login_list.copy()
        rates_to_avoid = []
        level = ''
        application_id = hotel.get('applicationId')
        all_levels_info = retrieve_all_information_of_club_levels(hotel)
        rates_user_levels = rates_has_user_level(application_id, user_levels_config, rates_login_hidden)
        
        def get_all_leveled_rates():
            return [
                rate.lower().strip()
                for level_info in all_levels_info.values()
                for rate in level_info.get('rates', [])
            ]
        
        if rates_user_levels and (not rates_login_hidden or rates_login_hidden == ONLY_LEVELS):
            logging.info("Actual user category is: Unregistered")
            user_level = get_level_from_points(0,  user_levels_config)
            if user_level:
                allowed_rates = [rate.lower() for rate in rates_user_levels.get(user_level, [])]
                rates_to_avoid = [rate for rate in all_available_rates if rate not in allowed_rates]
             
            elif rates_login_hidden == ONLY_LEVELS:
                leveled_rates = get_all_leveled_rates()
                rates_to_avoid = [
                    rate for rate in all_available_rates
                    if rate in rates_login_list and rate in leveled_rates
                ]
        else:
            if rates_login_hidden == ONLY_LEVELS:
                rates_to_avoid = get_all_leveled_rates()
            else:
                sorted_levels = sorted(
                    all_levels_info.items(),
                    key=lambda x: x[1].get('priority', float('inf'))
                )
                if sorted_levels:
                    highest_priority_level = sorted_levels[0][1]
                    level = highest_priority_level.get('title', '')
                   
    except Exception as e:
        logging.exception("Error while trying to filter club rates")
    
    return  rates_to_avoid, level


def build_rate_visibility_list(hotel, results, forbidden_rates, level, rates_login_hidden, filtered_rates_agency, all_club_rates) -> List[Dict]:
    """
    Applies all filters (club, agency, etc.) to the list of rates and returns the final list of rates to show/hide.

    Args:
        hotel: Hotel object
        results: List of rate keys to check
        forbidden_rates: List of forbidden (hidden) rate names
        level: User's club level (if any)
        rates_login_hidden: Configuration for hidden rates
        filtered_rates_agency: List of agency rates to filter
    Returns:
        List of dictionaries with rate information and visibility
    """
    if not results:
        return []
    rates_all_rates = get_all_rates(hotel.get('applicationId'))
    
    # Create dictionary with key:localName format
    rates_dict = {rate['key']: rate for rate in rates_all_rates if rate.get('enabled', True)}
    logging.info(f'Created rates dictionary with {len(rates_dict)} rates')
    
    # Ensure all forbidden rates are lowercase
    forbidden_rates = [rate.strip().lower() for rate in forbidden_rates]
    logging.info(f'Normalized {len(forbidden_rates)} forbidden rates to lowercase')
    
   
    rates_to_remove = []

    if rates_login_hidden and  rates_login_hidden != ONLY_LEVELS:            
        for rate in results:         
            local_name = rates_dict.get(rate, {}).get('localName', '').strip().lower()
        
            if local_name in forbidden_rates and local_name not in rates_to_remove and level.lower() in local_name:
                rates_to_remove.append(local_name)

    results_to_remove_agenct = []   
    if filtered_rates_agency:
        for rate in results:         
            local_name = rates_dict.get(rate, {}).get('localName', '').strip().lower()            
            for rate_agency in filtered_rates_agency:
                if rate_agency and rate_agency.strip().lower() in local_name:
                    results_to_remove_agenct.append(rate)

  
            
    result_hidden = {}      
    rates_dict_reverse = {rate.get('localName', '').strip().lower() : rate['key'] for rate in rates_all_rates if rate.get('enabled', True)}
    
    # Log all forbidden rates that were found
    for forbidden_rate in forbidden_rates:
        if forbidden_rate in rates_to_remove:
            continue
        # Check if rate exists in rates_dict before adding
        if rates_dict_reverse.get(forbidden_rate, ''):
            result_hidden[rates_dict_reverse[forbidden_rate]] = forbidden_rate
            logging.info(f'Added to result_hidden: {forbidden_rate}')
        else:
            logging.info(f'Rate not found in rates_dict: {forbidden_rate}')
    

    result_final = []
    for rate in results:
        value = rates_dict.get(rate, '')
        rate_paquete = None
        # Verificar si value contiene la palabra PACKAGE
        if not value and 'PACKAGE' in str(rate):
            # Si contiene PACKAGE, hacer split con '_@_' y tomar el valor en posición 1
            if isinstance(rate, str):
                parts = rate.split('_@_')
                if len(parts) > 2:
                    rate_paquete = parts[2]
                    value = rates_dict.get(rate_paquete, '')

        if not value:
            continue
                    
        local_name = value.get('localName').strip().lower() if value and value.get('localName') else ''
        club = local_name in forbidden_rates or local_name in all_club_rates if local_name else False
        if result_hidden.get(rate) or rate in results_to_remove_agenct:
            result_final.append({
                  "key": value.get('key') if value else '',
                "name": value.get('localName'),
                "visible": False,
                "reason": "Not priority",
                "club": club
               
            })
        else:
            result_final.append({
                "key": value.get('key'),
                "name": value.get('localName'),
                "visible": True,
                "reason": "priority",
                "club": club
             
            })
    return result_final 


def filter_rates_agencies(hotel: Dict) -> List[str]:
    """
    Gets the list of rates that should be filtered based on agency configuration.
    
    This function:
    1. Gets the agency configuration for the hotel
    2. Checks for rates configuration in the main agency info
    3. If not found, checks in remote login agencies
    4. Returns the list of rates to filter
    
    Args:
        hotel: Hotel entity containing application ID
        
    Returns:
        List of rate names to filter, or empty list if no configuration exists
    """
    try:
        hotel_code = hotel.get('applicationId')
        agency_info = get_hotel_web_config_item(hotel_code, AGENCY_INFO)
        if not agency_info:
            return []

        # Try to get rates from main agency configuration
        rates_by_words = agency_info.get('rates by words in agency') or agency_info.get('rates_by_words')
        if rates_by_words:
            return rates_by_words.split("|")
            
        # If not found, check remote login agencies
        remote_login_agencies = agency_info.get('remote login agencies')
        if not remote_login_agencies:
            return []
        
        # Get rates from remote agency configuration
        agency_info_remote = get_hotel_web_config_item(remote_login_agencies, AGENCY_INFO)
        if not agency_info_remote:
            return []
            
        rates_by_words = agency_info_remote.get('rates by words in agency') or agency_info_remote.get('rates_by_words')
        if not rates_by_words:
            return []
            
        return rates_by_words.split("|")
        
    except Exception as e:
        logging.error(f"Error filtering agency rates: {str(e)}")
        return []

@manager_cache.managers_cache(
    hotel_code_provider=lambda f, a, k: a[0],
    ttl_seconds=3600,
    only_thread_local_and_memory=True,
    entities='ConfigurationProperty,WebConfiguration'
)
def find_forbidden_club_rates(hotel_code: str, rate_keys: List[str]) -> List[Dict]:
    """
    Determines which club rates should be hidden for the current user.
    
    This function handles several cases:
    1. Club not active: All club rates are hidden
    2. Club active but user not logged in and hide_all_rates configured: All club rates are hidden
    3. Club active and user not logged in and only_levels configured: Only level rates are hidden
    4. Club active and user logged in: Rates are filtered according to user's level
    
    Args:
        hotel_code: Hotel application ID
        rate_keys: List of rate keys to check
        
    Returns:
        List of dictionaries containing rate information and visibility status
    """
    try:
        # Initialize variables
        level = None
        hotel = get_hotel_by_application_id(hotel_code)
        
        # Get configuration values
        rates_login_user_club = get_hotel_advance_config_value(hotel_code, RATES_LOGIN_USERS_CLUB)
        club_enabled = club_is_active(hotel, rates_login_user_club)
        rates_login_hidden = get_hotel_advance_config_value(hotel_code, RATES_LOGIN_HIDDEN)
        user_levels_config = get_hotel_advance_config_value(hotel_code, RATES_LOGIN_USERS)
        
        # Get all club rates, excluding promotions
        all_club_rates = [
            x.strip().lower()
            for x in get_all_club_rates(hotel_code, user_levels_config)
            if 'promotion=' not in x
        ]

        # Determine forbidden rates based on club configuration
        forbidden_rates = []
        if club_enabled:
            forbidden_rates, level = separate_allowed_forbidden_club_rates(
                hotel, all_club_rates, user_levels_config, rates_login_hidden
            )
        
        # If club is not active or hide_all_rates is configured, hide all club rates
        if not club_is_active(hotel, rates_login_user_club) or (
            rates_login_hidden and rates_login_hidden != ONLY_LEVELS
        ):
            logging.info('Filtering club rates because club is not active or user is not logged')
            forbidden_rates = all_club_rates

        logging.info('Forbidden rates:')
        logging.info(forbidden_rates)

        # Get agency rates to filter
        filtered_rates_agency = filter_rates_agencies(hotel)
        
        # Build final list of rates with visibility information
        filtered_rates = build_rate_visibility_list(
            hotel, rate_keys, forbidden_rates, level, rates_login_hidden, filtered_rates_agency, all_club_rates
        )
        
        return filtered_rates
        
    except ValueError as ve:
        logging.error(f"Validation error: {str(ve)}")
        return []
    except Exception as e:
        logging.error(f"Error finding forbidden club rates: {str(e)}")
        return []