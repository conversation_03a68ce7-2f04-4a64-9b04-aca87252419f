import time
import boto3
import logging

from paraty_commons_3.mail.amazon_constants import SERVICE_AWS, CHARSET, DEFAULT_BCC, REGION_NAME, AWS_SECRET_ACCESS_KEY, AWS_ACCESS_KEY_ID, COUNT_RETRY, DOMAIN_NOT_VERIFIED, D<PERSON>AIN_VERIFIED, TIME_STEP


class AmazonEmail:

    access_key = None
    secret_key = None
    region = None
    client = None
    sender = None

    def __init__(self, access_key=AWS_ACCESS_KEY_ID, secret_key=AWS_SECRET_ACCESS_KEY, region=REGION_NAME):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region

        self.client = boto3.client(
            SERVICE_AWS,
            region_name=self.region,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key
        )

    def set_sender(self, sender):
        self.sender = sender

    def send(self, address, title, contentText, contentHtml, cc=[], bcc=DEFAULT_BCC):

        try:

            logging.warning("Trying to send email using Amazon....")

            to_addresses = address.split(";")
            to_addresses = [email.strip() for email in to_addresses]

            response = self.client.send_email(
                Source=self.sender,
                Destination={
                    'ToAddresses': to_addresses,
                    'CcAddresses': cc,
                    'BccAddresses': bcc
                },
                Message={
                    'Subject': {
                        'Charset': CHARSET,
                        'Data': title
                    },
                    'Body': {
                        'Html': {
                            'Charset': CHARSET,
                            'Data': contentHtml,
                        },
                        'Text': {
                            'Charset': CHARSET,
                            'Data': contentText
                        }
                    }
                }
            )

            logging.info("Send Email Response: ", response)
            logging.info("Email sent successfully!")

        except Exception as e:

            logging.warning("Error sending email using Amazon to " + str(address))
            logging.error(e)

            return "KO"

        return "OK"

    def check_domain(self, domain):

        message = DOMAIN_NOT_VERIFIED

        try:
            retry = 1
            while retry <= COUNT_RETRY:
                response = self.client.get_identity_verification_attributes(
                    Identities=[domain]
                )
                verification_status = response['VerificationAttributes'][domain]['VerificationStatus']
                if verification_status == 'Success':
                    message = DOMAIN_VERIFIED
                    break
                else:
                    time.sleep(TIME_STEP)
                    retry += 1

        except Exception as e:
            logging.error(e)

        return message
