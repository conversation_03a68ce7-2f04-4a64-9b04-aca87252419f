import os

import flask
from jinja2 import Environment, FileSystemLoader, select_autoescape


def register_apidocs_route(app, file_element):
    app.add_url_rule('/apidocs_file_element', 'API Docs File', lambda: _return_file_openapi(file_element), methods=['GET'])
    app.add_url_rule('/apidocs', 'API Docs', _render_docs_html, methods=['GET'])


def _return_file_openapi(file_element):
    return flask.send_file(file_element)


def _render_docs_html():
    return _render_template("index.html", {})


def _render_template(template_name, context_data):
    actual_absolute_path = os.path.dirname(os.path.abspath(__file__))
    template_folder = os.path.join(actual_absolute_path, 'templates')

    env = Environment(
        loader=FileSystemLoader(template_folder),
        autoescape=select_autoescape(['html', 'xml'])
    )

    template = env.get_template(template_name)
    html = template.render(**context_data)
    return html
