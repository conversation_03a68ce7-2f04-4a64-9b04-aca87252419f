import io
import json
import logging
import traceback
import requests

try:
    from google.auth import default
except:
    logging.info("you must add google-auth to requirements")

BUG_SEEKER_SERVICE = 'https://europe-west1-assistant-seeker.cloudfunctions.net/notify_kpi'
BUG_SEEKER_SERVICE_HEADER = {"content-type": "application/json"}
BUG_SEEKER_SERVICE_TIMEOUT = 3

class BugSeekerDepartments:
    Account = 'Account'
    AccountOwner = 'AccountOwner'
    Design = 'Design'
    Backend = 'Backend'
    SEM = 'SEM'
    QA = 'QA'
    Analytics = 'Analytics'
    Engineering = 'Engineering'
    LatamAccounts = 'LATAM Accounts'


def _send_notification_to_bug_seeker(title, message, project_id, subject='', hotel_code='', departments=[], category=None):
    try:
        data_to_send = {
            'code': '',
            'hotel_code': hotel_code,
            'application_id': project_id,
            'departments': departments,
            'metadata': {
                'title': title,
                'category': category,
                'subject': subject if subject else title,
                'content': message
            }
        }
        try:
            requests.post(BUG_SEEKER_SERVICE, headers=BUG_SEEKER_SERVICE_HEADER, data=json.dumps(data_to_send), timeout=BUG_SEEKER_SERVICE_TIMEOUT)
        except Exception as e:
            # Due to timeout issues we dont expect to have a response, and will control errors in assistant-seeker 
            logging.info(f"Error _send_notification_to_bug_seeker: {e}")

    except Exception as e:
        logging.error(e)


def send_notification_to_bug_seeker(title, message, subject='', hotel_code='', departments=[], category=None):
    credentials, project_id = default()
    _send_notification_to_bug_seeker(title, message, project_id, subject=subject, hotel_code=hotel_code, departments=departments, category=category)


def send_exception_notification_to_bug_seeker(title, exception, subject='', hotel_code='', departments=[], category=None):
    credentials, project_id = default()
    output_buffer = io.StringIO()
    traceback.print_exception(type(exception), exception, exception.__traceback__, file=output_buffer)
    message = output_buffer.getvalue()

    _send_notification_to_bug_seeker(title, message, project_id, subject=subject, hotel_code=hotel_code, departments=departments, category=category)
