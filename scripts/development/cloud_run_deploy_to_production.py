import inspect
import subprocess
from paraty.config import Config
import main

PROJECT = Config.PROJECT
GCLOUD_COMMAND = 'gcloud beta'

REGION = Config.LOCATION

CUSTOM_DEPLOY_PARAMS = {
	'pdf-handlers': {
		"memory": "4Gi",
		"concurrency": "15"
	}
}

def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def deploy_flex():
	cloud_run_deployments = ['pdf-handlers']

	# See https://cloud.google.com/sdk/gcloud/reference/functions/deploy
	for cloud_run_name in cloud_run_deployments:
		memory = '512Mi'
		concurrency = '15'

		if cloud_run_name in CUSTOM_DEPLOY_PARAMS:
			memory = CUSTOM_DEPLOY_PARAMS[cloud_run_name].get('memory', memory)
			concurrency = CUSTOM_DEPLOY_PARAMS[cloud_run_name].get('concurrency', concurrency)

		cache_command = "gcloud config set builds/use_kaniko True"
		execute_command(cache_command)

		source_path = f"../../src/cloud_run_projects/{cloud_run_name}"
		image_path = f"gcr.io/{Config.PROJECT}/{cloud_run_name}"
		image_builder_script = f"gcloud builds submit --project {Config.PROJECT} --tag {image_path} {source_path}"
		execute_command(image_builder_script)

		deploy_command = f"{GCLOUD_COMMAND} run deploy {cloud_run_name} --project {Config.PROJECT} --memory {memory} --cpu 1 --concurrency {concurrency} --image {image_path} --region {REGION}"
		execute_command(deploy_command)


if __name__ == '__main__':
	deploy_flex()
