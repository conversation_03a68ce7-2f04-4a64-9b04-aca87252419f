import inspect
import subprocess
from paraty.config import Config
import main

PROJECT = Config.PROJECT
TIMEOUT = '540'
GCLOUD_COMMAND = 'gcloud'

REGION = Config.LOCATION

CUSTOM_FUNCTIONS_TIMEOUTS = {
	'send_survey_to_all_hotels': '3000',
	'execute_populate_function': '3000',
}


PARAMS_PER_FUNCTION = {
	'get_hotel_location_prefix': {
		"gen2": True,
		"memory": "512MB",
		"timeout": "30",
		"concurrency": "50",
		"min_instances": "1"
	},
	'get_all_from_entity': {
		"gen2": True,
		"memory": "1024MB",
		"timeout": "30",
		"concurrency": "30",
		"cloud_function_name": "get_all_from_entity2",
		"max_concurrency": "70",
		"max_instances": "10"
	},
	'get_web_page_properties_for_language': {
		"gen2": True,
		"memory": "1536MB",
		"timeout": "100",
		"concurrency": "20",
	},
	'backup_wikis_at_gcs_handler': {
		'service-account': '<EMAIL>'
	},
	"read_session_from_hotel_handler": {
		"gen2": True,
		'service-account': '<EMAIL>'
	},
	"write_session_from_hotel_handler": {
		"gen2": True,
		'service-account': '<EMAIL>'
	},
	'list_tasks_in_queue': {
		"gen2": False,
		"memory": "256MB",
		"timeout": "30",
		"concurrency": "40",
	},
	'paraty_add_reservation_agency_task': {
		"gen2": True,
		"memory": "1536MB",
		"concurrency": "20",
	},
	'paraty_add_reservation_agency': {
		"gen2": True,
		"memory": "1536MB",
		"concurrency": "20",
	},
	'hotel_backup':{
		"gen2": False,
		"memory": "4096MB",
		"timeout": "540",
		"concurrency": "5",
		"max_instances": "5"
	},
	'check_active_bookings': {
		"memory": "2048MB",
	},
	'execute_populate_function': {
		"gen2": True,
		"memory": "4096MB",
		"timeout": "3000",
		"concurrency": "5",
		"max_instances": "5"
	},
	'execute_create_hotel': {
		"gen2": False,
		"memory": "512MB",
		"timeout": "540",
		"concurrency": "5",
		"max_instances": "5"
	},
	'slack_notify': {
		"gen2": False,
		"memory": "512MB",
		"timeout": "60",
		"concurrency": "5",
		"max_instances": "1"
	},
	'get_total_accommodation_tax': {
		"gen2": True,
		"memory": "512MB",
		"timeout": "30",
		"concurrency": "40",
		"max_instances": "5"
	},
	'get_if_users_logged_in_callcenter': {
		"gen2": True,
		"memory": "4096MB",
		"timeout": "3000",
		"concurrency": "40",
		"max_instances": "5"
	},
	'get_new_combination': {
		"gen2": True,
		"memory": "1024MB",
		"timeout": "30",
		"concurrency": "40",
	},
	'cancel_reservations_expired_link_task': {
		"gen2": True,
		"memory": "512MB",
	},
	'get_confirmation_link_encrypt': {
		"gen2": True,
		"memory": "512MB",
	},
	'generate_calendar_files_handler': {
		"gen2": True,
		"memory": "512MB",
	},

}


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def deploy_flex():

	# functions_to_expose = [x[0] for x in inspect.getmembers(main, inspect.isfunction)]

	# In case we want just to deploy one of them
	# functions_to_expose = ['list_tasks_in_queue']
	functions_to_expose = ['get_confirmation_link_encrypt']

	# See https://cloud.google.com/sdk/gcloud/reference/functions/deploy
	for function_name in functions_to_expose:

		cloud_function_name = function_name

		target_timeout = CUSTOM_FUNCTIONS_TIMEOUTS.get(function_name, TIMEOUT)
		memory = '512MB'
		need_2_gen = '--no-gen2'
		concurrency = '15'
		max_instances = '5'
		min_instances = '0'

		if float(target_timeout) > 540:
			cloud_function_name = function_name.replace('_', '-')
			need_2_gen = '--gen2'

		# Not all functions might have the same configuration
		# If not clear, check cloud functions in production to see currenct configuration
		params = {}
		if function_name in PARAMS_PER_FUNCTION:
			params = PARAMS_PER_FUNCTION[function_name]
			if params.get("gen2"):
				need_2_gen = f'--gen2 --set-env-vars ENABLE_SETUP_LOGGING=True'
			if params.get("memory"):
				memory = params.get("memory")
			if params.get("timeout"):
				target_timeout = params.get("timeout")
			if params.get("concurrency"):
				concurrency = params.get("concurrency")
			if params.get("cloud_function_name"):
				cloud_function_name = params.get("cloud_function_name")
			if params.get("max_instances"):
				max_instances = params.get("max_instances")
			if params.get("min_instances"):
				min_instances = params.get("min_instances")

		command = f'cd ../../src;{GCLOUD_COMMAND} functions deploy {cloud_function_name} {need_2_gen} --cpu=1 --memory={memory} --min-instances {min_instances} --max-instances {max_instances} --entry-point {function_name} --trigger-http --allow-unauthenticated --project {PROJECT} --runtime python310 --timeout {target_timeout} --region {REGION} --concurrency={concurrency}'

		if params.get('service-account'):
			command += f' --service-account {params.get("service-account")}'

		execute_command(command)


if __name__ == '__main__':
	deploy_flex()
