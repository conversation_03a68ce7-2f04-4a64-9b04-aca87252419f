import base64
import os
import sys
import traceback
import logging
import html

#  In case POSTMARKAPP is installed in the project we use the library, otherwise we use the local version
try:
	from postmark.core import PMMail
except ImportError:
	from paraty_commons_3.libs.postmark.core import PMMail

from paraty_commons_3.decorators.cache.timebased_cache import timed_cache

EMAILS_SUPPORT = "<EMAIL>"

app_name = 'Paraty Tech'

DEFAULT_TITLE = '[%s] Problem at integration Adapter - ' % app_name


def makeTraceback():
	"""
	returns a string with the full traceback
	"""
	excinfo = sys.exc_info()
	message = ('Application: %s\nVersion: %s\n\n%s\n\n'
		% (os.getenv('APPLICATION_ID'),
		os.getenv('CURRENT_VERSION_ID'),
		'<br/>'.join(traceback.format_exception(*excinfo)),))

	return message


def notify_error_by_email(subject, message, emails_to_add=None):

	try:
		from paraty import Config
		if Config.DEV:
			# Don't send emails in DEV
			return
	except:
		# Better to send emails in dev than to break anything
		pass

	emails = EMAILS_SUPPORT
	if emails_to_add:
		emails += emails_to_add
	try:
		sendEmail(emails, subject, "", message)
	except:
		sendEmail_backup(emails, DEFAULT_TITLE, "", message)


@timed_cache(key_builder=lambda x: "notify_exception_by_email %s" % x[1], minutes=5)
def notifyExceptionByEmail(myException, myRequest):

	message = makeTraceback()
	emailContent = "<html><body><B>Request: %s </B><br/><br/>%s</body><html/>" % (html.escape(str(myRequest))[:2000], str(message))
	try:
		logging.warning("Unexpected Exception" + str(myException))
		sendEmail(EMAILS_SUPPORT, DEFAULT_TITLE, "", emailContent)
	except:
		sendEmail_backup(EMAILS_SUPPORT, DEFAULT_TITLE, "", emailContent)


def sendEmail(address, title, contentText, contentHtml, sender=None, attachments=None, send_default_bcc=True,
              key_ppm='70f0042b-fc70-43cf-a01c-86b783a31327'):
	sendEmail_backup(address, title, contentText, contentHtml, sender=sender, attachments=attachments,
	                 send_default_bcc=send_default_bcc, key_ppm=key_ppm)

def notifyMappingSeekerMissed(hotel, city, site):
	try :
		logging.warning("[Mapping Seeker] Mapping not found in inventory for hotel %s in %s and site %s " % (hotel, city, site))
		emailContent = "<html><body>Mapping not found in inventory for hotel <b>%s</b> in <b>%s</b> and site <b>%s</b> <br/></body><html/>" % (hotel, city, site)
		sendEmail("<EMAIL>", '[Mapping Seeker] Missing hotel in inventory', "", emailContent)
	except:
		pass


@timed_cache(key_builder=lambda x: "notify_email_to_backup_team %s" % x[1], minutes=2)
def notify_email_to_backup_team(content_html, title):
	my_trace = makeTraceback()
	sendEmail_backup("<EMAIL>", title, "", "<p>%s</p><p>%s</p>" % (content_html, my_trace))


def send_email_postmarkapp(address, subject, content_html, postmark_api, sender=None, content_text='', reply_to='', bcc=''):

	logging.warning("Trying to send it using PMM....")
	if not sender:
		sender = app_name + " <<EMAIL>>"

	to = ""
	for myAddress in address.split(";"):
		to = to + myAddress + ","

	message = PMMail(api_key=postmark_api,
					 sender=sender,
					 subject=subject,
					 html_body=content_html,
					 text_body=content_html,
					 to=to,
					 reply_to=reply_to)

	# For the moment I will be backup, in the future this will be moved to a specific project
	message.bcc = '<EMAIL>'

	message.send()
	logging.info("Sent!")


def sendEmail_backup(address, title, contentText, contentHtml, replyTo=None, sender=None, attachments=None, bcc=None,
                     send_default_bcc=True, key_ppm='70f0042b-fc70-43cf-a01c-86b783a31327'):
	try:
		logging.warning("Trying to send it using PMM....")
		if not sender:
			sender = app_name + " <<EMAIL>>"
		to = ""
		for myAddress in address.split(";"):
			to = to + myAddress + ","
		message = PMMail(api_key=key_ppm,
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 reply_to=replyTo)

		if send_default_bcc:
			message.bcc = '<EMAIL>,<EMAIL>,<EMAIL>'

		if bcc:
			message.bcc = bcc

		if attachments:
			logging.info("attachments found!!. Num: %s", len(attachments))
			message.attachments = attachments

		message.send()
		logging.info("Sent!")
	except Exception as e:
		logging.warning("Error sending email using PMM to " + str(address))
		logging.warning(e)
		logging.warning("trying without sender: %s", sender)

		sender = app_name + " <<EMAIL>>"

		message = PMMail(api_key=key_ppm,
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 bcc='<EMAIL>,<EMAIL>,<EMAIL>',
						 reply_to=replyTo)

		if attachments:
			message.attachments = attachments

		message.send()
		logging.warning("Sent!")

def sendEmail_with_bcc(address, title, contentText, contentHtml, bcc, replyTo=None, sender=None, attachments=None,
                       key_ppm='70f0042b-fc70-43cf-a01c-86b783a31327', cc=None):
	try:
		logging.warning("Trying to send it using PMM....")
		if not sender:
			sender = app_name + " <<EMAIL>>"
		to = ""
		for myAddress in address.split(";"):
			to = to + myAddress + ","

		message = PMMail(api_key=key_ppm,
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
		                 bcc=bcc,
		                 cc=cc,
						 reply_to=replyTo)

		if attachments:
			logging.info("attachments found!!. Num: %s", len(attachments))
			message.attachments = attachments

		message.send()
		logging.info("Sent!")
	except Exception as e:
		logging.warning("Error sending email using PMM to " + str(address))
		logging.warning(e)
		logging.warning("trying without sender: %s", sender)

		sender = app_name + " <<EMAIL>>"

		message = PMMail(api_key=key_ppm,
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 bcc='<EMAIL>, <EMAIL>',
						 reply_to=replyTo)

		if attachments:
			message.attachments = attachments

		message.send()
		logging.warning("Sent!")

def build_email_sender_from_hotel_webs_config(email_sender_config):
	if "-" in email_sender_config:
		reply_email = email_sender_config.split("-")
		email_sender = u" %s <%s>" % (reply_email[0], reply_email[1])
	else:
		email_sender = u" %s <%s>" % (email_sender_config, '<EMAIL>')

	return email_sender