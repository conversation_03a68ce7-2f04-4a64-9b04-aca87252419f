import gzip
import json
import logging
import os
import sys
import traceback

import google.cloud.logging
client = google.cloud.logging.Client()
client.setup_logging()

from flask import Flask, request

from utils.pdf_html_utils import generate_pdf_content_from_html
from utils.pdf_fill_utils import generate_pdf_filled_and_callback_url, fill_from_pdf

app = Flask(__name__)


@app.route("/html_to_pdf", methods=["POST"])
def html_to_pdf_handler():
    if request.headers.get("Content-Encoding") == "gzip":
        compressed_data = request.data
        decompressed_data = gzip.decompress(compressed_data).decode('utf-8')
        json_code = json.loads(decompressed_data)
        html_code = json_code.get("html_code")
    else:
        html_code = request.values.get("html_code")

    return generate_pdf_content_from_html(html_code)


@app.route("/pdf_fill_callback", methods=["POST"])
def pdf_fill_with_callback_handler():
    """
    The purpose of this endpoints is to fill a pdf file with some variables and return the filled pdf file.
    After the PDF is ready will be sent to the endpoint specified in the request.
    """
    return generate_pdf_filled_and_callback_url()


@app.route("/pdf_fill", methods=["POST"])
def pdf_fill_handler():
    """
    The purpose of this endpoint is to fill a pdf file with some variables and return the filled pdf file.
    """
    pdf_data, pdf_headers = fill_from_pdf()
    return pdf_data, 200, pdf_headers


@app.errorhandler(Exception)
def handle_exception(e):
    title = 'Error generating PDF on pdf-handlers'

    excinfo = sys.exc_info()
    message = ('Application: %s\nVersion: %s\n\nTRACE BACK: %s\n\n'
               % (os.getenv('APPLICATION_ID'),
                  os.getenv('CURRENT_VERSION_ID'),
                  '<br/>'.join(traceback.format_exception(*excinfo)),))

    logging.error(message)
    # send_notification_to_bug_seeker(title, message, subject=title)
    return str(e), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)
