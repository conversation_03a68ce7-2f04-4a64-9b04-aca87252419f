import logging
import tempfile

import requests
from fillpdf import fillpdfs
from flask import request


def fill_from_pdf():
    logging.info('Filling PDF with variables')
    post_data = request.json

    pdf_source = post_data.get('pdf_source')
    pdf_variables = post_data.get('json_dict')

    logging.info(f'PDF source: {pdf_source}')
    logging.info(f'PDF variables: {pdf_variables}')

    if not pdf_source or not pdf_variables:
        return ''

    pdf_content = _retrieve_pdf_element(pdf_source)

    with tempfile.TemporaryDirectory() as temp_dir:
        pdf_output_file = temp_dir + '/output.pdf'
        base_pdf_path = temp_dir + '/pdf_base.pdf'
        with open(base_pdf_path, 'wb+') as f:
            f.write(pdf_content)

        data = fillpdfs.get_form_fields(base_pdf_path)
        if data:
            for key in data:
                data[key] = pdf_variables.get(key, '')

            fillpdfs.write_fillable_pdf(base_pdf_path, pdf_output_file, pdf_variables, flatten=False)
            fillpdfs.flatten_pdf(pdf_output_file, pdf_output_file, True)
            pdf_data = open(pdf_output_file, 'rb')

        else:
            pdf_data = open(base_pdf_path, 'rb')

    pdf_headers = {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename=joined_pdf.pdf',
        'Content-Transfer-Encoding': 'binary'
    }

    return pdf_data, pdf_headers


def generate_pdf_filled_and_callback_url():
    logging.info('Generating PDF filled and callback URL')

    post_data = request.json
    callback_url = post_data.get('callback_url')

    pdf_source = post_data.get('pdf_source')
    pdf_variables = post_data.get('json_dict')

    logging.info(f'PDF source: {pdf_source}')
    logging.info(f'PDF variables: {pdf_variables}')
    logging.info(f'Callback URL: {callback_url}')

    if not pdf_source or not pdf_variables:
        return 'Missing required fields', 400

    pdf_data, pdf_headers = fill_from_pdf()
    requests.post(callback_url, pdf_data, headers=pdf_headers)

    return 'Success', 200


def _retrieve_pdf_element(pdf_source):
    response = requests.get(pdf_source)
    return response.content
