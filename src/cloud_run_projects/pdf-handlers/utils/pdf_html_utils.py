import os
import tempfile
import img2pdf
from selenium import webdriver

def generate_pdf_content_from_html(html_content):
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html') as f:
        f.write(html_content)
        f.seek(0)
        html_file_path = f.name

        options = webdriver.ChromeOptions()
        options.add_argument("--disable-notifications")
        options.add_argument('--no-sandbox')
        options.add_argument('--headless')

        actual_absolute_path = os.path.dirname(os.path.abspath(__file__))
        chrome_driver_path = os.path.join(actual_absolute_path, '../drivers/chromedriver')
        driver = webdriver.Chrome(chrome_driver_path, options=options)

        driver.get(f'file://{html_file_path}')

        width = driver.execute_script("return document.body.scrollWidth")
        height = driver.execute_script("return document.body.scrollHeight")
        if height < 1000:
            height = 1000
        driver.set_window_size(width, height)

        with tempfile.NamedTemporaryFile() as screenshot_file:
            driver.save_screenshot(screenshot_file.name)
            screenshot_file.seek(0)
            pdf_bytes = img2pdf.convert(screenshot_file.name)
            driver.close()
            return pdf_bytes


if __name__ == '__main__':
    pdf_content = generate_pdf_content_from_html('Hola mundo')
