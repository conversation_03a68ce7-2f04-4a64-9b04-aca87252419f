# Use the official Python image.
# https://hub.docker.com/_/python
FROM python:3.11

# Install manually all the missing libraries
RUN apt-get update
RUN apt-get install -y gconf-service libasound2 libatk1.0-0 libcairo2 libcups2 libfontconfig1 libgdk-pixbuf2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libxss1 fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils poppler-utils

# Copy local code to the container image.
ENV APP_HOME /app
WORKDIR $APP_HOME
COPY . .

RUN wget https://storage.googleapis.com/chrome-for-testing-public/131.0.6778.85/linux64/chromedriver-linux64.zip -P /tmp
RUN unzip /tmp/chromedriver-linux64.zip -d /tmp
RUN mv /tmp/chromedriver-linux64/chromedriver /app/drivers/

# Install Chrome
RUN wget https://storage.googleapis.com/chrome-for-testing-public/126.0.6423.0/linux64/chrome-linux64.zip
RUN unzip chrome-linux64.zip
RUN cp chrome-linux64.zip /tmp/

RUN mkdir -p /opt/google/chrome
RUN mv chrome-linux64/* /opt/google/chrome/

RUN ln -s /opt/google/chrome/chrome /usr/bin/google-chrome

RUN chmod -R 755 /opt/google/chrome

# Install Python dependencies.
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# Run the web service on container startup. Here we use the gunicorn
# webserver, with one worker process and 8 threads.
# For environments with multiple CPU cores, increase the number of workers
# to be equal to the cores available.
CMD exec gunicorn --bind :$PORT --workers 1 --threads 4 main:app