import urllib
from unittest.mock import  MagicMock

import flask
import pytest

from main import queue_url_call
from paraty.general.general_utils import create_file_at_storage
from paraty.websUtils.web_utils import build_friendly_url
import requests

@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_queue_url_call(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'hotel-puentereal',
            'target_url': 'https://www.puentereal.com/test.html?test_param=True',

        }
        queue_url_call(flask.request)


def test_create_file_at_storage(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.get_json = lambda: {
            'full_file_path': 'test/test.txt',
            'bucket': 'dev_unit_tests',
            'content': 'test',
            'content_type': 'text/plain'
        }
        create_file_at_storage(flask.request)



def _test_production(app):
    with app.test_request_context():

        url = 'https://europe-west1-build-tools-2.cloudfunctions.net/queue_url_call'

        target_url = "http://spinach-dot-admin-hotel3.appspot.com/booking1?language=SPANISH&agesKid1=&agesKid2=&agesKid3=&original_referer=https%3A%2F%2Fwww.granpalashotel.com%2Fbooking1%3Fsid%3De2fe842b-2379-4fbb-8f57-28a9f22428c1%26_ga%3D1808190826.1643753926&roomType=&roomTypeValue=&roomFilter=.*&roomFilterName=&roomOrder=&roomtype_list_json=&namespace=granpalas-experience&gclid=&priceSeekerHotel=&startDate=11%2F05%2F2022&endDate=15%2F05%2F2022&numRooms=1&adultsRoom1=2&childrenRoom1=0&babiesRoom1=0&name=2&name=2&name=2&name=2&name=2&name=2&adultsRoom2=2&childrenRoom2=0&babiesRoom2=0&name=2&name=2&name=2&name=2&name=2&name=2&adultsRoom3=2&childrenRoom3=0&babiesRoom3=0&name=2&name=2&name=2&name=2&name=2&name=2&_ga=148740917.1643798443&promocode=&_ga=148740917.1643798443&booking-search-popup=true&source=Web&application_id=e~secure-booking21&sid=85eb4b39-1cfa-41f6-910e-635e1c060108&warm_request=True&ignoreStats=True"

        target_url = urllib.parse.quote(target_url.encode('utf8'))

        url += "?hotel_code=%s&target_url=%s" % ('hotel-puentereal', target_url)

        result = requests.get(url)

        print(result)
