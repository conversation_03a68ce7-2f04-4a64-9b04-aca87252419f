import unittest
import base64
from unittest.mock import patch, MagicMock
from src.paraty.bookings.calendar_generator import generate_calendar_files
# Asumimos que la función de envío de email se podría usar así:
from src.paraty_commons_3.email_utils import sendEmail


class TestCalendarGenerator(unittest.TestCase):

    def setUp(self):
        """Set up test data for all tests."""
        self.test_data = {
            "identifier": "86484021",
            "customer_name": "<PERSON>",
            "check_in": "2025-10-20",
            "check_out": "2025-10-25",
            "hotel_name": "Hotel Paraíso Test",
            "hotel_address": "Calle Falsa 123, Ciudad Ejemplo",
            "hotel_code": "test-backend3"
        }

    def test_generate_calendar_files_success(self):
        """
        Tests that calendar files are generated successfully with valid data.
        """
        result = generate_calendar_files(self.test_data)

        self.assertIn("ics_base64", result)
        self.assertIn("google_calendar_url", result)
        self.assertIsNotNone(result["ics_base64"])
        self.assertIsNotNone(result["google_calendar_url"])

        # Decode the base64 content and check for key information
        decoded_ics = base64.b64decode(result["ics_base64"]).decode('utf-8')
        self.assertIn("SUMMARY:Booking at Hotel Paraíso Test", decoded_ics)
        self.assertIn("DESCRIPTION:Booking for Elena BL. Reservation No: 86484021", decoded_ics)

    def test_generate_calendar_files_missing_fields(self):
        """
        Tests that an exception is raised when required fields are missing.
        """
        test_data = {
            "identifier": "86484021",
            "customer_name": "Elena BL",
            # check_in is missing
            "check_out": "2025-10-25",
            "hotel_name": "Hotel Paraíso Test",
            "hotel_address": "Calle Falsa 123, Ciudad Ejemplo",
            "hotel_code": "test-backend3"
        }

        with self.assertRaises(Exception) as context:
            generate_calendar_files(test_data)

        self.assertTrue("The following required fields are missing: check_in" in str(context.exception))

    @patch('src.paraty_commons_3.email_utils.sendEmail')
    def test_send_calendar_email_integration(self, mock_sendEmail: MagicMock):
        """
        Tests the integration of generating and sending the calendar file via email.
        """
        # 1. Generate calendar data
        calendar_files = generate_calendar_files(self.test_data)
        ics_base64 = calendar_files["ics_base64"]

        # 2. Prepare attachment and email content
        attachment = [
            ("reserva.ics", ics_base64, "text/calendar")
        ]
        recipient_email = "<EMAIL>"
        subject = f"Booking Confirmation {self.test_data['identifier']}"
        html_body = "<h1>Test Email</h1>"

        # 3. Call the email function
        sendEmail(
            address=recipient_email,
            title=subject,
            contentHtml=html_body,
            contentText="Test email",
            attachments=attachment
        )

        # 4. Assert that sendEmail was called correctly
        mock_sendEmail.assert_called_once()
        args, kwargs = mock_sendEmail.call_args
        self.assertEqual(kwargs.get("address"), recipient_email)
        self.assertEqual(kwargs.get("title"), subject)
        self.assertEqual(kwargs.get("attachments"), attachment)


if __name__ == '__main__':
    unittest.main()