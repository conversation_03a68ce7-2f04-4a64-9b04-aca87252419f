import flask
import pytest

from unittest.mock import MagicMock
from main import get_utm_sources


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_get_utm_source_my_business(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'identifiers': '27589947',
            'hotel_code': 'parkroyal-mazatlan',
        }
        result = get_utm_sources(flask.request)
        assert result == '{"27589947": "google my business"}'

