
from unittest.mock import  MagicMock

import flask
import pytest
from paraty.websUtils.web_utils import build_friendly_url


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_build_friendly_url(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'name': 'sección de niños',
            'extension': '.html',
            'language': 'SPANISH'

        }
        url = build_friendly_url(flask.request)
        assert url == "/es/seccion-de-ninos.html"