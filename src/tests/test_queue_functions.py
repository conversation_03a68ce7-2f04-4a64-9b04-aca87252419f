from unittest.mock import MagicMock

import flask
import pytest

from paraty.queues.task_queue_common_functions import list_tasks_in_queue


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_list_tasks_in_queue(app):

    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'project': 'siteminder-adapter',
            'location': 'europe-west1',
            'queue_name': 'processor5'

        }
        result = list_tasks_in_queue(flask.request)
    print(result)
