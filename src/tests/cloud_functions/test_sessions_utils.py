import logging
from unittest.mock import patch

from paraty.session.session_utils import read_session_hotel, write_session_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from tests.test_base import TestBase


class TestSessionsEndpoint(TestBase):
    def test_read_session_from_hotel(self):
        demo_sessions = get_using_entity_and_params('UserSession', [], hotel_code='demo5', limit=1)
        target_sid = demo_sessions[0].key.name

        with self.app.test_request_context(f'/read_session_hotel', method='GET'):
            session = read_session_hotel(target_sid, 'demo5')
            logging.info(session)
            self.assertIsNotNone(session)


    @patch('paraty.session.session_utils._read_session_from_redis')
    def test_write_session_from_hotel(self, *args):
        target_hotel_code = 'demo5'

        demo_sessions = get_using_entity_and_params('UserSession', [], hotel_code=target_hotel_code, limit=1)
        target_sid = demo_sessions[0].key.name

        args[0].return_value = None

        with self.app.test_request_context(f'/write_session_hotel?source=unit-test', method='POST'):
            session = write_session_hotel(target_sid, target_hotel_code, {'test': 'test'})
            logging.info(session)
            self.assertIsNotNone(session)
