import unittest
from unittest.mock import MagicMock
from main import get_total_accommodation_tax
from tests.test_base import TestBase


class TestGetTotalAccommodationTax(TestBase):
    def test_mock_request_get_json(self):
        with self.app.test_request_context():
            mock_request = MagicMock()
            mock_request.get_json.return_value = PARKROYAL_COZUMEL

            result = get_total_accommodation_tax(mock_request)

            self.assertEqual(result, PARKROYAL_COZUMEL_TAX)
            mock_request.get_json.assert_called_once()



PARKROYAL_COZUMEL_TAX = 4.58
PARKROYAL_COZUMEL = {
    "hotel_code": "parkroyal-cozumel",
    "prices_total": 1006.6,
    "current_result": {
        "num_adults": 2,
        "num_nights": 3,
        "currency": "EUR",
        "start_date": "2025-01-13"
    },
    "amount_after_taxes": 1006.6,
    "country_location_custom_tax": 1.14,
    # "tourism_tax_optional": 2.0
}
SMY_ARAN_BLU_TAX = 45
SMY_ARAN_BLU = {
    "hotel_code": "smy-aran-blu",
    "prices_total": 318.06,
    "current_result": {
        "num_adults": 2,
        "num_nights": 3,
        "currency": "EUR",
        "start_date": "2025-01-13"
    },
    "amount_after_taxes": 318.06,
    # "country_location_custom_tax": 1.14,
    # "tourism_tax_optional": 0.0
}
if __name__ == "__main__":
    unittest.main()
