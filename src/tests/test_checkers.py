from paraty.checkers import identity_checks
from paraty.checkers.html_checks import check_html_handler
from tests.test_base import TestBase


identity_tests = [
    ("AT", [["000247681888", True], ["1233edd221d", False], ["********", True]]),  # AUSTRIA
    ("BE", [["70.01.16-287.31", True], ["1233edd221d", False], ["BE123456", True]]),  # B<PERSON><PERSON><PERSON>
    ("BG", [["7608010133", True], ["1233edd221d", False], ["123456789", True]]),  # BUL<PERSON>RI<PERSON>
    ("CH", [["CHE123456789", True], ["1233edd221d", False], ["********", True]]),  # SWITZERLAND
    ("CN", [["330302199808180355", True], ["1233edd221d", False], ["*********", True]]),  # <PERSON>IN<PERSON>
    ("DE", [["261083-C-20917", True], ["1233edd221d", False], ["C3JJ4789L", True]]),  # GERMANY
    ("ES", [["99999999R", True], ["Y9991867Z", True], ["32082084M", True], ["1233edd221d", False], ["*********", True]]),  # SPAIN
    ("FI", [["101052-719E", True], ["1233edd221d", False], ["*********", True]]),  # FINLAND
    ("FR", [["151024610204", True], ["1233edd221d", False], ["78TH67845", True]]),  # FRANCE
    ("GB", [["*********", True], ["1233edd221d", False], ["*********", True]]),  # UNITED KINGDOM
    ("GR", [["P 000007", True], ["1233edd221d", False], ["*********", True]]),  # GREECE
    ("HU", [["1 651105 6666", True], ["1233edd221d", False], ["AB123456", True]]),  # HUNGARY
    ("IE", [["1234567A", True], ["1233edd221d", False], ["1234567A", True]]),  # IRELAND
    ("IT", [["77491246Z", True], ["1233edd221d", False], ["*********", True]]),  # ITALY
    ("NL", [["987654321", True], ["1233edd221d", False], ["*********", True]]),  # NETHERLANDS
    ("PO", [["ABC1234567", True], ["1233edd221d", False], ["*********", True]]),  # POLAND
    ("PT", [["25534512", True], ["1233edd221d", False], ["A123456", True]]),  # PORTUGAL
    ("RO", [["2121212121218", True], ["1233edd221d", False], ["123456789", True]]),  # ROMANIA
    ("SE", [["610321-3499", True], ["1233edd221d", False], ["25534512", True]]),  # SWEDEN
    ("SL", [["2902966500017", True], ["1233edd221d", False], ["*********", True]]),  # SLOVANIA
    ("US", [["123456789", True], ["1233edd221d", False], ["123456789", True]])  # UNITED STATES
]


class TestCheckers(TestBase):
    def test_all_checkers(self):
        data = {'html': '<div></div>'}
        results = check_html_handler(self.create_request(data))
        self.assertEqual(results['status'], 'ok')

        data = {'html': '<div>'}
        results = check_html_handler(self.create_request(data))
        self.assertEqual(results['status'], 'error')

    def test_identity_checkers(self):
        for test in identity_tests:
            country = test[0]
            for country_test in test[1]:
                result = identity_checks.validate_identifier(country, country_test[0])
                self.assertEqual(result, country_test[1])
