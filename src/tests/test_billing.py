from unittest.mock import patch, MagicMock

import flask
import pytest
from google.cloud import storage

from paraty.backups.backup_utils import save_multiple_in_zip_file
from paraty.billing.billing_constants import BILLING_BUCKET
from paraty.billing.monthly_billing import default_billing_for_previous_month, download_bill, download_to_storage, zip_and_send_bills
from paraty_commons_3.storage.storage_utils import get_all_files_in_bucket


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)

@patch('paraty_commons_3.queue_utils.create_task_with_url_get_target')
def test_monthly_billing_task_creationg_happy_flow(mock1, app):

    with app.test_request_context():
        default_billing_for_previous_month(flask.request)
        print("Produced %s tasks" % mock1.call_count)
        assert mock1.call_count > 500


def test_zip_and_send_bills(app):
    with app.test_request_context():
        zip_and_send_bills(flask.request)


def test_download_bill(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'startDate': '2020-08-01',
            'endDate': '2020-08-31',
            'hotelId': '4001',
            'path': 'callcenter',
            'departure': 'False',
            'source': 'Callcenter'
        }
        download_bill(flask.request)


def test_download_to_storage():

    TEST_URL = 'https://admin-hotel.appspot.com/MainPage/excel?entryStartDate=2019-05-01&entryEndDate=2019-05-31&hotelId=4001'
    download_to_storage(TEST_URL, BILLING_BUCKET, 'test/hotel_puentereal.xls')

    TEST_URL = 'https://admin-hotel.appspot.com/MainPage/excel?entryStartDate=2019-05-01&entryEndDate=2019-05-31&hotelId=11001'
    download_to_storage(TEST_URL, BILLING_BUCKET, 'test/hotel_zen.xls')

def test_zip_storage_folder():

    blobs = get_all_files_in_bucket(BILLING_BUCKET)

    print()
    print('Objects:')

    multiple_files = []
    for blob in blobs:
        multiple_files.append((blob.name, blob.download_as_bytes()))

    billing_zip_url = save_multiple_in_zip_file(BILLING_BUCKET, 'bills/bills.zip', multiple_files)

    print(billing_zip_url)

    #https://storage.cloud.google.com/paraty_billing/test/bills.zip

    #Get the link and send it by email

