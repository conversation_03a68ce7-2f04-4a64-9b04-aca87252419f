
from unittest.mock import patch, MagicMock

import flask
import pytest

from paraty.backups.backup_constants import DEVELOPMENT_BUCKET
from paraty.backups.backup_utils import backup_entities
from paraty.build.build_utils import get_web_update_configuration
from paraty.development.development_utils import get_latest_file, get_all_from_entity, AUTHENTICATION
from paraty_commons_3.storage.storage_utils import get_all_files_in_bucket


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_save_file_in_development_bucket():
    backup_entities(DEVELOPMENT_BUCKET, 'datastore_data', 'best-corporate', ['RoomType', 'WebPageProperty', 'Picture'], date_format="latest")


def test_get_latest_file():
    result = get_latest_file('best-corporate', 'WebPageProperty')
    pass

def test_get_file_in_bucket():
    all_files = get_all_files_in_bucket(DEVELOPMENT_BUCKET)
    hotel_code = 'best-corporate'

    valid_files = list(filter(lambda x: x.name.split("/")[1] == hotel_code, all_files))

    for file in all_files:
        hotel_code = file.name.split("/")[1]
        timestamp = file.updated

        a = file.download_as_bytes().decode('utf8')
        pass


def test_get_all_entities(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'hotel-puentereal',
            'entity_name': 'Rate',

        }
        flask.request.headers = {
            'authentication': AUTHENTICATION
        }
        result = get_all_from_entity(flask.request)

def test_generic_backup(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'pio-apartments',
            'configuration': 'Custom Upload Info',

        }
        get_web_update_configuration(flask.request)