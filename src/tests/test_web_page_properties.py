from unittest.mock import MagicMock

import flask

from main import refresh_web_page_properties_for_language, get_web_page_properties_for_language
from paraty.webPageProperties.web_page_properties_utils import clean_web_page_properties_for_hotel, \
    delete_web_page_property_for_hotel
from tests.test_base import TestBase

TEST_HOTEL_CODE = "test-backend6"
TEST_LANGUAGE = "TEST_LANGUAGE"
WEB_PAGE_PROPERTY_RESULT = {
    'result': [
        {
            'timestamp': 2693811090000,
            'entityKey': 'entityKey',
            'name': None,
            'value': 'value',
            'title': 'title',
            'type': 'type',
            'description': None,
            'helpText': None,
            'mainKey': 'mainKey',
            'legacy_key': 'legacy_key',
            'languageKey': 'TEST_LANGUAGE',
            'id': 5125853904109568,
            'kind': 'WebPageProperty',
            'project': 'integration-test-hotel',
            'namespace': 'test-backend6'
        }
    ]
}


class TestWebPageProperties(TestBase):
    def test_refresh(self):
        flask.request = MagicMock()
        flask.request.args = {
            'hotel_code': TEST_HOTEL_CODE,
            'language': TEST_LANGUAGE
        }

        refresh_web_page_properties_for_language(flask.request)

    def test_get(self):
        flask.request = MagicMock()
        flask.request.args = {
            'hotel_code': TEST_HOTEL_CODE,
            'language': TEST_LANGUAGE
        }

        result = get_web_page_properties_for_language(flask.request)
        assert result == WEB_PAGE_PROPERTY_RESULT

    def test_clean_web_page_properties_for_hotel(self):
        flask.request = MagicMock()
        flask.request.args = {
            'hotel_code': TEST_HOTEL_CODE
        }

        clean_web_page_properties_for_hotel(flask.request)

    def test_delete_web_page_property_for_hotel(self):
        flask.request = MagicMock()
        flask.request.args = {
            'hotel_code': TEST_HOTEL_CODE,
            'ids': "5125853904109568,5125853904109568",
        }

        delete_web_page_property_for_hotel(flask.request)
