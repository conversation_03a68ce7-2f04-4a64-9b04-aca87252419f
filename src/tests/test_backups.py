import random
import string
import zlib
from unittest.mock import patch, MagicMock

import flask
import pytest

import paraty
from paraty.backups.backup_utils import entities_generic_backup
from paraty.backups.hotels import hotel_backup
from paraty.backups.web_seeker import web_seeker_backup
from paraty.development import populate_datastore_utils
from paraty.development.development_utils import get_all_from_entity, AUTHENTICATION
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.security_utils import encrypt_random_text, decrypt_random_text


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_zip_encrypt_and_back(app):
    TEST_STRING = '{hola mundo}'
    compressed_text = str(zlib.compress(TEST_STRING.encode("utf-8")))
    encrypted_text = encrypt_random_text('test-hotel', compressed_text)
    print(encrypted_text)
    decrypted_compressed_text = decrypt_random_text('test-hotel', encrypted_text)
    final_bytes = eval(decrypted_compressed_text)
    final_text = zlib.decompress(final_bytes).decode('utf-8')

    assert final_text == TEST_STRING


def _get_random_text(length):
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))


def test_encrypt_decrypt_very_long_string(app):
    TEST_STRING = _get_random_text(25000000)
    compressed_text = str(zlib.compress(TEST_STRING.encode("utf-8")))
    encrypted_text = encrypt_random_text('test-hotel', compressed_text)
    print(encrypted_text)
    decrypted_compressed_text = decrypt_random_text('test-hotel', encrypted_text)
    final_bytes = eval(decrypted_compressed_text)
    final_text = zlib.decompress(final_bytes).decode('utf-8')

    assert final_text == TEST_STRING


@patch('paraty_commons_3.google_functions_utils.functions_utils.create_task_for_each_hotel')
def test_backup_web_seeker_happy_flow(mock1, app):
    with app.test_request_context():
        web_seeker_backup.execute_webseeker_backup(flask.request)
        assert mock1.call_count == 1


def test_generic_backup(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'ramblas-barcelona',
            'entities': 'Wysiwyg',
            'global_path': 'web_seeker'
        }
        entities_generic_backup(flask.request)


def test_get_all_from_entity(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'hotel-puentereal',
            'entity_name': 'RoomType',
        }

        flask.request.headers = {'Authorization': AUTHENTICATION}

        get_all_from_entity(flask.request)


def test_backup_hotel(app):
    with app.test_request_context():
        hotel_backup.execute_hotels_backup(flask.request)


def test_execute_function(app):
    with app.test_request_context():
        flask.request = MagicMock()
        # flask.request.values = {"typeFunction": "hotel_backup", "source": 4694659359309824, "target": 0, "from_storage": "", "backup": "", "webseekers": ""}

        # flask.request.values = {
        #         "typeFunction": "only_web_copy",
        #         "source": 6243883481890816,
        #         "target": 6356136759721984,
        #         "from_storage": "",
        #         "backup": False,
        #         "webseekers": True,
        #         "add_reservations": False,
        #         "checkDominio": True,
        #         "add_advanced_configurations": True
        #     }

        flask.request.values = {"typeFunction": "only_web_copy", "source": 6220533130395648, "target": 4967547614855168, "from_storage": "", "backup": True, "webseekers": True, "add_reservations": False, "checkDominio": True, "add_advanced_configurations": True}




        populate_datastore_utils.execute_funcion(flask.request)


#

def test_execute_create(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {"name": "TEST: Backend10", "comision": "10%", "production": True, "application": "integration-test-hotel",
                                "hotelCode": "test-backend10", "enable": True, "multi": True}

        populate_datastore_utils.create_hotel(flask.request)


def test_execute_dig(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {"dominio": "puentereal.com"}
        # with app.execute_function():
        populate_datastore_utils.execute_dig(flask.request)


def test_parse_to_agency():
    entities = datastore_communicator.get_using_entity_and_params('Agencies', [('is_company', '=', 'true')], hotel_code='porthotels:port-corpo')
    entities.count()
    for item in entities:
        print(f'Agency is {item}')
