from unittest.mock import MagicMock

import flask
import pytest

from paraty.tools.calendars.get_calendar_utils_breakdown import get_calendar_results_breakdown


@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)


def test_calendars_breakdown(app):
    with app.test_request_context():
        flask.request = MagicMock()
        flask.request.values = {
            'hotel_code': 'blaumar-blaumar',
            'month': '7',
            'year': '2023',
            'device': 'Web',
            'country_code': 'ES',
            'adults': '2',
            'children': '0',
            'babies': '0',
        }

        results = get_calendar_results_breakdown(flask.request)
        print(results)