import json


class PersonalDetailsEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, PersonalDetails):
            return obj.__dict__
        return super().default(obj)

class PersonalDetails(object):
    def __init__(self):
        self.prefix = ''
        self.personalId = ''
        self.name = ''
        self.lastName1 = ''
        self.lastName2 = ''
        self.country = ''
        self.province = ''
        self.postalCode = ''
        self.city = ''
        self.address = ''
        self.id = ''
        self.email = ''
        self.telephone = ''
        self.comments = ''
        self.birthday = ''
        self.password = ''
        self.allow_notifications = False
        self.user_club_allow_register = False
        self.share_info_group_chain = ""
        self.accept_conditions_and_policies = ""
        self.billing_name = ''
        self.billing_cif = ''
        self.billing_address = ''
        self.hour_flight = ''
        self.chk_hour_flight = ''
        self.extra_fields = {}

    def from_json(self, data):
        for data_key in data:
            if hasattr(self, data_key):
                setattr(self, data_key, data[data_key])

    def __getitem__(self, item):
        if hasattr(self, item):
            return getattr(self, item)
        else:
            return None

    def get(self, key, default=None):
        if hasattr(self, key):
            return getattr(self, key)
        else:
            return default

    def __serialize__(self):
        return self.__dict__