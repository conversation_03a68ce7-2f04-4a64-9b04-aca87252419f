import inspect

from flask import Flask, request

import main

# Init flask app
app = Flask(__name__)

def execute_func():
    actual_path = request.path
    target_function = actual_path.replace('/', '')
    target_function = getattr(main, target_function)
    return target_function(request)

if __name__ == '__main__':
    functions_to_expose = [x[0] for x in inspect.getmembers(main, inspect.isfunction)]
    for function_name in functions_to_expose:
        app.add_url_rule(f'/{function_name}', function_name, view_func=execute_func)

    app.run(debug=True)
