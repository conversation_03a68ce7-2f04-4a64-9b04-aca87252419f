import datetime
import logging

from paraty_commons_3 import queue_utils
from paraty_commons_3.storage import storage_utils

ASYNC_CALLS_QUEUE = 'url-async-calls'


def queue_url_call(request):
    target_url = request.values.get("target_url")
    hotel_code = request.values.get("hotel_code")
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    logging.info("target_url: %s", target_url)

    queue_utils.create_task_with_url_get_target(ASYNC_CALLS_QUEUE, target_url, name='%s_%s' % (hotel_code, timestamp), in_seconds=10)

    return 'OK'


def create_file_at_storage(request):

    data = request.get_json()
    full_file_path = data.get('full_file_path')
    bucket_name = data.get('bucket')
    content = data.get('content')
    content_type = data.get('content_type')

    storage_utils.save_text_file(bucket_name, full_file_path, content, content_type=content_type)

    return f'File accessible at https://storage.cloud.google.com/{bucket_name}/{full_file_path}'
