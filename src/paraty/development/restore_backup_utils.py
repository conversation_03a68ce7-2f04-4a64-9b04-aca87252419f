import io
import json
import zipfile

from paraty.backups.backup_constants import BACKUP_BUCKET
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import flush_entity_cache
from paraty_commons_3.storage.storage_utils import get_blob_in_path


def _build_path(hotel_code, backup_date_forder, entity_name, try_with_zip=False):
    # TODO, dinamically find the folder date and zip

    extension = "json"
    if try_with_zip:
        extension = "zip"

    path = 'hotels/%s/%s/%s.%s' % (hotel_code, backup_date_forder, entity_name, extension)
    return path


def restore_wpp_for_main_key(hotel_code, date, main_keys):
    entities = get_last_backup(hotel_code, date, 'WebPageProperty')

    valid_entities = []
    for entity in entities:
        if entity['mainKey'] in main_keys:
            valid_entities.append(entity)

    print(valid_entities)

    for entity in valid_entities:
        current_key = datastore_utils.alphanumeric_to_key(entity['key'])
        current_entity = datastore_communicator.get_entity_by_key(current_key, hotel_code)
        if not current_entity:
            entity.pop('key', None)
            datastore_communicator.save_to_datastore('WebPageProperty', current_key.id, entity, hotel_code)
        else:
            pass

    flush_entity_cache(hotel_code, 'WebPageProperty')


def get_last_backup(hotel_code, backup_date_forder, entity_name):
    bucket_name = BACKUP_BUCKET

    path = _build_path(hotel_code, backup_date_forder, entity_name)
    blob = get_blob_in_path(path, bucket_name)

    if not blob:
        path = _build_path(hotel_code, backup_date_forder, entity_name, try_with_zip=True)
        blob = get_blob_in_path(path, bucket_name)

    if blob:
        result = blob.download_as_bytes()

        if ".zip" in path:
            z = zipfile.ZipFile(io.BytesIO(result))
            result_as_bytes = z.read('%s.json' % entity_name)
        else:
            result_as_bytes = result
        if result_as_bytes:
            try:
                return json.loads(result_as_bytes.decode("utf8"))
            except:
                # iE here a re reservations because they are encrypted.
                # TODO: unecrypted reservations
                return []
        else:
            print()

    return []


if __name__ == '__main__':
    # Vacio
    # restore_wpp_for_main_key('casual-musica-valencia', '2023-09-17-07-12-13', ['rateName', 'rateDescription'])
    restore_wpp_for_main_key('casual-musica-valencia', '2023-06-25-07-04-01', ['rateName', 'rateDescription'])

# result = get_last_backup('casas-arenal', '2021-10-05', 'ConfigurationProperty')
# print(result)
