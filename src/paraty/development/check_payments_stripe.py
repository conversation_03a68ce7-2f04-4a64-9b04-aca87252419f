import datetime
import json

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
   get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels
import stripe

from paraty_commons_3.logging.my_gae_logging import logging

TIMESTAMP = "2023-03-01 00:00:00"
TO_TIMESTAMP = "2023-12-31 00:00:00"


def get_paid_reservations(hotel_code=None, hotel_name_filter=None, only_this_identifier=None, days=1, find_only_less_paid=False):
   from datetime import datetime, timedelta

   if only_this_hotel:
      hotel_codes = [only_this_hotel]
   else:
      hotel_codes = [hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter] if hotel_name_filter else ([hotel_code] if hotel_code else get_all_hotels())


   pending_reservations = {}


   for hotel_code in hotel_codes:

      if "demo" in hotel_code.lower():
         continue

      pending_reservations[hotel_code] = []
      try:
         pep_paylinks = list(get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code,return_cursor=True, search_params=[('name', '=', 'PEP_PAYLINKS COBRADOR')]))[0]
      except:
         logging.info(f'Hotel {hotel_code} no tiene configurado PEP_PAYLINKS COBRADOR')
         pep_paylinks = {}

      if pep_paylinks:

         res_search_param = [('timestamp', '>', (str(datetime.now() - timedelta(days=days))).split(' ')[0])]
         if only_this_identifier:
            res_search_param = [('identifier', '=', only_this_identifier)]


         reservations = list(get_using_entity_and_params('Reservation', hotel_code=hotel_code, return_cursor=True, search_params=res_search_param)) or logging.info(f'No hay reservas en {hotel_code} en los últimos {days} días') or []
         for reservation in reservations:

            if reservation.get("identifier") in EXCLUDED_RESERVATIONS:
               continue

            today = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            payed_by_tpv_link = json.loads(reservation.get('extraInfo', '')).get('payed_by_tpv_link')
            amount_payed = 0
            if payed_by_tpv_link:
               amount_payed = payed_by_tpv_link[0].get("amount")

            reservation_startDate = get_fixed_startDate(reservation)
            reservation_timestamp = reservation.get('timestamp')
            #and ((reservation_timestamp < today) or (reservation_startDate < today))

            if amount_payed and not reservation.get('cancelled'):
               pending_reservations[hotel_code].append(reservation.get('identifier'))
   return {key: value for key, value in pending_reservations.items() if value}




def configure_stripe_conection(hotels, hotel_code):
   integration_config = get_integration_configuration_of_hotel(hotels[hotel_code], "STRIPE COBRADOR")
   if integration_config:
      integration_config = integration_config[0]
      config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
      private_key = config.get("private_key")

      # client = stripe.http_client.RequestsClient()
      # stripe.default_http_client = client

      stripe.api_key = private_key
      stripe.api_version = "2020-08-27"

      return True
   return False


def check_if_reservation_is_bad_paid(reservation, hotel, fix_reservation_only_more_paid):
   hotels = get_all_hotels()
   stripe_configured_ok = configure_stripe_conection(hotels, hotel)
   if stripe_configured_ok:
      reservations = get_reservations_of_hotel(hotels[hotel], TIMESTAMP, TO_TIMESTAMP, reservation_id=reservation, include_end_date=True)
      result_data = []

      also_check_this_identifier = None

      for reservation in reservations:
         identifier = reservation.get("identifier")
         num_rooms= reservation.get("numRooms")
         if "-" in identifier:
            also_check_this_identifier = identifier
            identifier = identifier.split("-")[-1]
         try:
            extra_info = json.loads(reservation.get("extraInfo", "{}"))
         except:
            extra_info = {}

         paid_by_link = 0
         if extra_info.get("payed_by_tpv_link"):
            payed_by_tpv_link = extra_info.get("payed_by_tpv_link")
            if payed_by_tpv_link:
               for paid_line in payed_by_tpv_link:
                  paid_by_link += paid_line.get("amount")


         #nacho2 en lugar de preguntar por el identificador sacamos el ID  de stripe del extaInfo
         stripe_id_in_res = extra_info.get("stripe_customer_id")

         #customer = stripe.Customer.search(query="name:'%s'" % identifier)
         #customer = stripe.Customer.retrieve(stripe_id_in_res)

         pago_proceso = float(extra_info.get("payed", "0"))
         pagos_cobrador = float(extra_info.get("payed_by_cobrador", "0"))
         # TODO NACHO2: quitar los paxgos manuales!!  RD01E36D0F (oasishoteles-grandcancun)
         # tienes que recorrer todo los pagso de payment seeker e ir restadno los cobros amanuales y restarlos de 'pagos_cobrador'

         # TODO NACHO2: quitar los pagos en proceso que ha metido el hotel a mano!!! R42389078 (oasishoteles-grandcancun)
         # mirar en el histórico si ha si el hotel quien ha tocoado el "payed" del extra info, si ha sido el hotel
         # si se ha modificado a mano, restsrlo del 'pago_proceso'



         #nacho2: sumar al pagado_total el payed y los pagos programdos
         pagado_total = paid_by_link + pago_proceso + pagos_cobrador

         if pagado_total:
            import_all = 0
            #for cus in customer.get("data", []):

            #payment_methods = stripe.PaymentMethod.list(customer=cus.stripe_id)
            #nacho2: PONER UN LIMITE, sino sólo nos traemos los 10 primeros por fefecto que pueden estar incompletos,
            payments = stripe.PaymentIntent.search(query="customer:'%s'" % stripe_id_in_res, limit=100)

            for payment in payments.get("data", []):
               if payment.status == "succeeded":
                  #nacho2: mejor leer amount_received en lugar de amount a secas
                  import_all += (int(payment.get("amount_received")) / 100)

            if also_check_this_identifier:

               origin_hotel_code = extra_info.get("origin_hotel_code")
               if origin_hotel_code:
                  #TODO: maybe is posible that we need to read again stripe_id_in_res from the original reservation

                  stripe_configured_ok = configure_stripe_conection(hotels, origin_hotel_code)
                  if stripe_configured_ok:
                     payments = stripe.PaymentIntent.search(query="customer:'%s'" % stripe_id_in_res, limit=100)

                     for payment in payments.get("data", []):
                        if payment.status == "succeeded":
                           # nacho2: mejor leer amount_received en lugar de amount a secas
                           import_all += (int(payment.get("amount_received")) / 100)

                  # return the conection to the real hotel
                  stripe_configured_ok = configure_stripe_conection(hotels, hotel)


            import_all_to_check = int(import_all)
            pagado_total_to_check = int(pagado_total)

            check_if = not import_all_to_check == pagado_total_to_check
            if find_only_less_paid:
               check_if = import_all_to_check < pagado_total_to_check

            if check_if:
               total_reserva = float(reservation.get("price")) + float(reservation.get("priceSupplements", 0))

               print(f"ERROR EN LA RESERVA: {identifier} PAGOS STRIPE: {str(import_all)} PAGOS EN RESERVA: {str(paid_by_link)}")

               if import_all < pagado_total:
                  tipo_error = "PAGADO FALSO"
               else:
                  tipo_error = "PAGO NO GUARDADO?"


               if reservation.get("modificationTimestamp"):
                  tipo_error = "MODIFICADA " + tipo_error


               total_reserva_tocheck = int(total_reserva)

               secure_fix = True
               #TODO: comentar esta linea cuando queramos arreglar todo
               secure_fix = total_reserva_tocheck == import_all_to_check

               if fix_reservation_only_more_paid and secure_fix and import_all_to_check > pagado_total_to_check:
                  from datetime import datetime, timedelta
                  print(f"{identifier} Pagos stripe: {import_all} - Pagos en reserva: {pagado_total} - Precio total reserva {total_reserva}")

                  import_to_add = round(import_all - pagado_total, 2)
                  print(f"Precio igual a lo pagado en strip")
                  print(extra_info.get("payed_by_tpv_link", {}))
                  if not extra_info.get('payed_by_tpv_link'):
                     extra_info['payed_by_tpv_link'] = []

                  new_payed_by_tpv_link = {'order': reservation.get('identifier'), 'amount': import_to_add,
                                           'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                           'fix_manual_added': True, "amount": import_to_add, "fix_manual_added": True}

                  extra_info["payed_by_tpv_link"].append(new_payed_by_tpv_link)
                  print(extra_info.get("payed_by_tpv_link", {}))

                  reservation["extraInfo"] = json.dumps(extra_info)
                  print(f'Reserva arreglada {reservation.get("identifier")} añadido {import_to_add}')




                  # ids = [reservation.key.id]
                  # resertacions_to_save = [reservation]
                  # new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids,
                  #                                                                 resertacions_to_save,
                  #                                                                 hotel_code=hotel)


               res_error = {"hotel_code": hotel,
                            "identifier": reservation.get("identifier"),
                            "Pagado total STRIPE": import_all,
                            "Pagado total reserva": pagado_total,
                            "pagado proceso": pago_proceso,
                            "paid_by_link": paid_by_link,
                            "pagado cobrador": pagos_cobrador,
                            "Creada": reservation.get("timestamp"),
                            "Entrada": reservation.get("startDate"),
                            "Total Reserva": total_reserva,
                            "Error": tipo_error}
               if reservation.get("modificationTimestamp"):
                  res_error["Modificada"] = reservation.get("modificationTimestamp")
               return res_error





def get_fixed_timestamp(reservation, expire_hours_link):
   from datetime import datetime, timedelta
   return str(datetime.strptime(reservation.get('timestamp', 0), "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link)) or reservation.get('timestamp', 0)


def get_fixed_startDate(reservation):
   from datetime import datetime, timedelta
   return str(datetime.strptime(reservation.get('startDate'), '%Y-%m-%d') + timedelta(days=1))



def check_payments_reservations(reservations_with_payments, fix_reservation_only_more_paid):
   bad_reservations = {}
   for hotel_code, reservations in reservations_with_payments.items():
      if hotel_code not in bad_reservations:
         bad_reservations[hotel_code] = []
      for reservation in reservations:
         x = check_if_reservation_is_bad_paid(reservation, hotel_code, fix_reservation_only_more_paid)
         if x:
            bad_reservations[hotel_code].append(x)
   return bad_reservations


# checkea todas las reservas de todos los hoteles con pep paylinks de reservas de hace x dias y si se han pagado comprueba el importe contra stripe


EXCLUDED_RESERVATIONS = ["M-82860723", "M-R68872119", "R41267842", "R68873850", "M-R49816854", "R62010173", "R62010173",
                         "M-42287349", "R40884761", "R89774428", 'R80136216', 'R34301E7F4', 'R4E9136579', 'RD01E36D0F',
                         "98902471", "R42389078", "R16342141", "R34185289"]





only_this_hotel = None
only_this_hotel = "oasishoteles-grandcancun"

only_this_identifier = None
only_this_identifier="98329472"

fix_reservation_only_more_paid = True
days = 100

# name_filter = None
name_filter = 'oasis'

find_only_less_paid = False

paid_reservations = get_paid_reservations(hotel_code=only_this_hotel, hotel_name_filter=name_filter, only_this_identifier=only_this_identifier, days=days, find_only_less_paid=find_only_less_paid)
res = check_payments_reservations(paid_reservations, fix_reservation_only_more_paid)
print(res)

