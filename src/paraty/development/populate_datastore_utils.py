import time

from paraty.backups.hotels.hotel_backup import execute_hotel_backup
from paraty.development.restore_backup_utils import get_last_backup
from paraty.webPageProperties.web_page_properties_utils import do_clean_web_page_properties_for_hotel

from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.managers_cache.cache_entry_refresher import refresh_entity_timestamps
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_all_valid_hotels, get_location_profix_of_applicationId
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.email_utils import send_email_postmarkapp, sendEmail_backup
import subprocess
from google.cloud import datastore
import requests

LEGACY_KEY_FIELD = 'legacy_key'

BOOKING_ENTITIES_WITH_PICTURES = ['RoomType', 'Promotion', 'PriceIncrease']
ENTITIES_IGNORE = ['GiftBono', 'Reservation', 'Consent', 'ExternalIdentifiers', 'BookingHtml', 'Booking1Html', 'cache', 'pageentry', 'usersession', 'userentry', 'sessionmodel', 'errorswebmodel', 'refreshstatus', 'logbackup', 'UserClub','ServiceExternal', 'Newsletter', 'PaymentConfiguration', 'PaymentsReservation',  'PaymentRulesHistory']
BOOKING_ENTITIES_TO_RESTORE = ['RatePeriod','RoomType', 'Rate', 'RateCondition', 'Regimen', 'Promotion', 'IntegrationConfiguration']

BOOKING_ENTITIES_WITH_WPP = ['RoomType', 'Rate', 'Regimen', 'RateCondition', 'Supplement', 'MultiRate', 'News', 'Promotion', 'PriceIncrease']
ONLYWEB_ENTITIES_WITH_WPP = ['Picture', 'WebSection', 'News']

ENTITIES_FOR_WEBSEEKERS = ['WysiwygHistory', 'Wysiwyg', 'WysiwygStyles']

EXTRA_ENTITIES_ONLY_WEB_TO_IGNORE = ['Reservation', 'UserClub', 'Supplement', 'FinalPriceDay', 'priceIncrease', 'priceTimer', 'redirection', 'webconfiguration',
                                     'Restriction', 'YieldModification', 'SatisfactionForm', 'GiftBono', 'Newsletter', 'PaymentConfiguration', 'PaymentsReservation',  'PaymentRulesHistory']
ENTITIES_IGNORE_ONLY_WEB = ENTITIES_IGNORE + BOOKING_ENTITIES_TO_RESTORE + EXTRA_ENTITIES_ONLY_WEB_TO_IGNORE

ALL_ENTITIES_BY_DEFAULT_IN_BACKUP = ['Agencies', 'ConfigurationProperty', 'Consent', 'Currency', 'IntegrationConfiguration', 'MarketingLogo',
            'MultiRate', 'News', 'Picture', 'PriceIncrease', 'Promocode', 'Promotion', 'Rate', 'RateCondition',
            'RatePeriod', 'Redirection', 'Regimen', 'RoomType', 'RoomTypeStatus', 'Supplement',
           'WebConfiguration', 'WebSection', 'Wysiwyg', 'WysiwygHistory', 'WebPageProperty', 'GiftBono', 'WysiwygStyles', 'Newsletter', 'PaymentConfiguration', 'PaymentRulesHistory', 'PaymentsReservation']


def _get_all_entities(hotel_code):
    project, namespace = hotel_manager_utils.get_hotel_project_and_namespace(hotel_code)
    new_client = datastore.Client(project=project, namespace=namespace)
    query = new_client.query(kind='__kind__')
    query.keys_only()
    kinds = [entity.key.id_or_name for entity in query.fetch()]
    return kinds

def _get_kinds_to_save(hotel_code, only_web=False, add_advanced_configurations=True, add_reservations=True, webseekers=True, add_news=True):
    all_kinds = _get_all_entities(hotel_code)
    valid_kinds = []

    if only_web:
        entities_to_ignore = ENTITIES_IGNORE_ONLY_WEB
    else:
        entities_to_ignore = ENTITIES_IGNORE

    if not add_advanced_configurations:
        entities_to_ignore += ['ConfigurationProperty']

    if not add_reservations:
        entities_to_ignore += ['Reservation']

    if not webseekers:
        entities_to_ignore += ENTITIES_FOR_WEBSEEKERS

    if not add_news:
        entities_to_ignore += ['News']

    for kind in all_kinds:
        to_ignore = False

        for content_to_ignore in entities_to_ignore:
            if content_to_ignore.lower() in kind.lower() or kind.startswith("_"):
                to_ignore = True
                break
        if not to_ignore:
            valid_kinds.append(kind)

    return valid_kinds


def _remove_entity(hotel_code, entity_name, wpp_only_web=False, main_keys_to_keep=[]):
    logging.info("Removing %s from %s", entity_name, hotel_code)

    if wpp_only_web or main_keys_to_keep:
        entities_to_remove = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=False, hotel_code=hotel_code)
        list_of_keys = []
        for entity in entities_to_remove:

            if main_keys_to_keep:
                # removing Picture!
                condition_to_remove = not (entity.get("mainKey") in main_keys_to_keep)
            else:
                # removing WPP! ->Only remove WPP about pics and WebSection becasue we will restore from source hotel
                condition_to_remove = "picture" in entity.get("mainKey", "") or "WebSection" in entity.get("mainKey", "")

            if condition_to_remove:
                list_of_keys.append(entity.key)
    else:
        entities_to_remove = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=True, hotel_code=hotel_code)
        list_of_keys = [x.key for x in entities_to_remove]

    logging.info("Entities to remove: %s", len(list_of_keys))
    datastore_communicator.delete_entity_multi(list_of_keys, hotel_code=hotel_code)

    logging.info("Flushing entity cache")
    hotel_manager_utils.flush_entity_cache(hotel_code, entity_name)

    logging.info("Entity %s removed!!!!", entity_name)


def _remove_migrated_entity(hotel_code, entity_name, wpp_only_web=False):
    logging.info("Removing migrated entity %s from %s", entity_name, hotel_code)
    entities_to_remove = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=False, hotel_code=hotel_code)
    list_of_keys = []
    for current_entity in entities_to_remove:
        wpp_condition_ok = (not wpp_only_web) or ("picture" in current_entity.get("mainKey", "") or "WebSection" in current_entity.get("mainKey", ""))
        if wpp_condition_ok and current_entity.get(LEGACY_KEY_FIELD):
            list_of_keys.append(current_entity.key)

    logging.info("Entities to remove: %s", len(list_of_keys))
    datastore_communicator.delete_entity_multi(list_of_keys, hotel_code=hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, entity_name)

def _get_utc_now():
    return int(time.time() * 1000)
def _restore_entity(source, target, entity_name, from_storage="", enties_with_wpp_id=[], dominio_asociado=""):
    logging.info("Restoring %s from %s to %s", entity_name, source, target)

    # First we remove already migrated entities to make sure this is idempotent
    _remove_migrated_entity(target, entity_name)

    if from_storage:
        backup_date_forder = from_storage
        entities_to_add = get_last_backup(source, backup_date_forder, entity_name)
    else:
        entities_to_add = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=False, hotel_code=source)

    new_ids_generated = []
    origin_ids_added = []

    new_entities = []

    for current_entity in entities_to_add:
        new_entity = dict(current_entity)
        if current_entity.get("mainKey", "") and ("Dominio asociado" in current_entity.get("mainKey", "")) and dominio_asociado:
            new_entity["value"] = dominio_asociado
        # for security reaseons
        pic_or_section_condition = current_entity.get("mainKey", "") and ("picture" in current_entity.get("mainKey", "") or "WebSection" in current_entity.get("mainKey", ""))
        specific_main_key_condition = new_entity.get("entityKey") in enties_with_wpp_id
        wpp_condition_ok = (not enties_with_wpp_id) or pic_or_section_condition or specific_main_key_condition
        if wpp_condition_ok:
            if datastore_communicator.is_hotel_datastore(target):
                if from_storage:
                    new_entity[LEGACY_KEY_FIELD] = current_entity.get("key")
                else:
                    new_entity[LEGACY_KEY_FIELD] = datastore_utils.id_to_entity_key(source, current_entity.key)


            new_entity['timestamp'] = _get_utc_now()

            new_entities.append(new_entity)
            origin_ids_added.append(new_entity[LEGACY_KEY_FIELD])

    logging.info("%s entities to restore: %s", entity_name, len(new_entities))

    if from_storage:
        new_ids_generated = []
        entity_ids = [x['id'] for x in new_entities]

        [x.pop('id', None) for x in new_entities]
        [x.pop('key', None) for x in new_entities]
        [x.pop('timestamp', None) for x in new_entities]

        new_ids_generated = datastore_communicator.save_multiple_entities(entity_name, entity_ids, new_entities, hotel_code=target)


    else:

        ids = [""] * len(new_entities)
        new_ids_generated = datastore_communicator.save_multiple_entities(entity_name, ids, new_entities, hotel_code=target)

    if entity_name == "Reservation":
        logging.info("Reservations saved in %s: entities to save: %s, entities generated: %s", target, len(new_entities), len(new_ids_gerated))

    hotel_manager_utils.flush_entity_cache(target, entity_name)

    return new_ids_generated, origin_ids_added


def _restore_slave_entity(source, target, slave_entity, master_entity, field_relating):
    logging.info("Restoring slave entity %s for %s", slave_entity, master_entity)

    # We have already migrated the entity_name
    entities = datastore_communicator.get_using_entity_and_params(entity_name=master_entity, keys_only=False, hotel_code=target)

    # For each we restore it's pictures
    for current_entity in entities:
        legacy_key = current_entity.get(LEGACY_KEY_FIELD)
        new_key = datastore_utils.id_to_entity_key(target, current_entity.key)
        if not legacy_key:
            continue

        related_pictures = datastore_communicator.get_using_entity_and_params(entity_name=slave_entity, search_params=[(field_relating, '=', legacy_key)],
                                                                              keys_only=False, hotel_code=source)
        new_entities = []
        for current_entity in related_pictures:
            new_entity = dict(current_entity)
            if datastore_communicator.is_hotel_datastore(target):
                new_entity[LEGACY_KEY_FIELD] = datastore_utils.id_to_entity_key(source, current_entity.key)
            new_entity[field_relating] = new_key

            new_entities.append(new_entity)

        if new_entities:
            ids = [""] * len(new_entities)

            datastore_communicator.save_multiple_entities(slave_entity, ids, new_entities, hotel_code=target)

    hotel_manager_utils.flush_entity_cache(target, slave_entity)


def _get_migrated_key_map(hotel_code, entity_name):
    # Legacy_key to current_key
    all_master_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=False, hotel_code=hotel_code)
    master_map = {x.get(LEGACY_KEY_FIELD): datastore_utils.id_to_entity_key(hotel_code, x.key) for x in all_master_entities}
    '''master_map = {}
	for x in all_master_entities:
		legacy_key = x.get(LEGACY_KEY_FIELD)
		try:
			legacy_id = datastore_utils.id_to_entity_key(hotel_code, x.key)
		except Exception as e:
			legacy_id = ""
			logging.warning(e)
			logging.warning("error in calculating legacy_key %s vs  entity key %s ", legacy_key, x.key)

		if legacy_key and legacy_id:
			master_map[legacy_key] = legacy_id
			'''

    return master_map


def fix_migrated_entity(hotel_code, slave_entity, master_entity, field_name):
    logging.info("Fixing migrated entity %s from %s", slave_entity, hotel_code)

    # Legacy_key to current_key
    master_map = _get_migrated_key_map(hotel_code, master_entity)

    slave_entities = datastore_communicator.get_using_entity_and_params(entity_name=slave_entity, keys_only=False, hotel_code=hotel_code)
    entities_to_update = []
    for current_entity in slave_entities:
        if current_entity.get(LEGACY_KEY_FIELD):

            if not current_entity.get(field_name):
                continue

            if isinstance(current_entity[field_name], list):
                newlist_of_keys = []
                for my_element in current_entity[field_name]:
                    newlist_of_keys.append(master_map.get(my_element))
                current_entity[field_name] = newlist_of_keys
            else:
                current_entity[field_name] = master_map.get(current_entity[field_name])
            entities_to_update.append(current_entity)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, slave_entity)


def fix_migrated_entity_field(hotel_code, entity_name, keys_map, field_name):
    logging.info("Fixing migrated entity field %s from %s", entity_name, hotel_code)

    slave_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=False, hotel_code=hotel_code)
    entities_to_update = []
    for current_entity in slave_entities:
        if current_entity.get(LEGACY_KEY_FIELD):
            current_entity[field_name] = keys_map.get(current_entity[field_name], current_entity[field_name])
            entities_to_update.append(current_entity)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, entity_name)


def _replace_keys_in_list(current_list, entity_map):
    result = []
    for current in current_list:
        transformed_key = current
        for k, v in entity_map.items():
            transformed_key = transformed_key.replace(k, v)

        result.append(transformed_key)

    return result


def _replace_migrated_key(bad_original_key, entity_map):
    transformed_key = bad_original_key
    if transformed_key:
        for k, v in entity_map.items():
            if k in transformed_key:
                transformed_key = transformed_key.replace(k, v)
    return transformed_key


def _fix_migrated_rate_condition(hotel_code):
    logging.info("Fixing rate_conditions at %s", hotel_code)

    rate_condition_map = _get_migrated_key_map(hotel_code, 'RateCondition')

    all_integrations = datastore_communicator.get_using_entity_and_params(entity_name='RateCondition', keys_only=False, hotel_code=hotel_code)

    entities_to_update = []

    for current_integration in all_integrations:
        if current_integration.get(LEGACY_KEY_FIELD):
            if 'cancellationPeriods' in current_integration:
                current_integration['cancellationPeriods'] = \
                    _replace_keys_in_list(current_integration['cancellationPeriods'], rate_condition_map)

            entities_to_update.append(current_integration)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, 'RateCondition')


def _fix_migrated_integration_configuration(hotel_code):
    logging.info("Fixing IntegrationConfigurations at %s", hotel_code)

    rate_map = _get_migrated_key_map(hotel_code, 'Rate')
    board_map = _get_migrated_key_map(hotel_code, 'Regimen')
    room_map = _get_migrated_key_map(hotel_code, 'RoomType')
    promotion_map = _get_migrated_key_map(hotel_code, 'Promotion')

    all_integrations = datastore_communicator.get_using_entity_and_params(entity_name='IntegrationConfiguration', keys_only=False, hotel_code=hotel_code)

    entities_to_update = []

    for current_integration in all_integrations:
        if current_integration.get(LEGACY_KEY_FIELD):
            if 'roomMap' in current_integration:
                current_integration['roomMap'] = _replace_keys_in_list(current_integration['roomMap'], room_map)

            if 'rateMap' in current_integration:
                current_integration['rateMap'] = _replace_keys_in_list(current_integration['rateMap'], rate_map)

            if 'boardMap' in current_integration:
                current_integration['boardMap'] = _replace_keys_in_list(current_integration['boardMap'], board_map)

            if 'promotionMap' in current_integration:
                current_integration['promotionMap'] = _replace_keys_in_list(current_integration['promotionMap'], promotion_map)
            entities_to_update.append(current_integration)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, 'IntegrationConfiguration')


def _fix_migrated_reservations(hotel_code):
    logging.info("Fixing Reservations at %s", hotel_code)

    rate_map = _get_migrated_key_map(hotel_code, 'Rate')
    board_map = _get_migrated_key_map(hotel_code, 'Regimen')
    room_map = _get_migrated_key_map(hotel_code, 'RoomType')
    promotion_map = _get_migrated_key_map(hotel_code, 'Promotion')
    priceIncrease_map = _get_migrated_key_map(hotel_code, 'PriceIncrease')

    all_reservations = datastore_communicator.get_using_entity_and_params(entity_name='Reservation', keys_only=False, hotel_code=hotel_code)
    logging.info("all_reservations to be fixed in %s: %s", hotel_code, len(all_reservations))

    entities_to_update = []

    # TODO: fix reservations also with shopping cart!

    for current_reservation in all_reservations:

        # in a reservation we have to fix: Room / rate / boards / promotions / priceIncrease

        if 'roomType1' in current_reservation:
            current_reservation['roomType1'] = _replace_migrated_key(current_reservation['roomType1'], room_map)

        if 'roomType2' in current_reservation:
            current_reservation['roomType2'] = _replace_migrated_key(current_reservation['roomType2'], room_map)

        if 'roomType3' in current_reservation:
            current_reservation['roomType3'] = _replace_migrated_key(current_reservation['roomType3'], room_map)

        if 'rate' in current_reservation:
            current_reservation['rate'] = _replace_migrated_key(current_reservation['rate'], rate_map)

        if 'regimen' in current_reservation:
            current_reservation['regimen'] = _replace_migrated_key(current_reservation['regimen'], board_map)

        if 'promotions' in current_reservation:
            current_reservation['promotions'] = _replace_migrated_key(current_reservation['promotions'], promotion_map)

        # TODO: check this, never tested
        if 'priceIncrease' in current_reservation:
            current_reservation['priceIncrease'] = _replace_migrated_key(current_reservation['priceIncrease'], priceIncrease_map)

        entities_to_update.append(current_reservation)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, hotel_code)

    hotel_manager_utils.flush_entity_cache(hotel_code, 'Reservation')


def hotel_full_copy_from_backup_to_different_hotel(source_hotel_code, target_hotel_code, backup_date, backup=True, add_reservations=False, webseekers=False, dominio_asociado=""):
    """
    Restaura un backup de un hotel origen a un hotel destino diferente.
    Esta función permite restaurar backups entre hoteles diferentes, ajustando automáticamente
    las claves de las entidades para que funcionen correctamente en el hotel destino.

    Args:
        source_hotel_code: Código del hotel del cual se tomó el backup
        target_hotel_code: Código del hotel destino donde se restaurará
        backup_date: Fecha del backup en formato "YYYY-MM-DD-HH-MM-SS" (ej: "2025-02-24-00-00-00")
        backup: Si True, hace backup del hotel destino antes de la restauración
        add_reservations: Si True, incluye reservas en la restauración
        webseekers: Si True, incluye configuraciones de webseekers
        dominio_asociado: Dominio asociado para configuraciones específicas
    """
    logging.info("hotel_full_copy_from_backup_to_different_hotel: Restaurando backup de %s a %s desde %s",
                 source_hotel_code, target_hotel_code, backup_date)

    # Hacer backup del hotel destino por seguridad
    if backup:
        logging.info("Creando backup de seguridad del hotel destino: %s", target_hotel_code)
        execute_hotel_backup(target_hotel_code)

    # Obtener entidades válidas para restaurar
    valid_entities = ALL_ENTITIES_BY_DEFAULT_IN_BACKUP

    if not add_reservations:
        valid_entities = [entity for entity in valid_entities if entity != 'Reservation']

    logging.info("Entidades a restaurar: %s", valid_entities)

    # Restaurar cada entidad desde el backup
    for entity_name in valid_entities:
        logging.info("Procesando entidad: %s", entity_name)
        _remove_entity(target_hotel_code, entity_name)
        _restore_entity(source_hotel_code, target_hotel_code, entity_name,
                       from_storage=backup_date, dominio_asociado=dominio_asociado)

    # Crear mapa de claves migradas para corregir referencias
    logging.info("Creando mapa de claves migradas para corrección de referencias")
    full_map = {}
    for entity_name in valid_entities:
        full_map.update(_get_migrated_key_map(target_hotel_code, entity_name))

    # Corregir referencias entre entidades
    logging.info("Corrigiendo referencias entre entidades")
    fix_migrated_entity(target_hotel_code, 'Rate', 'RatePeriod', 'ratePeriods')
    fix_migrated_entity_field(target_hotel_code, 'WebPageProperty', full_map, 'entityKey')
    fix_migrated_entity_field(target_hotel_code, 'Picture', full_map, 'mainKey')
    fix_migrated_entity(target_hotel_code, 'Rate', 'RateCondition', 'rateCondition')

    _fix_migrated_integration_configuration(target_hotel_code)
    _fix_migrated_rate_condition(target_hotel_code)

    # Configuraciones adicionales
    if webseekers:
        logging.info("Aplicando configuraciones de webseekers")
        fix_web_seekers_after_backup(source_hotel_code, target_hotel_code)

    if add_reservations:
        logging.info("Corrigiendo referencias en reservas")
        _fix_migrated_reservations(target_hotel_code)

    for entity in valid_entities:
        refresh_entity_timestamps(entity, target_hotel_code)

    logging.info("Restauración completada exitosamente de %s a %s", source_hotel_code, target_hotel_code)


def hotel_full_copy(source, target, from_storage=False, backup=True, add_reservations=True, webseekers=False, dominio_asociado=""):
    # First of all, let's play it safe and make a backup
    logging.info("hotel_full_copy")
    if backup:
        execute_hotel_backup(target)

    if from_storage and not source == target:
        logging.error("CORTANDO EJECUCIÓN. From storage solo se  usar para el mismo hotel")


    valid_entities = _get_kinds_to_save(source, add_reservations=add_reservations)

    if not valid_entities and from_storage:
        valid_entities = ALL_ENTITIES_BY_DEFAULT_IN_BACKUP

    for entity_name in valid_entities:
        _remove_entity(target, entity_name)
        _restore_entity(source, target, entity_name, from_storage=from_storage, dominio_asociado=dominio_asociado)

    if from_storage:
        return

    full_map = {}
    for entity_name in valid_entities:
        full_map.update(_get_migrated_key_map(target, entity_name))

    fix_migrated_entity(target, 'Rate', 'RatePeriod', 'ratePeriods')
    fix_migrated_entity_field(target, 'WebPageProperty', full_map, 'entityKey')
    fix_migrated_entity_field(target, 'Picture', full_map, 'mainKey')

    fix_migrated_entity(target, 'Rate', 'RateCondition', 'rateCondition')

    _fix_migrated_integration_configuration(target)
    _fix_migrated_rate_condition(target)

    # TODO, Fix references at RatePeriod, Rate, RoomTypeStatus, YieldModification, Promotion, PriceIncrease,Restrictions, MarketingLogo, PriceTimer

    if webseekers:
        fix_web_seekers_after_backup(source, target)

    if add_reservations:
        _fix_migrated_reservations(target)


def populate_booking_entities(source, target, backup=True):
    # First of all, let's play it safe and make a backup
    if backup:
        execute_hotel_backup(target)

    # Idempotency
    _remove_migrated_entity(target, 'WebPageProperty')
    _remove_migrated_entity(target, 'Picture')

    for entity_name in BOOKING_ENTITIES_TO_RESTORE:
        _remove_entity(target, entity_name)
        _restore_entity(source, target, entity_name)
        _restore_slave_entity(source=source, target=target, slave_entity='WebPageProperty', master_entity=entity_name, field_relating='entityKey')

    for entity_name in BOOKING_ENTITIES_WITH_PICTURES:
        _restore_slave_entity(source=source, target=target, slave_entity='Picture', master_entity=entity_name, field_relating='mainKey')
    _restore_slave_entity(source=source, target=target, slave_entity='WebPageProperty', master_entity='Picture', field_relating='entityKey')

    fix_migrated_entity(target, 'Rate', 'RateCondition', 'rateCondition')

    _fix_migrated_integration_configuration(target)
    _fix_migrated_rate_condition(target)


# TODO, Fix references at RatePeriod, Rate, RoomTypeStatus, YieldModification, Promotion, PriceIncrease,Restrictions, MarketingLogo, Reservation, PriceTimer


def execute_hotel_only_web_copy(source, target, webseeker=False, backup=True, add_advanced_configurations=True, dominio_asociado="", add_news=True):
    # First of all, let's play it safe and make a backup
    if backup:
        execute_hotel_backup(target)

    valid_entities = _get_kinds_to_save(source, only_web=True, add_advanced_configurations=add_advanced_configurations, webseekers=webseeker, add_news=add_news)

    origin_enties_with_wpp_id = []

    non_fully_deletable_entities = ["WebPageProperty", "Picture"]

    for entity_name in valid_entities:
        if not entity_name in non_fully_deletable_entities:
            _remove_entity(target, entity_name, wpp_only_web=False)
            new_ids_generated, origin_ids_added = _restore_entity(source, target, entity_name, enties_with_wpp_id=[], dominio_asociado=dominio_asociado)
            if entity_name in ONLYWEB_ENTITIES_WITH_WPP:
                origin_enties_with_wpp_id += origin_ids_added

    # and finally WPP and Pics! BE CAREFUL!! PICS before WPP (because we need to keep WPP of Pics)!

    if "Picture" in valid_entities:
        # PICTURES: NOT REMOVE PICS related to BOOKING_ENTITIES_WITH_PICTURES

        main_keys_to_keep = []
        for entity_booking_wit_pics in BOOKING_ENTITIES_WITH_PICTURES:
            all_entities_related_to_pics = datastore_communicator.get_using_entity_and_params(entity_name=entity_booking_wit_pics, keys_only=True,
                                                                                              hotel_code=target)

            for current_to_keep in all_entities_related_to_pics:
                key_to_keep = datastore_utils.id_to_entity_key(target, current_to_keep.key)
                main_keys_to_keep.append(key_to_keep)

        _remove_entity(target, "Picture", wpp_only_web=False, main_keys_to_keep=main_keys_to_keep)
        new_ids_generated, origin_ids_added = _restore_entity(source, target, "Picture", enties_with_wpp_id=[], dominio_asociado=dominio_asociado)
        origin_enties_with_wpp_id += origin_ids_added

    if "WebPageProperty" in valid_entities:
        # WEB PAGE PROPERTIES: only remove and only restore WPP about websections and pictures and be carefu  with EDITAR button

        _remove_entity(target, "WebPageProperty", wpp_only_web=True)
        _restore_entity(source, target, "WebPageProperty", enties_with_wpp_id=origin_enties_with_wpp_id)

    full_map = {}
    for entity_name in valid_entities:
        full_map.update(_get_migrated_key_map(target, entity_name))

    fix_migrated_entity_field(target, 'WebPageProperty', full_map, 'entityKey')
    fix_migrated_entity_field(target, 'Picture', full_map, 'mainKey')

    if webseeker:
        copy_web_seekers_structure(source, target, full_map)


def fix_web_seekers_after_backup(source, target):
    valid_entities = _get_kinds_to_save(source, only_web=True)
    full_map = {}
    for entity_name in valid_entities:
        full_map.update(_get_migrated_key_map(target, entity_name))

    fix_slave_wysiwyg(source, target, full_map)
    fix_migrated_entity_field(target, 'Wysiwyg', full_map, 'section')


def copy_web_seekers_structure(source, target, full_map):
    fix_migrated_entity_field(target, 'Wysiwyg', full_map, 'section')
    fix_slave_wysiwyg(source, target, full_map)

    add_webconfiguration(source, target, 'wysiwyg')


def fix_slave_wysiwyg(source, target, full_map):
    entities_to_update = []
    destiny_entities = datastore_communicator.get_using_entity_and_params(entity_name='Wysiwyg', keys_only=False, hotel_code=target)
    for current_entity in destiny_entities:

        origin_slave_id = current_entity["slave"]
        if origin_slave_id:
            # origin_entity = datastore_communicator.get_using_entity_and_params(entity_name='Wysiwyg', search_params=[('id', '=', origin_slave)], keys_only=False, hotel_code=source)
            origin_entity = datastore_communicator.get_entity('Wysiwyg', origin_slave_id, hotel_code=source)
            if origin_entity:
                origin_key = datastore_utils.id_to_entity_key(source, origin_entity.key)
                migration_slave = full_map.get(origin_key)
                final_slave_id = alphanumeric_to_id(migration_slave)
                if final_slave_id:
                    current_entity["slave"] = final_slave_id
                    entities_to_update.append(current_entity)

    logging.info("Entities to update: %s", len(entities_to_update))
    datastore_communicator.save_entity_multi(entities_to_update, target)
    hotel_manager_utils.flush_entity_cache(target, 'Wysiwyg')


def add_webconfiguration(source, target, config_name):
    all_web_configurations = datastore_communicator.get_using_entity_and_params(entity_name="WebConfiguration", keys_only=False, hotel_code=source)

    new_entities = []
    for webconfig in all_web_configurations:
        new_entity = dict(webconfig)

        if new_entity.get("name") == config_name:
            new_entities.append(new_entity)

    if new_entities:
        ids = [""] * len(new_entities)
        datastore_communicator.save_multiple_entities("WebConfiguration", ids, new_entities, hotel_code=target)
        hotel_manager_utils.flush_entity_cache(target, "WebConfiguration")


def fix_lost_pictures_from_source_of_entity(source, target, entity_mother):
    all_mother_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_mother, keys_only=False, hotel_code=target)
    all_pictures_in_target = datastore_communicator.get_using_entity_and_params(entity_name="Picture", keys_only=False, hotel_code=target)
    all_pictures_from_source = datastore_communicator.get_using_entity_and_params(entity_name="Picture", keys_only=False, hotel_code=source)

    for entity in all_mother_entities:
        if entity.get("pictures"):
            entity_key = datastore_utils.id_to_entity_key(target, entity.key)
            already_has_pictures = find_all_entities_related(entity_key, all_pictures_in_target, column_related="mainKey")

            if not already_has_pictures:
                entity_legacy_key = entity.get("legacy_key", "")
                pictures_to_add = find_all_entities_related(entity_legacy_key, all_pictures_from_source, column_related="mainKey")

                for ent in pictures_to_add:
                    ent["mainKey"] = entity_key
                    datastore_communicator.save_to_datastore("Picture", None, ent, hotel_code=target)


def fix_lost_wpp_from_source_of_entity(source, target, entity_mother, from_storage=""):
    all_mother_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_mother, keys_only=False, hotel_code=target)
    all_wpp_in_target = datastore_communicator.get_using_entity_and_params(entity_name="WebPageProperty", keys_only=False, hotel_code=target)

    all_wpp_from_source = None
    if from_storage:
        all_wpp_from_source = get_last_backup(source, from_storage, "WebPageProperty")
    elif source:
        all_wpp_from_source = datastore_communicator.get_using_entity_and_params(entity_name="WebPageProperty", keys_only=False, hotel_code=source)

    if not all_wpp_from_source:
        logging.error("Or source or from_storage folder MUST be declared!!!")
        return

    num_wpp_passed = 0
    for mother_entity in all_mother_entities:

        mother_key = datastore_utils.id_to_entity_key(target, mother_entity.key)
        # wpp_to_add = _get_wpp_with_entity_key(all_wpp_from_backup, picture.legacy_key)

        original_mother_key = mother_entity.get(LEGACY_KEY_FIELD)
        if not original_mother_key:
            # if we dont have LEGACY_KEY, maybe is a RoomType or Promotion not moved before
            #if entity_mother in BOOKING_ENTITIES_WITH_PICTURES:
                # i'm a cobard
            original_mother_key = mother_key

        if original_mother_key:
            wpp_to_add = find_all_entities_related(original_mother_key, all_wpp_from_source)

            if wpp_to_add:
                wpp_already_added = find_all_entities_related(mother_key, all_wpp_in_target)

                main_keys_to_ignore = {x['mainKey']: True for x in wpp_already_added}

                for current_wpp in wpp_to_add:
                    current_main_key = current_wpp['mainKey']
                    if not current_main_key in main_keys_to_ignore:
                        current_wpp['entityKey'] = mother_key
                        current_wpp['timestamp'] = _get_utc_now()

                        updated_id = datastore_communicator.save_to_datastore("WebPageProperty", None, current_wpp, hotel_code=target)
                        num_wpp_passed += 1
    hotel_manager_utils.flush_entity_cache(target, 'WebPageProperty')

    logging.info("num_wpp_passed: %s", num_wpp_passed)


def find_all_entities_related(origin_key, all_entites_in_backup, column_related="entityKey"):
    entities_found = []
    for enti in all_entites_in_backup:
        if str(enti.get(column_related, "")) == str(origin_key):
            entities_found.append(enti)

    return entities_found


def fix_lost_pics_4_entities(target, backup_date_forder, entities_names):
    all_entities_to_fix = []
    for entity_name in entities_names:
        all_entities_to_fix += datastore_communicator.get_using_entity_and_params(entity_name=entity_name, keys_only=True, hotel_code=target)

    all_pics = get_last_backup(target, backup_date_forder, 'Picture')

    entities_to_update = []
    for current_to_fix in all_entities_to_fix:
        origin_key = datastore_utils.id_to_entity_key(target, current_to_fix.key)
        entities_to_update += find_all_entities_related(origin_key, all_pics, column_related="mainKey")

    if entities_to_update:
        logging.info("Entities to update: %s", len(entities_to_update))

        all_new_pics_id = []
        for entity in entities_to_update:
            entity[LEGACY_KEY_FIELD] = entity.get("key")
            updated_id = datastore_communicator.save_to_datastore("Picture", None, entity, hotel_code=target)
        # logging.info("Entity %s recovered!!!!", entity)

    # if you wnt to recover also WPP of this image just execute before:
    fix_lost_wpp_from_source_of_entity(source=target, target=target, entity_mother="Picture", from_storage=backup_date_forder)

    hotel_manager_utils.flush_entity_cache(target, 'Picture')


def clean_cache_in_hotel(hotel_code, entities):
    logging.info("Cleaning hotel: %s", hotel_code)
    for entity in entities:
        try:
            hotel_manager_utils.flush_entity_cache(hotel_code, entity)
        except Exception as e:
            logging.error("Error en hotel: %s, entities: %s", hotel_code, entity)

    logging.info("Hotel %s flushed", hotel_code)


def fix_cache_all_hotels():
    hotels = get_all_valid_hotels()

    ENTITIES_TO_FLUSH = ('Promotion', 'Rate', 'MultiRate', 'rates.PriceIncrease', 'Picture')

    params = []
    for hotel in hotels:
        params.append((hotel['applicationId'], ENTITIES_TO_FLUSH))

    concurrency_utils.execute_in_parallel(clean_cache_in_hotel, params, max_concurrency=10)


def execute_funcion(request):
    dictFunction = {"hotel_backup": "execute_hotel_backup(source)",
                    'full_copy': 'hotel_full_copy(source=source, target=target, from_storage = from_storage, backup = backup,add_reservations= add_reservations, webseekers = webseekers, dominio_asociado = dominio_asociado)',
                    'only_web_copy': 'execute_hotel_only_web_copy(source=source, target = target, webseeker = webseekers,add_advanced_configurations= add_advanced_configurations, backup = backup, dominio_asociado=dominio_asociado, add_news = add_news)',
                    'migrated_reservations': '_fix_migrated_reservations(source)'}
    # Set CORS headers for the main request
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Max-Age': '3600'
    }

    try:

        data = request.get_json(force=True)
        if not data:
            data = request.values
        logging.info("Info data %s", data)
        typeFunction = data.get("typeFunction")
        hotels = get_all_hotels()
        hotel = [x for x in list(hotels.values()) if x.get('id') == int(data.get("source"))]
        urlSource = hotel[0].get('url')
        source = hotel[0]["applicationId"]
        logging.info("origin hotel %s", source)
        hotel = [x for x in list(hotels.values()) if x.get('id') == int(data.get("target"))]
        target = hotel[0]["applicationId"]
        checkDominio = data.get("checkDominio")
        dominio_asociado = ""
        if checkDominio:
            application = hotel[0].get("url").replace("https://rest-dot-", "").replace(".appspot.com", "")
            ind = application.find("/multi/")
            if ind > 1:
                nuevo = True
                application = application[0:ind]
            dominio_asociado = f"https://{target}-dot-{application}.appspot.com"
        logging.info("destination hotel %s", target)
        from_storage = data.get("from_storage")
        backup = data.get("backup")
        webseekers = data.get("webseekers")
        add_reservations = data.get("add_reservations")
        add_advanced_configurations = data.get("add_advanced_configurations")
        logging.info("Start function %s", dictFunction[typeFunction])
        exe_function = dictFunction[typeFunction]
        hotel_code = target
        add_news = data.get("addNews")

        eval(exe_function)

        do_clean_web_page_properties_for_hotel(hotel_code)

        # clean_web_page_properties_for_hotel

        url_get = "https://europe-west1-build-tools-2.cloudfunctions.net/clean_web_page_properties_for_hotel?hotel_code=%s" % hotel_code
        logging.info("calling url_get %s", url_get)
        response = requests.get(url_get)
        logging.info("response %s", response)

        url_get = f"https://wpp-utils-flask-399475283438.europe-west1.run.app/clean-wpp-by-hotel?hotel_code={hotel_code}"
        logging.info("calling url_get %s", url_get)
        response = requests.get(url_get)
        logging.info("response %s", response)

    except Exception as e:
        return ('KO', 204, headers)

    return ('OK', 200, headers)


def create_hotel(request):
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Max-Age': '3600'
    }
    try:
        # data = request.values
        data = request.get_json(force=True)
        item = {"name": data.get("name"), "url": "https://rest-dot-" + data.get("application") + ".appspot.com/multi/" + str(data.get("hotelCode")),
                "percentage": data.get("comision"), "description": data.get("name"),
                "inProduction": data.get("production"), "applicationId": data.get("hotelCode"), "enabled": True}

        datastore_communicator.save_to_datastore("HotelApplication", None, item, "admin-hotel")


    except Exception as e:
        logging.info(e)
        return 'KO', 204, headers

    return 'OK', 200, headers


if __name__ == '__main__':
    # ATENCIÓN SOLO DEBE HABER UNA FUNCION ACTIVA!! (a no ser que sepas que vas a hacer varias copias ;)))) )
    print("JUST UNCOMMENT METHODS!!")
    #execute_hotel_backup('port-denia')


    # execute_hotel_only_web_copy(source='demo3', target="diamar-lanzarote", add_advanced_configurations=True, webseeker=True, backup=True)


    # hotel_full_copy(source='entremares-hotel', target='entremares-hotel', from_storage="2024-06-20-10-57-29", backup=False, webseekers=False, add_reservations=False)


    # MAIN FUNCTIONS:

    # Only a full backup!
    # execute_hotel_backup('mint-bannister')

    # happy flow full copy:
    # hotel_full_copy(source='eco-servamar', target='best-lloret-splash', backup=False)

    # happy flow full copy WITH NO RESERVATIONS:
    # hotel_full_copy(source='olee-calaceite', target='olee-torrox', backup=True, add_reservations=False)

    # ---> happy flow only web CON WEB SEEKERS:
    # hotel_only_web_copy(source='hotansa-cervol', target="lesbrases", webseeker=True, backup=True)

    # happy flow only web SIN web-seekers
    # hotel_only_web_copy(source='baseline-express', target='marsol-hotel', webseeker=False, backup=True)

    # EJEMPLO ONLY WEB SIN configuraciones avanzadas (Solo secciones, pictures y noticias)
    # hotel_only_web_copy(source='test4-copia4', target='em-cartagena', webseeker=False, backup=True, add_advanced_configurations=False)

    # FULL COPY desde copia de seguridad
    # hotel_full_copy(source='port-corpo', target='test4-copia13', from_storage="2021-11-10-21-50-03", backup=False)

    # RESTAURAR BACKUP DE UN HOTEL ORIGEN A UN HOTEL DESTINO DIFERENTE
    hotel_full_copy_from_backup_to_different_hotel(source_hotel_code='puerto-palace',
                                                   target_hotel_code='puerto-palace-old',
                                                   backup_date="2024-12-29-08-58-06", backup=True,
                                                   add_reservations=False, webseekers=True)

    # SOLO MOTOR: (room, rates, boards, promotion, rate conditions y XML configs)
    # populate_booking_entities(source='olee-calaceite', target='test5-copia6', backup=False)

    ###############################################

    # FIX FUNCTIONS!
    #REALLY USE FULL:
    '''for entity_to_fix in ['Rate', 'RateCondition', 'Supplement', 'MultiRate', 'News', 'PriceIncrease']:
           fix_lost_wpp_from_source_of_entity(source='entremares-hotel', target='entremares-hotel', entity_mother=entity_to_fix, from_storage="2024-06-20-10-57-29")
       '''

    # _restore_slave_entity(source='olee-calaceite', target='test5-copia6', slave_entity='Picture', master_entity='RoomType', field_relating='mainKey')

    # fix_web_seekers_after_backup(source='test5-copia7', target='test9')

    # fix_cache_all_hotels()



    # fix_lost_pics_4_entities(target='hotel-puentereal', backup_date_forder='2021-11-04-10-55-55',entities_names=BOOKING_ENTITIES_WITH_PICTURES)
    # hotel_manager_utils.flush_entity_cache('port-corpo', 'WebPageProperty')


