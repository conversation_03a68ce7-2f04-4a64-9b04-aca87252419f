import datetime
import logging

from flask import jsonify
from paraty.development.users.add_callcenter_config_to_user import get_all_user_sessions


def do_get_if_users_logged_in_callcenter(users, start_date, months):
    try:
        logging.info(f"Checking if users {users} have logged in to callcenter between {start_date} and the next {months} months.")
        start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        users_logged_in = get_all_user_sessions(start_date_obj, months)
        filtered_users = users_logged_in
        if users_logged_in and users:
            filtered_users = [user for user in users_logged_in if user.get('name') in users]

        filtered_users = sorted(filtered_users, key=lambda x: x.get('name', ''))
        logging.info(f"Users logged in to callcenter: {filtered_users}")

        found = "found" if filtered_users else "not found"
        message = f"Users sessions to callcenter {found}"
        return jsonify({
            "-message": message,
            "filtered_users": filtered_users
        })
    except Exception as e:
        logging.error(f"Error getting users logged in to callcenter: {e}")
        return jsonify({
            "-message": 'ERROR getting users logged in to callcenter',
            "error": e
        })


if __name__ == '__main__':
    users = ['ebaena', 'jperez']
    start_date = datetime.datetime(2024, 11, 16, 00, 00, 00)
    months = 2
    do_get_if_users_logged_in_callcenter(users, start_date, months)