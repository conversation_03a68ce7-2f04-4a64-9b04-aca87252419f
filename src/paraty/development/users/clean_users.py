from paraty.development.users.add_callcenter_config_to_user import add_callcenter_config, get_all_user_sessions
from paraty.development.users.disable_users import disable_users
from paraty_commons_3.datastore import datastore_communicator


def get_all_users():
    all_users = datastore_communicator.get_using_entity_and_params('ParatyUser', hotel_code="admin-hotel")
    return all_users


if __name__ == '__main__':
    all_users = get_all_users()
    users_logged_in = get_all_user_sessions()
    users_logged_in = {u.get('name') for u in users_logged_in}
    for user in all_users:
        disable_users(user)
        add_callcenter_config(user, users_logged_in)