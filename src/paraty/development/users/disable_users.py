import datetime
import logging

from paraty_commons_3.datastore import datastore_communicator

CHANGES_FILE = 'changes.txt'


def get_last_logging_attempt(user):
    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=365)
    # year_before_today = year_before_today.strftime('%Y-%m-%d %H:%M:%S')
    year_before_today = str(year_before_today)
    last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt', [('user', '=', user), ('timestamp', '>', year_before_today)], hotel_code="user-seeker:")
    return last_logging_attempt


def disable_user(user):
    try:
        if user.get('enabled') is not False:
            user['enabled'] = False
            datastore_communicator.save_to_datastore('ParatyUser', user.id, user, hotel_code="admin-hotel")
            return True
        else:
            return False
    except Exception as e:
        logging.error(f"Error disabling user: {user.get('name')} ERROR {e}")


def get_user_name(user):
    return user.get('name')


def is_paraty_user(user):
    permissions = user.get('permission')
    if permissions:
        return 'admin' in permissions

def disable_users(user):
    disabled_users = []
    user_name = get_user_name(user)
    if not is_paraty_user(user):
        last_logging_attempt = get_last_logging_attempt(user_name)
        if not last_logging_attempt:
            disabled = disable_user(user)
            if disabled:
                disabled_users.append(user_name)

                # Abrir el archivo en modo "a" para NO sobrescribirlo al inicio
                with open(CHANGES_FILE, "a") as log_file:
                    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    log_file.write(f"User disabled: {user_name} - {timestamp} - \n")


# if __name__ == '__main__':
#     disable_users()