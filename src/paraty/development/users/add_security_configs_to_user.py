import datetime
import json
import logging
import pickle
from concurrent.futures import ThreadPoolExecutor
from paraty.development.users.disable_users import CHANGES_FILE
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_hotel_code_by_id(id, all_valid_hotels):
    for hotel in all_valid_hotels:
        if id == hotel.get("id"):
            return hotel.get("applicationId")



def add_security_configs():
    users = datastore_communicator.get_using_entity_and_params('ParatyUser', hotel_code="admin-hotel:")
    all_valid_hotels = get_all_valid_hotels()

    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta()
    today_now = str(year_before_today).split(".")[0]

    user_changes = []
    for user in users:
        try:

            user_change = ""

            configuration_map = user.get('configurationMap', [])
            save_email = True
            save_expire = False
            for item in configuration_map:
                if 'email_password' in item:
                    save_email = False
                    break

            if save_email:

                app_ids= user.get('accesibleApplications')
                email_reservas = ''
                for app_id in app_ids:
                    hotel_code = get_hotel_code_by_id(app_id, all_valid_hotels)

                    email_reservas = get_hotel_advance_config_value(hotel_code,
                                                                    'Email reservas')

                    if email_reservas:
                        email_reservas = email_reservas.replace(";", ",").split(",")[0]
                        break



                if email_reservas:
                    configuration_map.append("email_password @@ %s" % email_reservas)
                    user['configurationMap'] = configuration_map

                    user_change = "%s new email_password: %s" % (user.get("name"), email_reservas)
                else:
                    email_reservas = "<EMAIL>"
                    configuration_map.append("email_password @@ %s" % email_reservas)
                    user['configurationMap'] = configuration_map
                    user_change = "%s new email_password: %s" % (user.get("name"), email_reservas)


                if not user.get("lastPasswordChange"):

                    user['lastPasswordChange'] = today_now
                    save_expire = True

                    user_change += " %s new lastPasswordChange: %s" % (user.get("name"), today_now)


            if save_email or save_expire:
                logging.info("SAVING %s", user_change)
                user_changes.append(user_change)
                datastore_communicator.save_to_datastore('ParatyUser', user.id, user, hotel_code="admin-hotel:")
        except Exception as e:
            logging.error(f"Error cahngin user %s" % e)


    with open("User_security_changes_DEFAULT2.txt", "w", encoding="utf-8") as archivo:
        json.dump(user_changes, archivo, ensure_ascii=False, indent=4)

if __name__ == '__main__':
     add_security_configs()
