import datetime
import logging
import pickle
from concurrent.futures import ThreadPoolExecutor
from paraty.development.users.disable_users import CHANGES_FILE
from paraty_commons_3.datastore import datastore_communicator


def get_monthly_ranges(start_date, months=12):
    ranges = []
    for i in range(months):
        start_of_month = start_date + datetime.timedelta(days=30 * i)
        end_of_month = (start_of_month + datetime.timedelta(days=30)).replace(day=1) - datetime.timedelta(seconds=1)
        ranges.append((start_of_month, end_of_month))
    return ranges


def fetch_monthly_data(date_range):
    start_date, end_date = date_range
    try:
        logging.info(f"Starting query for UserSessions, range: {start_date} a {end_date}")

        all_user_sessions = datastore_communicator.get_using_entity_and_params(
            'UserSession',
            [('timestamp', '>', start_date), ('timestamp', '<', end_date)],
            hotel_code="call-seeker:"
        )

        return all_user_sessions
    except Exception as e:
        logging.error(f"Error in query for UserSessions, range {start_date} a {end_date}: {e}")


def fetch_multiple_months(start_date, months=12, max_workers=10):
    month_ranges = get_monthly_ranges(start_date, months)
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(fetch_monthly_data, month_ranges))
    return results


def get_all_user_sessions(start_date=None, months=12):
    users_logged_in = []
    today = datetime.datetime.now(datetime.timezone.utc)
    start_date = today - datetime.timedelta(days=365) if not start_date else start_date
    all_user_sessions = fetch_multiple_months(start_date, months=months, max_workers=5)
    if not all_user_sessions:
        logging.warning("No sessions returned")
        return users_logged_in

    for users_session in all_user_sessions:
        for user_session in users_session:
            try:
                content = user_session.get('content')
                if content:
                    content = pickle.loads(content)
                    user = content.get('user')
                    if user:
                        user_name = user.get('name')
                        login_timestamp = user_session.get('timestamp', '')
                        if login_timestamp:
                            login_timestamp = login_timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        if user_name:
                            user_logged = {'name': user_name, 'login_timestamp': login_timestamp}
                            users_logged_in.append(user_logged)

            except Exception as e:
                logging.error(f"Error processing user session: {e}")
    return users_logged_in


def has_user_access_callcenter(user, users_logged_in):
    user_name = get_user_name(user)
    return user_name in users_logged_in


def get_user_name(user):
    return user.get('name')


def has_user_callcenter_config(user):
    configuration_map = get_config_map(user)
    for item in configuration_map:
        if 'callcenter' in item:
            return True

    return False


def get_config_map(user):
    configuration_map = user.get('configurationMap', [])
    return configuration_map


def do_add_callcenter_config(user):
    configuration_map = get_config_map(user)
    configuration_map.append('callcenter @@ True')
    user['configurationMap'] = configuration_map
    try:
        datastore_communicator.save_to_datastore('ParatyUser', user.id, user, hotel_code="admin-hotel")
    except Exception as e:
        logging.error(f"Error adding callcenter config to user: {get_user_name(user)} ERROR {e}")


def add_callcenter_config(user, users_logged_in):
    if has_user_access_callcenter(user, users_logged_in):
        if has_user_callcenter_config(user):
            return
        do_add_callcenter_config(user)
        logging.info(f"Callcenter config added to user: {get_user_name(user)}")

        # Abrir el archivo en modo "a" para NO sobrescribirlo al inicio
        with open(CHANGES_FILE, "a") as log_file:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_file.write(f"Callcenter config added to user:: {get_user_name(user)} - {timestamp} - \n")


# if __name__ == '__main__':
#     add_callcenter_config()
