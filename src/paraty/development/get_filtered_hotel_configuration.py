import json, io

import openpyxl

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.common_data.common_data_provider import get_hotel_all_advance_configs, \
    get_hotel_advance_config_item


def gateway_amount_configuration(hotel_configuration_translates, hotel):
    if hotel.get("applicationId"):
        param_to_find = "Gateway Amount"
        hotel_config = get_hotel_advance_config_item(hotel, param_to_find)
        param_to_get = "value"
        config_values = get_advance_hotel_configuration_value(hotel_config, param_to_get)
        translations = translate_config(config_values, hotel_configuration_translates)
        if translations:
            return translations


def range_percent_configuration(hotel_configuration_translates, hotel):
    if hotel.get("applicationId"):
        hotel_code = hotel["applicationId"]
        param_to_find = "RANGE_PERCENT"
        param_to_get = "configurations"
        hotel_config = get_all_integration_configuration_of_hotel(hotel, param_to_find, param_to_get)
        translations = translate_config(hotel_config, hotel_configuration_translates)
        if translations:
            return translations


def tokenizer_configuration(hotel):
    if hotel.get("applicationId"):
        param_to_find = "credit cards by token"
        hotel_config = get_hotel_advance_config_item(hotel, param_to_find)
        param_to_get = "value"
        config_values = get_advance_hotel_configuration_value(hotel_config, param_to_get)
        if config_values:
            result = ", ".join(config_values)
            return result



def get_advance_hotel_configuration_value(hotel_config, name_value: str = None):
    if len(hotel_config) > 0 and (hotel_config[0].get(name_value) or hotel_config.get(name_value)):
        value_list = hotel_config[0][name_value].split(";")
        return value_list


def get_all_integration_configuration_of_hotel(hotel, param_to_find: str, param_to_get: str):
    result_config = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration',
                                                                         hotel_code=hotel['applicationId'])
    for config in result_config:
        if param_to_find in config["name"]:
            return config[param_to_get]


def translate_config(values, hotel_configuration_translates):
    translations = []
    if values:
        for value in values:
            if hotel_configuration_translates.get(value):
                result = hotel_configuration_translates[value]
                translations.append(result)
            else:
                result = value
                for config, translate in hotel_configuration_translates.items():
                    if value in config:
                        result = translate
                        break
                translations.append(result)

        result = ", ".join(translations)
        return result


def save_data_into_excel(hotel_code, translations, config_sheet, workbook, translates_excel_directory):
    data_to_insert = [hotel_code, translations]
    config_sheet.append(data_to_insert)
    workbook.save(translates_excel_directory)


if __name__ == '__main__':
    translates_excel_directory = "../utilities/excel/hotel_configurations_to_migarete_pep.xlsx"
    workbook = openpyxl.Workbook()
    config_sheet = workbook.active
    config_sheet.append(('Hotel', 'gateway Amount', 'RANGE_PERCENT', 'Credit cards by token (Tokenizador)'))

    with open('../utilities/translates/hotel_configuration.json') as file:
        hotel_configuration_translates = json.load(file)

    all_valid_hotels = get_all_valid_hotels()
    for hotel in all_valid_hotels:
        if hotel.get("applicationId"):
            hotel_code = hotel["applicationId"]
            gateway_amount_translates_configuration = gateway_amount_configuration(hotel_configuration_translates, hotel)
            range_percent_translates_configuration = range_percent_configuration(hotel_configuration_translates, hotel)
            credit_cards_by_token = tokenizer_configuration(hotel)
            if gateway_amount_translates_configuration or range_percent_translates_configuration or credit_cards_by_token:
                data_to_insert = [hotel_code, gateway_amount_translates_configuration, range_percent_translates_configuration, credit_cards_by_token]
                config_sheet.append(data_to_insert)
                workbook.save(translates_excel_directory)

