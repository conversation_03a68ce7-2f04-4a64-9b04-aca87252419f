import datetime
import json

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

from paraty_commons_3.logging.my_gae_logging import logging

def has_dates_with_small_time_difference(paylinks_list, threshold_seconds=300):
   # Convert date strings to datetime objects
   date_objects = [datetime.datetime.strptime(paylink.get("timestamp"), "%Y-%m-%d %H:%M:%S") for paylink in paylinks_list]

   # Check for time differences
   for i in range(len(date_objects) - 1):
      for j in range(i + 1, len(date_objects)):
         time_difference = abs((date_objects[i] - date_objects[j]).total_seconds())
         if time_difference < threshold_seconds:
            return True

   return False

def check_duplicated_paylink_reservations(hotel_code=None, hotel_name_filter=None, only_this_identifier=None, days=1):
   from datetime import datetime, timedelta

   if only_this_hotel:
      hotel_codes = [only_this_hotel]
   else:
      hotel_codes = [hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter] if hotel_name_filter else ([hotel_code] if hotel_code else get_all_hotels())


   bad_reservations = {}


   for hotel_code in hotel_codes:

      if "demo" in hotel_code.lower():
         continue

      bad_reservations[hotel_code] = []
      try:
         pep_paylinks = list(get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code,return_cursor=True, search_params=[('name', '=', 'PEP_PAYLINKS COBRADOR')]))[0]
      except:
         logging.info(f'Hotel {hotel_code} no tiene configurado PEP_PAYLINKS COBRADOR')
         pep_paylinks = {}

      if pep_paylinks:

         res_search_param = [('timestamp', '>', (str(datetime.now() - timedelta(days=days))).split(' ')[0])]
         if only_this_identifier:
            res_search_param = [('identifier', '=', only_this_identifier)]


         reservations = list(get_using_entity_and_params('Reservation', hotel_code=hotel_code, return_cursor=True, search_params=res_search_param)) or logging.info(f'No hay reservas en {hotel_code} en los últimos {days} días') or []
         for reservation in reservations:
            if not reservation.get('cancelled'):
               payed_by_tpv_link = json.loads(reservation.get('extraInfo', '')).get('payed_by_tpv_link')

               if payed_by_tpv_link:
                  bad_reservation = has_dates_with_small_time_difference(payed_by_tpv_link)
                  if bad_reservation:
                     if (not start_date_bigger_than) or start_date_bigger_than <= reservation.get('startDate'):
                        bad_res_info = {
                           "hotel": hotel_code,
                           "identifier": reservation.get('identifier'),
                           "timestamp": reservation.get('timestamp'),
                           "startDate": reservation.get('startDate'),
                           "paylinks_list": payed_by_tpv_link

                        }

                        bad_reservations[hotel_code].append(bad_res_info)

   return bad_reservations










only_this_hotel = None
#only_this_hotel = "oasishoteles-grandpalm"



only_this_identifier = None
#only_this_identifier="53915660"

days = 365

# name_filter = None
name_filter = 'oasis'


start_date_bigger_than = "2023-12-26"


paid_reservations = check_duplicated_paylink_reservations(hotel_code=only_this_hotel, hotel_name_filter=name_filter, only_this_identifier=only_this_identifier, days=days)

print(json.dumps(paid_reservations))

