
import json
from datetime import datetime, timedelta

from paraty_commons_3.datastore import datastore_communicator
import logging


def check_identifier(request):
	identifier = request.values.get("identifier", '')
	hotel_code = request.values.get("hotel_code", '')
	message = f' Identifier: {identifier}'
	message += f' - Hotel Code: {hotel_code}' if hotel_code else ''
	logging.info(message)
	identifier_records = get_BookingRequestLog(identifier) or get_BookingRequestLog(str(identifier.upper()))

	identifier_list = json.loads(json.dumps(identifier_records))
	used_identifier = False
	for item_identifier in identifier_list:
		try:
			date_now = datetime.now()
			date_time_backup = date_now.strftime('%Y-%m-%d %H:%M:%S')
			reservation_pushed_date = datetime.strptime(item_identifier.get("integrationPushTimestamp", str(date_time_backup)), '%Y-%m-%d %H:%M:%S')
			days_to_check = 7

			if hotel_code == item_identifier.get("hotelCode") and check_push_timestamp(reservation_pushed_date, days=days_to_check):
				# if hotel is the same, and integrationPushTimestamp is less than days_to_check, we assume that the locator is OK!
				used_identifier = False
				break
			else:
				used_identifier = True
				break
		except ValueError as e:
			logging.error(f'Error parsing timestamp: {e}')
	try:
		if not used_identifier:
			audit_records = list(datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent', hotel_code='siteminder-adapter:', return_cursor=True, search_params=[('request_id', '=', identifier)]))
			for record in audit_records:
				if record.get('hotel_code', '') != hotel_code:
					used_identifier = True
					break
	except:
		logging.error(f'Error getting EndpointCallAuditEvent from siteminder')

	result = {"result": "used"} if used_identifier else {"result": "not_used"}
	logging.info(f'Identifier {result.get("result")}')
	return result


def check_identifier_manual(identifier, hotel_code=""):
	# identifier = request.values.get("identifier", '')
	message = f' Identifier: {identifier}'
	message += f' - Hotel Code: {hotel_code}' if hotel_code else ''
	logging.info(message)
	identifier_records = get_BookingRequestLog(identifier) or get_BookingRequestLog(str(identifier.upper()))

	identifier_list = json.loads(json.dumps(identifier_records))
	used_identifier = False
	for item_identifier in identifier_list:
		try:
			date_now = datetime.now()
			date_time_backup = date_now.strftime('%Y-%m-%d %H:%M:%S')
			reservation_pushed_date = datetime.strptime(
				item_identifier.get("integrationPushTimestamp", str(date_time_backup)), '%Y-%m-%d %H:%M:%S')
			days_to_check = 7

			if hotel_code == item_identifier.get("hotelCode") and check_push_timestamp(
					reservation_pushed_date, days=days_to_check):
				# if hotel is the same, and integrationPushTimestamp is less than days_to_check, we assume that the locator is OK!
				used_identifier = False
				break
			else:
				used_identifier = True
				break
		except ValueError as e:
			logging.error(f'Error parsing timestamp: {e}')
	try:
		if not used_identifier:
			audit_records = list(datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent', hotel_code='siteminder-adapter:', return_cursor=True, search_params=[('request_id', '=', identifier)]))
			for record in audit_records:
				if record.get('hotel_code', '') != hotel_code:
					used_identifier = True
					break
	except:
		logging.error(f'Error getting EndpointCallAuditEvent from siteminder')

	result = {"result": "used"} if used_identifier else {"result": "not_used"}
	logging.info(f'Identifier {result.get("result")}')
	return result


def check_push_timestamp(reservation_pushed_date, days=7):
	# if integrationPushTimestamp was less than days ago, we assume it's the same reservation
	date_now = datetime.now() - timedelta(days=days)
	return reservation_pushed_date > date_now


def get_BookingRequestLog(identifier):
	return list(datastore_communicator.get_using_entity_and_params('BookingRequestLog', hotel_code='admin-hotel',
		return_cursor=True,
		search_params=[('identifier', '=', identifier)]))


if __name__ == "__main__":
	# check_push_timestamp('2023-12-22 17:00:52', 3)

	check_identifier_manual("68052420", 'marinas-de-nerja')
