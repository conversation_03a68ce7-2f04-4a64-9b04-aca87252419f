import csv
import json
import logging

import requests

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, delete_entity, \
    save_to_datastore

INPUT_DIR = 'input_data'
OUTPUT_DIR = 'output_data'
DEFAULT_LANGUAGE = 'SPANISH'
DEFAULT_LOGOTYPE = 'https://cdn.paraty.es/landmar/images/external/0834_240520240833'


def import_users(file_name, current_users):
    users_list = []
    final_users = []

    with open(f'{INPUT_DIR}/{file_name}', mode='r', encoding='utf-8-sig') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            users_list.append(dict(row))

    for user in users_list:
        user_data = refactor_user_data(user)
        result = False

        if user_data.get('email'):
            if user_data['email'].lower() not in current_users:
                result, user_data = create_user(**user_data, hotel_code=hotel_code)

        if result:
            final_users.append(user_data)

    return final_users


def rollback_users(file_name):
    users_list = []

    with open(f'{INPUT_DIR}/{file_name}', mode='r', encoding='utf-8-sig') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            users_list.append(dict(row))

    for user in users_list:
        user_data = refactor_user_data(user)

        if user_data.get("email"):
            current_user = get_using_entity_and_params("Agencies",
                                                       search_params=[('email', '=', user_data.get("email"))],
                                                       hotel_code=hotel_code)

            if current_user:
                logging.info(f"User {user_data.get('email')} already exists")
                delete_entity("Agencies", current_user[0].id, hotel_code=hotel_code)


def activate_users(file_name):
    users_list = []

    with open(f'{INPUT_DIR}/{file_name}', mode='r', encoding='utf-8-sig') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            users_list.append(dict(row))

    for user in users_list:
        user_data = refactor_user_data(user)

        if user_data.get("email"):
            current_user = get_using_entity_and_params("Agencies",
                                                       search_params=[('email', '=', user_data.get("email"))],
                                                       hotel_code=hotel_code)

            if current_user:
                current_user = current_user[0]
                if not current_user.get('enabled'):
                    logging.info(f"Activating user {user_data.get('email')}")
                    current_user['enabled'] = True
                    save_to_datastore("Agencies", current_user.id, current_user, hotel_code=hotel_code)


def refactor_user_data(user):
    user_data = {
        "agency_licence": user.get("ID_Agencia"),
        "billing_address": user.get("Direccion"),
        "cif": user.get("CIF"),
        "city": user.get("Localidad"),
        "contact_name": user.get("NombreComercial"),
        "email": user.get("Email"),
        "name": user.get("NombreComercial"),
        "pais": user.get("pais"),
        "postal_code": user.get("CodigoPostal"),
        "province": user.get("Provincia"),
        "telephone": user.get("Telefonos"),
        "username": user.get("Email")
    }

    return {key: value for key, value in user_data.items() if value is not None}


def create_user(**kwargs):
    """
    Create a user with the given data
    """
    user_data = {
        "agency_licence": kwargs.get("agency_licence"),
        "billing_address": kwargs.get("billing_address"),
        "cif": kwargs.get("cif"),
        "city": kwargs.get("city"),
        "contact_name": kwargs.get("contact_name"),
        "email": kwargs.get("email"),
        "name": kwargs.get("name"),
        "pais": kwargs.get("pais"),
        "postal_code": kwargs.get("postal_code"),
        "province": kwargs.get("province"),
        "telephone": kwargs.get("telephone"),
        "username": kwargs.get("username"),
        "password": random_password_generator(),
        "hotel_code": kwargs.get('hotel_code'),
        "language": DEFAULT_LANGUAGE,
        "posible_logotype": DEFAULT_LOGOTYPE
    }

    base_url = 'https://hotel-manager-2-dot-admin-hotel.appspot.com'
    base_url = base_url + "/new_agency"

    logging.info("Performing request to the following url: %s" % base_url)
    logging.info("Using the following params: %s" % user_data)

    request_query = requests.post(base_url, data=json.dumps(user_data), headers={'Content-type': 'application/json'})

    logging.info(f'Response POST: {request_query.text}')

    if request_query.status_code == 200:
        return True, user_data


def random_password_generator():
    import random
    import string

    password = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

    return password


def create_login_credentials(final_users):
    for user_data in final_users:
        data = {
            "email": user_data.get('email'),
            "password": user_data.get('password')
        }

        with open(f'{OUTPUT_DIR}/credentials.json', 'a') as json_file:
            json.dump(data, json_file)
            json_file.write('\n')


def generate_url(file_name):
    users_list = []

    with open(f'{OUTPUT_DIR}/{file_name}', 'r') as file:
        datos = json.load(file)
        for element in datos:
            users_list.append(element)

    for user in users_list:
        url = f"https://www.landmarhotels.com/agencias.html?user={user.get('email')}&password={user.get('password')}"

        # Save the url into a txt file
        with open(f'{OUTPUT_DIR}/urls.txt', 'a') as file:
            file.write(url)
            file.write('\n')



if __name__ == '__main__':
    hotel_code = 'landmar'

    current_users = get_using_entity_and_params("Agencies", [], hotel_code=hotel_code)
    current_users = set([x.get('email').lower() for x in current_users])

    file_name = 'Plantilla_usuario_loginunico_AIRMET_23-05-2024.csv'  # Reemplaza con el nombre de tu archivo CSV
    final_users = import_users(file_name, current_users)
    # rollback_users(file_name)
    # activate_users(file_name)
    create_login_credentials(final_users)

    # Crear url acceso
    # file_name_json = 'credentials.json'
    # generate_url(file_name_json)
