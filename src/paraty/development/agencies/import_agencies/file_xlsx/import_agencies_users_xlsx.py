"""
This script imports the users from the old club to the new one.
The existing users are updated and the new ones are created.
It uses the input file from INPUT_DIR and saves the processed users to OUTPUT_DIR.
There are 2 output files: success and error.
The success file contains the users that were successfully imported.
The error file contains the users that were not imported, so they can be processed again.
"""

import datetime
import json
import logging

import numpy as np
import pandas as pd
import requests

from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params

DUMMY_PASSWORD = '__hash__not_supposed_to_be_used'
DEFAULT_LANGUAGE = 'SPANISH'
DEFAULT_LOGOTYPE = 'https://cdn.paraty.es/cancun-bay/images/external/5852_080320240817'
INPUT_DIR = 'input_data'
OUTPUT_DIR = 'output_data'

INPUT_DATA_TYPES = {  # Used to avoid pandas converting the data to the wrong type
    'Identificador': 'string',
    'Tipo código': 'string',
    'Códigos': 'string',
    'Nombre': 'string',
    'Nombre comercial': 'string',
    'Correo electrónico': 'string',
    'Estado': 'string',
    'Tipo': 'string',
    'Es sede': 'string',
    'Sede': 'string',
    'Usa configuración sede': 'string',
    'Grupo': 'string',
    'Comisión': 'Int64',
    'Recibir newsletter': 'string',
    'Persona de contacto': 'string',
    'Teléfono fijo': 'string',
    'Dirección': 'string',
    'Código postal': 'string',
    'Ciudad': 'string',
    'Provincia': 'string',
    'País': 'string',
    'CIF': 'string',
    'Correo electrónico de facturación': 'string',
    'Núm. Reservas': 'Int64',
    'Dirección fiscal': 'string'
}


def _get_user_data(row):
    # Use this function to map the input data to the user data

    # Fill the user data with data from the row
    user_data = {
        "agency_licence": row.get("Códigos"),
        "billing_address": row.get("Dirección fiscal"),
        "cif": row.get("CIF"),
        "city": row.get("Ciudad"),
        "contact_name": row.get("Persona de contacto"),
        "email": row.get("Correo electrónico"),
        "name": row.get("Nombre"),
        "pais": row.get("País"),
        "postal_code": row.get("Código postal"),
        "province": row.get("Provincia"),
        "telephone": row.get("Teléfono fijo"),
        "username": row.get("Correo electrónico")
    }

    return {key: value for key, value in user_data.items() if value is not None}


def create_user(**kwargs):
    """
    Create a user with the given data
    """
    user_data = {
        "agency_licence": kwargs.get("agency_licence"),
        "billing_address": kwargs.get("billing_address"),
        "cif": kwargs.get("cif"),
        "city": kwargs.get("city"),
        "contact_name": kwargs.get("contact_name"),
        "email": kwargs.get("email"),
        "name": kwargs.get("name"),
        "pais": kwargs.get("pais"),
        "postal_code": kwargs.get("postal_code"),
        "province": kwargs.get("province"),
        "telephone": kwargs.get("telephone"),
        "username": kwargs.get("username"),
        "password": DUMMY_PASSWORD,
        "hotel_code": kwargs.get('hotel_code'),
        "language": DEFAULT_LANGUAGE,
        "posible_logotype": DEFAULT_LOGOTYPE
    }

    base_url = 'https://hotel-manager-2-dot-admin-hotel.appspot.com'
    base_url = base_url + "/new_agency"

    logging.info("Performing request to the following url: %s" % base_url)
    logging.info("Using the following params: %s" % user_data)

    request_query = requests.post(base_url, data=json.dumps(user_data), headers={'Content-type': 'application/json'})

    logging.info(f'Response POST: {request_query.text}')

    if request_query.status_code == 200:
        return True


def save_processed_users(success, error, file_name):
    # Save the processed users to a file
    success_df = pd.DataFrame(success)
    error_df = pd.DataFrame(error)

    success_df.to_excel(f'{OUTPUT_DIR}/success_{file_name}', index=False)
    error_df.to_excel(f'{OUTPUT_DIR}/error_{file_name}', index=False)


def import_users(current_users, file_name, hotel_code):
    df = pd.read_excel(f'{INPUT_DIR}/{file_name}', dtype=INPUT_DATA_TYPES)
    df = df.replace({np.nan: None})
    success = []
    error = []

    rows_num = len(df)

    for index, row in df.iterrows():
        result = False
        user_data = {}
        try:
            user_data = _get_user_data(row)

            if user_data.get('email'):
                if user_data['email'].lower() not in current_users:
                    result = create_user(**user_data, hotel_code=hotel_code)

        except Exception as e:
            print(f'Error processing user: {index}')
            traceback = make_traceback()
            print(traceback)

        if result:
            success.append(row)
            current_users.add(user_data['email'].lower())
        else:
            error.append(row)

        if index % 10 == 0:
            save_processed_users(success, error, file_name)  # Autosave in case of a mid execution error
            print(f'Index: {index}/{rows_num}. Success: {len(success)}. Error: {len(error)}')

    save_processed_users(success, error, file_name)


if __name__ == '__main__':
    hotel_code = 'cancun-bay'

    current_users = get_using_entity_and_params("Agencies", [], hotel_code=hotel_code)
    current_users = set([x.get('email').lower() for x in current_users])

    import_users(current_users, 'registered_users.xlsx', hotel_code)
