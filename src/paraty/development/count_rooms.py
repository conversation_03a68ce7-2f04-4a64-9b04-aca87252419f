import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel

# Dictionary containing the room keys to track for each hotel
room_keys = {
	"oasishoteles-grandcancun": {
		"ESTANDAR EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICQ1eb9mAsMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu",
		"SUNSET VIEW EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICQ1aSTmgoMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu",
		"OCEAN VIEW EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICQtciznQoMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu"
	},
	"oasishoteles-pyramid": {
		"ESTANDAR EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgIDAwPnPkQoMogEUb2FzaXNob3RlbGVzLXB5cmFtaWQ",
		"SUNSET VIEW EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgIDAgK7YmwoMogEUb2FzaXNob3RlbGVzLXB5cmFtaWQ",
		"OCEAN VIEW EUROSON": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgIDAwIXYiAoMogEUb2FzaXNob3RlbGVzLXB5cmFtaWQ"
	}
}

def get_all_room_keys(room_keys_dict: Dict[str, Dict[str, str]]) -> set:
    """
    Extract all unique room keys from the room_keys dictionary regardless of hotel.
    
    Args:
        room_keys_dict: Dictionary containing hotel codes and their room mappings
        
    Returns:
        Set of unique room keys
    """
    all_room_keys = set()
    for hotel_rooms in room_keys_dict.values():
        all_room_keys.update(hotel_rooms.values())
    return all_room_keys

def count_room_bookings(start_date: str, end_date: str):
    """
    Count bookings for specific rooms across hotels for a given date range.
    Hotel codes are obtained from the room_keys dictionary.
    
    Args:
        start_date: Start date for the reservation period in format 'YYYY-MM-DD'
        end_date: End date for the reservation period in format 'YYYY-MM-DD'
    
    Returns:
        Dict with structure: {
            "counter": {
                "date": {
                    "room_key": count
                }
            }
        }
    """
    
    result = {}
    tracked_room_keys = get_all_room_keys(room_keys)
    
    # Get hotel codes from room_keys dictionary
    hotel_codes = room_keys.keys()

    # Convert string dates to datetime
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
    
    # Create list of all dates in the range
    dates_check = []
    current_date = start_datetime
    while current_date <= end_datetime:
        dates_check.append(current_date)
        current_date += timedelta(days=1)

    # Initialize counters 
    for date in dates_check:
        date_str = date.strftime("%Y-%m-%d")
        if date_str not in result:
            result[date_str] = {key: 0 for key in tracked_room_keys}
    
    for hotel_code in hotel_codes:
        # Get all bookings for the hotel
        all_bookings = get_reservations_of_hotel({"applicationId": hotel_code}, start_date, end_date, include_modified_reservations=True)
        
        # Process each reservation
        for reservation in all_bookings:
            # Get the range the room is booked
            dates_room = []
            start_datetime = datetime.strptime(reservation.get("startDate"), '%Y-%m-%d')
            end_datetime = datetime.strptime(reservation.get("endDate"), '%Y-%m-%d')
            while start_datetime <= end_datetime:
                dates_room.append(start_datetime)
                start_datetime += timedelta(days=1)

            # Get extra info containing room details
            extra_info = json.loads(reservation.get("extraInfo", "{}"))
            
            # Get rooms for the reservation
            if not extra_info.get("shopping_cart_human_read", []):
                rooms = extra_info.get("original_rooms", "{}").values()
            else:
                rooms = extra_info.get("shopping_cart_human_read", []).get("rooms")

            # For each room in the reservation
            for item in rooms:
                # Get room key
                if not item.get("id"):
                    room_key = item.get("id")
                else:
                    room_key = item.get("room_key")

                # If room key is not in tracked room keys, skip
                if not room_key or room_key not in tracked_room_keys:
                    continue
                    
                # For each date in the range
                for date in dates_room:
                    date_str = date.strftime("%Y-%m-%d")

                    # If the date is in the dates to check, increment the counter for this room on this date
                    if date in dates_check:
                        result[date_str][room_key] += 1
    
    return {"counter": result}