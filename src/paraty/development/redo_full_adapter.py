
import sys

import datetime
import time

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager import hotel_manager_utils

sys.path.append('..')



import requests

'''

Search for a given Configuration property in all hotels.

'''


#from common.general import getRealDomain, getConfigurationProperty, chooseHotelAndPerformAction, get_hotels

__author__ = 'nmarin'


class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)






def get_valid_reservations_from_adapter(adapter_name,  timestamp, only_these_hotels, only_this_chain, exclude_this_chain):


	#all_hotels = get_hotels("", onlyValidHotels=False, onlyInProduction=False, ignoreWidgets=False)
	all_hotels = hotel_manager_utils.get_all_valid_hotels()

	cont = 1

	for hotel in all_hotels:
		if not hotel.get("applicationId"):
			continue

		if only_these_hotels and hotel.get("applicationId", "") not in  only_these_hotels:
			continue

		if only_this_chain and only_this_chain not in hotel.get("applicationId", ""):
			continue

		if exclude_this_chain and exclude_this_chain in hotel.get("applicationId", ""):
			continue


		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			print("ERROR Not permission: %s", hotel)


		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			url_get = "http://%s-adapter.appspot.com/redo?hotel=%s&fromDate=%s" % (adapter_name, hotel.get("applicationId"), timestamp)
			print("%s %s", cont, url_get)

			response = requests.get(url_get)
			if response.status_code == 200:
				print(response.content)
				if delay:
					time.sleep(delay)
				cont += 1
			else:
				print('ERROR redo could not be pushed')
				print('')






if __name__ == "__main__":

	'''if len(sys.argv) < 2:
		print
		print "Usage: python force_pull_reservations_full_adapter.py adapter_name"
		print
		sys.exit(1)
		adapter_name = sys.argv[1]
	'''


	adapter_name = "yieldplanet"
	timestamp = '2024-03-14 11:49:01'


	#only_these_hotels = "checkin-flamingo" #insert here the namaspace if wanted separated by lo que quieras
	only_these_hotels = ""

	only_this_chain = ""
	exclude_this_chain = "checkin-"
	#only_this_chain = ""

	delay = 15

	get_valid_reservations_from_adapter(adapter_name, timestamp, only_these_hotels, only_this_chain, exclude_this_chain)


