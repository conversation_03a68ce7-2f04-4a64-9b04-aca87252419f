'''
csv to datastor utilities
'''
import csv

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils


def add_webconfiguration(hotel_code, entity_configuration):

	#1 find it already exists
	config_name = entity_configuration.get("name")
	all_web_configurations = datastore_communicator.get_using_entity_and_params(entity_name="WebConfiguration", keys_only=False, hotel_code=hotel_code)


	for webconfig in all_web_configurations:
		new_entity = dict(webconfig)

		if new_entity.get("name") == config_name:
			print("ERROR: ya existe %s. ELimine o modifique el nombre de la configuración antes" % config_name)
			return

	if entity_configuration:
		ids = [""]*1
		datastore_communicator.save_multiple_entities("WebConfiguration", ids, [entity_configuration], hotel_code=hotel_code)
		hotel_manager_utils.flush_entity_cache(hotel_code, "WebConfiguration")

		print("ALL OK. Num redirections: %s", len(entity_configuration.get("configurations", [])))



def upload_redirections_from_csv(hotel_code, csv_path, delimeter=","):

	'''

	 final_web_configuration = {'configurations': ['/descripcion.html @@ /descripcion1.html', '/descripcion1.html @@ /descripcion12.html',
												  '/email-footerr.html @@ /email-footer.html', '/en/yeah.html @@ /en/yeah1.html'],
							   'name': 'Website redirections',
							   'description': None}
	'''

	redirections_list = []
	with open(csv_path) as file:
		lines = file.readlines()
		for line in lines:
			columns = line.split(delimeter)

			if len(columns) >1:
				origin = columns[0]
				target = columns[1].replace("\n", "")
				redirections_list.append("%s @@ %s" % (origin, target))


	if redirections_list:

		final_web_configuration = {'configurations': redirections_list,
							   'name': 'Website redirections',
							   'description': None}

		add_webconfiguration(hotel_code, final_web_configuration)



if __name__ == '__main__':

	upload_redirections_from_csv("bg-corporativa", "files/bg-corporativa.csv")

