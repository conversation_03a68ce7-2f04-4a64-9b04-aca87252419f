import os
import datetime
from collections import defaultdict

from paraty_commons_3.datastore import datastore_communicator

import csv


def get_posible_login_dangerous(days_ago, secure_countries_name, status_ok, only_this_countries):
    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=days_ago)
    year_before_today = str(year_before_today)

    headers = ["timestamp", "user", "ip", "status", "ip_geolocation"]

    file_name = f'access_users_possible_bad_2.csv'
    f = open(file_name, "w")
    csv_writer = csv.writer(f)
    csv_writer.writerow(headers)

    last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt', [(
        'timestamp', '>', year_before_today)], hotel_code="user-seeker:")



    for entrada in last_logging_attempt:

        ip = entrada['ip']
        geo = entrada.get('ip_geolocation', "UNKNOWN")
        status = entrada.get('status', "UNKNOWN")

        if only_this_countries and geo in only_this_countries:
            pass

        elif geo in secure_countries_name:
            continue

        if status in status_ok:
            csv_writer.writerow([entrada.get(x) for x in headers])

            print(entrada)
            print()






if __name__ == '__main__':

    days_ago = 2
    secure_countries_name =['Spain', 'Portugal', 'Mexico', 'Colombia', 'United States', 'Italy', 'Dominican Republic',
     'Andorra', 'Germany', 'Gibraltar', "Austria", "Greece", "Uruguay"]

    only_this_country = ["Unknown"]
    only_this_country = []

    status_ok = ["login_ok", "password_expired_change_required", "two_factor_required"]

    get_posible_login_dangerous(days_ago, secure_countries_name, status_ok, only_this_country)




