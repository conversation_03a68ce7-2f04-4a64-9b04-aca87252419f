import datetime
import json
import logging
import math

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels



def avalon_external_identifier(extra_info):
	external_identifier = ""
	if extra_info.get("external_identifier"):

		for reserva in extra_info.get("external_identifier"):
			if reserva.get("Reserva") not in external_identifier:
				external_identifier += reserva.get("Reserva") + ", "
	return external_identifier.strip(", ")

def omnibees_external_identifier(extra_info):
	external_identifier = ""
	if extra_info.get("external_identifier"):
		external_identifier = extra_info.get("external_identifier").get("ID")
	return external_identifier


def normalize_external_identifier(reservations, hotel_code):
	resertacions_to_save = []
	ids = []
	for reservation in reservations:

		extra_info = json.loads(reservation.get("extraInfo", "{}"))

		if refresh_old_external or not extra_info.get("external_identifier_for_manager"):
			external_identifier = ""
			if type_adapter == "avalon":
				external_identifier = avalon_external_identifier(extra_info)
			elif type_adapter == "omnibees":
				external_identifier = omnibees_external_identifier(extra_info)

			if external_identifier:
				#add normalization
				extra_info["external_identifier_for_manager"] = external_identifier
				reservation["extraInfo"] = json.dumps(extra_info)

				logging.info("FIXING RESERVATION! %s %s ", reservation.get("identifier"), hotel_code)

				ids.append(reservation.key.id)
				resertacions_to_save.append(reservation)

	if resertacions_to_save:
		new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids, resertacions_to_save, hotel_code=hotel_code)



def update_reservations():

	all_hotels = get_all_valid_hotels()


	for hotel in all_hotels:

		hotel_code = hotel['applicationId']
		if chain_name in hotel["name"].lower():

			from_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
			to_datetime = datetime.datetime.now().strftime("%Y-%m-%d") + " 23:59:59"


			all_reservations = get_reservations_of_hotel(hotel, from_date, to_datetime, include_modified_reservations=False)
			normalize_external_identifier(all_reservations, hotel_code)




if __name__ == "__main__":

	#string contained in name. examples:
	#chain_name = "park royal"
	#chain_name = "oasis hoteles"

	refresh_old_external = False

	chain_name = "cozumel"

	#make a method for each new one!!!
	#type_adapter = "avalon"
	type_adapter = "omnibees"


	update_reservations()
	logging.info("FIN")