import base64
import json
import datetime
from Crypto.Cipher import AES

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.logging.my_gae_logging import logging

ENCRYPTION_KEY = b'j1Bj26rde#%@F$#@'


def encrypt_pending_reservation(request):
	hotels = list(get_all_valid_hotels())
	week_ago = str((datetime.datetime.now() - datetime.timedelta(days=2)).replace(hour=0, minute=0, second=0, microsecond=0))
	hotels_left = len(hotels)
	for hotel in hotels:
		hotel_code = hotel.get('applicationId')
		logging.info(f'Checkin hotel {hotel_code}')
		reservations = datastore_communicator.get_using_entity_and_params('PendingReservation', hotel_code=hotel_code, search_params=[('timestamp', '>', week_ago)])
		for reservation in reservations:
			logging.info(f"Hotel: {hotel_code} - Reservation {reservation.get('identifier')}")
			try:
				encrypt_and_save_reservation(reservation, hotel_code)
			except Exception as e:
				logging.info(f"error encrypting: Hotel: {hotel_code} - Reservation {reservation.get('identifier')}: {e}")
		hotels_left -= 1
		logging.info(f"Finished encrypting {hotel_code} - {hotels_left} hotels left")
	logging.info(f'Finished encypting PendingReservation from {week_ago}')


def encrypt_and_save_reservation(reservation, hotel):
	reservation['personal_details'] = encrypt_data(reservation.get('personal_details', ''))
	reservation['reservation_data'] = encrypt_data(reservation.get('reservation_data', ''))
	datastore_communicator.save_to_datastore("PendingReservation", reservation.id, reservation, hotel_code=hotel)


def encrypt_data(data):
	try:
		if not_encrypted_yet(data):
			encryptor = AES.new(ENCRYPTION_KEY, AES.MODE_CFB)
			encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
			return base64.urlsafe_b64encode(encryptor.iv + encrypted_data).decode("utf-8")
		logging.info(f"Already encrypted")
		return data
	except Exception as e:
		logging.error(f"Error encrypting: {e}")
		return data


def decrypt_data(encrypted_data):
	encrypted_data = base64.urlsafe_b64decode(encrypted_data.encode("utf-8"))
	iv = encrypted_data[:16]
	ciphertext = encrypted_data[16:]

	decryptor = AES.new(ENCRYPTION_KEY, AES.MODE_CFB, iv=iv)
	decrypted_data = decryptor.decrypt(ciphertext)

	return json.loads(decrypted_data.decode("utf-8"))


def not_encrypted_yet(data):
	try:
		json.loads(data)
		return True
	except:
		return False
