import datetime
import requests
import json
import logging

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params

def fixed_user_transaction_timestamp(transaction, all_reservations_list):
    target_transaction_data = [reservation for reservation in all_reservations_list if reservation.get('identifier') == transaction.get('booking_id')]
    if not target_transaction_data:
        return transaction
    target_transaction_data = target_transaction_data[0]
    fixed_timestamp = datetime.datetime.strptime(target_transaction_data.get('endDate'), "%Y-%m-%d").strftime("%d-%m-%Y %H:%M")
    logging.info('Changing transaction timestamp for reservation %s: from %s to %s' % (transaction.get('booking_id'), transaction.get('timestamp'), fixed_timestamp))
    transaction['timestamp'] = fixed_timestamp

    return transaction

def retreive_user_transactions(idmember, hotel_code, reservation_data, all_reservations_list):
    logging.info("Retreiving user transactions, idmember: %s, hotel-code: %s" % (idmember, hotel_code))
    try:
        post_params = {
            'action': 'retreive_user_transactions',
            'idmember': idmember,
            'namespace': hotel_code
        }

        target_endpoint = 'https://loyalty-seeker.appspot.com/transactions/'
        loyalty_response = requests.post(target_endpoint, post_params, timeout=10)
        user_transactions = loyalty_response.text

        if user_transactions:
            user_transactions = json.loads(user_transactions)
            user_transactions = list(filter(lambda x: x.get('booking_id') != reservation_data.get('identifier'), user_transactions))
            user_transactions = list(filter(lambda x: datetime.datetime.strptime(fixed_user_transaction_timestamp(x, all_reservations_list).get('timestamp'), "%d-%m-%Y %H:%M") <= datetime.datetime.strptime(reservation_data.get('endDate'), "%Y-%m-%d"), user_transactions))

            return user_transactions

    except Exception as e:
        logging.info('Something wrong trying to get user transactions')
        return []

def retreive_user_points(idmember, hotel_code, reservation, all_reservations_list):
    user_transactions = retreive_user_transactions(idmember, hotel_code, reservation, all_reservations_list)

    if not user_transactions:
        return 0

    total_points = sum(map(lambda x: float(x.get('amount')), user_transactions))
    user_points = round(total_points, 2)
    return user_points

def retreive_current_reservation_points(reservation):
    date_from = datetime.datetime.strptime(reservation.get('startDate'), "%Y-%m-%d")
    date_to = datetime.datetime.strptime(reservation.get('endDate'), "%Y-%m-%d")
    return (date_to - date_from).days


def fix_extra_info_club(reservation, hotel_code, club_corpo_namespace, all_reservations_list):
    updated_reservation = False
    extra_info = json.loads(reservation.get("extraInfo", "{}"))
    user_data = extra_info.get('clubMember_user_points') and extra_info.get('clubMember_user_points_by_reservation')
    club_hotel_code = club_corpo_namespace if club_corpo_namespace else hotel_code
    if user_data:
        user_email = reservation.get('email')
        user_data = datastore_communicator.get_using_entity_and_params("UserClub", [('email', '=', user_email)], hotel_code=club_hotel_code)
        if not user_data:
            logging.info('Error - user %s not found!!!' % user_email)
            return

        user_data = user_data[0]

        points_in_this_reservations = retreive_current_reservation_points(reservation)
        accumulated_points = retreive_user_points(user_data['idmember'], club_hotel_code, reservation, all_reservations_list)


        logging.info('Accumulated points for reservation %s and user %s: %s' % (reservation.get('identifier'), user_email, accumulated_points + points_in_this_reservations))
        logging.info('Points for current reservation %s and user %s: %s' % (reservation.get('identifier'), user_email, points_in_this_reservations))

        extra_info['clubMember_user_points'] = points_in_this_reservations + accumulated_points
        extra_info['clubMember_user_points_by_reservation'] = points_in_this_reservations
        updated_reservation = True

    if updated_reservation:
        reservation['extraInfo'] = json.dumps(extra_info)
        ids = [reservation.key.id]
        reservations_to_save = [reservation]

        new_ids_generated = datastore_communicator.save_multiple_entities("Reservation", ids, reservations_to_save,
                                                                          hotel_code=hotel_code)
        logging.info("Fixing reservation %s - %s ", reservation.get("identifier"), hotel_code)


def update_reservation_extra_info_club_data(target_application_id, target_namespaces_list, club_corpo_namespace=None):
    if not target_application_id or not target_namespaces_list:
        return

    all_emails = []

    if club_corpo_namespace:
        all_users = get_using_entity_and_params('UserClub', hotel_code=f'{target_application_id}:{club_corpo_namespace}')
        all_emails = [user['email'] for user in all_users]

    for hotel_code in target_namespaces_list:
        if not club_corpo_namespace:
            all_users_by_namespace = get_using_entity_and_params('UserClub',hotel_code=f'{target_application_id}:{hotel_code}')
            all_emails = [user['email'] for user in all_users_by_namespace]

        reservations = get_using_entity_and_params('Reservation', hotel_code=f'{target_application_id}:{hotel_code}')
        all_reservations = [reservation for reservation in reservations if reservation['email'] in all_emails]

        for reservation in all_reservations:
            fix_extra_info_club(reservation, hotel_code, club_corpo_namespace, all_reservations)


if __name__ == "__main__":
    target_application_id = ''
    target_namespaces_list = []
    club_corpo_namespace = ''
    update_reservation_extra_info_club_data(target_application_id, target_namespaces_list, club_corpo_namespace)
    logging.info('FINISHED')
