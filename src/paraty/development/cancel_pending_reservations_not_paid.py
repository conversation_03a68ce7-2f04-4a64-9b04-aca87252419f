import json
import logging

from paraty.development.development_utils import get_all_payments_info_list, _get_total_payed_amount_from_all_sources
from paraty_commons_3 import queue_utils, email_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
import stripe
import requests
from paraty_commons_3.logging.my_gae_logging import logging
from datetime import datetime, timedelta
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_hotel_by_application_id, \
	get_internal_url
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, get_promotions_of_hotel
from pytz import timezone
import re

TIMESTAMP = "2023-03-01 00:00:00"
TO_TIMESTAMP = "2023-12-31 00:00:00"
WEEK = 7

ADMIN_URL = "https://admin-hotel.appspot.com"
USER = "paco"
PASSWORD = "paco"
COBRADOR_BASE_URL = "https://payment-seeker.appspot.com"


def get_expire_hours_link(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "expire_hours_link" in configs and (configs.split(" @@ ")[0] == "expire_hours_link"):
			return float(configs.split(" @@ ")[1])
	return 0


def get_expire_error_link(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "expire_hours_error_link" in configs:
			return float(configs.split(" @@ ")[1])
	return 0


def get_automatic_promotion_pending(pep_paylinks):
	try:
		for configs in pep_paylinks.get("configurations", []):
			if "automatic promotion for pending reservations" in configs:
				return float(configs.split(" @@ ")[1].split(";")[0].replace("h", "")), configs.split(" @@ ")[1].split(";")[1], float(
					configs.split(" @@ ")[1].split(";")[2].replace("h", ""))
		return 0, "", 0
	except Exception as e:
		return 0, "", 0


def get_automatic_promotion_pending_from_dict(pep_paylinks):
	try:
		automatic_promotion = pep_paylinks.get("automatic promotion for pending reservations")
		if automatic_promotion:
			return float(automatic_promotion.split(";")[0].replace("h", "")), automatic_promotion.split(";")[1], float(automatic_promotion.split(";")[2].replace("h", ""))
		return 0, "", 0
	except Exception as e:
		return 0, "", 0


def cancel_pending_reservations_not_paid(hotel_code=None, hotel_name_filter='oasis', days=6):
	from datetime import datetime
	
	hotel_codes = ([hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter]
	               if hotel_name_filter else ([hotel_code] if hotel_code else get_all_hotels()))
	
	cancelled = {"ok": [], "ko": [], "something_payed": []}
	
	for hotel_code in hotel_codes:
		try:
			pep_paylinks = list(get_using_entity_and_params("IntegrationConfiguration", hotel_code=hotel_code, return_cursor=True,
			                                                search_params=[("name", "=", "PEP_PAYLINKS COBRADOR")]))[0]
		except:
			logging.info(f"Hotel {hotel_code} no tiene configurado PEP_PAYLINKS COBRADOR")
			pep_paylinks = {}
		
		if pep_paylinks:
			expire_hours_link = get_expire_hours_link(pep_paylinks)
			start_date, end_date = get_start_end_date(days)
			reservations = (
					list(get_using_entity_and_params("Reservation", hotel_code=hotel_code, return_cursor=True,
					                                 search_params=[("timestamp", ">", start_date), ("timestamp", "<", end_date)]))
					or logging.info(f"No hay reservas en {hotel_code} en los últimos {days} días")
					or []
			)
			for reservation in reservations:
				today = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
				extra_info = json.loads(reservation.get("extraInfo", ""))
				status_reservation = extra_info.get("status_reservation")
				expired_timestamp = get_fixed_timestamp(reservation, expire_hours_link)
				expired_link = expired_timestamp < today
				nothing_paid = get_nothing_paid(extra_info)
				
				if status_reservation == "pending" and expired_link and nothing_paid and not reservation.get("cancelled"):
					res = cancel_if_reservation_is_not_paid(reservation, hotel_code)
					if res == 200:
						cancelled["ok"].append(reservation.get("identifier"))
					elif res == "ko":
						cancelled["ko"].append(reservation.get("identifier"))
					else:
						cancelled["something_payed"].append(reservation.get("identifier"))
					
					print(cancelled)
	logging.info(cancelled)
	return cancelled


def cancelBooking(reservationKey, applicationId):
	requestString = f'/book?applicationId={applicationId}&reservationKey={str(reservationKey)}&forceCancellation=true'
	
	logging.info(ADMIN_URL + requestString)
	
	response = requests.delete(ADMIN_URL + requestString, auth=(USER, PASSWORD))
	
	logging.info("Response to cancellation from manager: %s" % "")
	
	# TODO Process response
	
	return response.status_code


def cancel_if_reservation_is_not_paid(reservation, hotel):
	hotels = get_all_hotels()
	integration_config = get_integration_configuration_of_hotel(hotels[hotel], "STRIPE COBRADOR")
	if integration_config:
		integration_config = integration_config[0]
		config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
		private_key = config.get("private_key")
		
		client = stripe.http_client.RequestsClient()
		stripe.default_http_client = client
		
		stripe.api_key = private_key
		stripe.api_version = "2020-08-27"
		
		identifier = reservation.get("identifier")
		if "-" in identifier:
			identifier = identifier.split("-")[-1]
		
		customer = stripe.Customer.search(query="name:'%s'" % identifier)
		cancel_reservation = False
		something_payed = False
		if not customer.get("data"):
			cancel_reservation = True
		
		for cus in customer.get("data", []):
			payments = stripe.PaymentIntent.search(query="customer:'%s'" % cus.stripe_id, limit=100)
			
			if not payments.get("data"):
				cancel_reservation = True
			for payment in payments.get("data", []):
				if payment.status == "succeeded":
					import_all = int(payment.get("amount")) / 100
					something_payed = bool(import_all)
					if not import_all:
						cancel_reservation = True
				else:
					cancel_reservation = True
		
		if cancel_reservation and not something_payed:
			print(f"CANCELLING RESERVATION NOT PAID IN {hotel}: {identifier}")
			logging.info(f"CANCELLING RESERVATION NOT PAID IN {hotel}: {identifier}")
			
			if reservation.get("incidents"):
				reservation["incidents"] += " Automatic cancelled"
			else:
				reservation["incidents"] = "Automatic cancelled"
			
			try:
				datastore_communicator.save_to_datastore("Reservation", reservation.id, reservation, hotel_code=hotel)
				# TODO: send to manager
				key_str = id_to_entity_key(hotel, reservation.key)
				cancelled = cancelBooking(key_str, hotel)
				return cancelled
			except:
				return "ko"
		
		if something_payed:
			return "something was payed"


def get_fixed_timestamp(reservation, expire_hours_link):
	from datetime import datetime, timedelta
	
	initial_ts = reservation.get("modificationTimestamp") or reservation.get("timestamp", 0)
	return str(datetime.strptime(initial_ts, "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link))


def get_start_end_date(days):
	end_date = datetime.now() - timedelta(days=days)
	start_date = end_date - timedelta(days=WEEK)
	return str(start_date).split(" ")[0], str(end_date).split(" ")[0]


def get_start_end_date_add_days(hours):
	start_date = datetime.now() - timedelta(hours=hours)
	end_date = datetime.now() + timedelta(days=1)
	return str(start_date).split(" ")[0], str(end_date).split(" ")[0]


def get_reservation_by_identifier(hotel_code, reservation_with_failed_payment):
	reservation_identifier = reservation_with_failed_payment.get('reservation_identifier')
	search_params = [("identifier", "=", reservation_identifier)]
	reservation = list(get_using_entity_and_params("Reservation", hotel_code=hotel_code, return_cursor=True,
	                                               search_params=search_params))
	return reservation


def get_start_end_date_link_error(expire_hours):
	days = float(expire_hours) / 24
	days = int(days) if days.is_integer() else int(days) + 1
	end_date = datetime.now() + timedelta(days=days)
	start_date = end_date - timedelta(days=days + 1)
	return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')


def get_fixed_payment_link_send_date(reservation, expire_hours_link):
	extra_info = {
	}
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
	expire_hours_link_num = float(expire_hours_link)
	initial_ts = extra_info.get("payment_link_send_date")
	if initial_ts:
		return datetime.strptime(initial_ts, "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link_num)
	return None


def get_fixed_payment_error_link_send_date(reservation, expire_hours_link_error):
	extra_info = {
	}
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
	expire_hours_link_num = float(expire_hours_link_error)
	initial_ts = extra_info.get("payment_link_paid_error_send_date")
	if initial_ts:
		return datetime.strptime(initial_ts, "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link_num)
	return None


def calculate_expiry_datetime_payment_link(reservation, expire_hours_link_error):
	failed_payment_link_date_str = reservation.get('failed_payment_link_date')
	if failed_payment_link_date_str is None:
		return None
	
	failed_payment_link_date = datetime.strptime(failed_payment_link_date_str, "%Y-%m-%d %H:%M:%S")
	expiry_datetime = failed_payment_link_date + timedelta(hours=expire_hours_link_error)
	
	return expiry_datetime


# @managers_cache(hotel_code_provider=lambda f, a, k: "get_all_valid_hotels_pep_paylinks", entities='IntegrationConfiguration', ttl_seconds=6 * 3600, background_refresh=False)
def get_all_valid_hotels_pep_paylinks():
	# Returns all the Hotels that are enabled and in production and container pep paylinks cobrador
	hotels = get_all_hotels()
	hotels_aux = [x for x in list(hotels.values()) if x.get('enabled') and x.get('inProduction')]
	hotel_result = []
	hotels_without_pep_paylinks = []
	for hotel in hotels_aux:
		if is_valid_hotel_pep_paylink(hotel.get("applicationId")):
			hotel_result.append(hotel)
		else:
			hotels_without_pep_paylinks.append(hotel.get("applicationId"))
	logging.info(f"Hotels without PEP_PAYLINKS COBRADOR: {hotels_without_pep_paylinks}")
	return hotel_result


# refresh_entity_timestamps("IntegrationConfiguration", hotel.get("applicationId"))
@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='IntegrationConfiguration', ttl_seconds=24 * 3600, background_refresh=False)
def is_valid_hotel_pep_paylink(hotel_code):
	pep_paylinks = _get_integration_configuration_properties("PEP_PAYLINKS COBRADOR", hotel_code)
	if pep_paylinks and len(pep_paylinks) > 0:
		automatic_cancel_pending = pep_paylinks.get('automatic_cancell_pending', None)
		automatic_cancel_error_link = pep_paylinks.get('automatic_cancel_error_link', None)
		automatic_promotion, promotion, entry_time_limit = get_automatic_promotion_pending_from_dict(pep_paylinks)
		if automatic_cancel_pending or automatic_promotion or automatic_cancel_error_link:
			logging.info(f"Hotel {hotel_code} is valid for PEP_PAYLINKS COBRADOR")
			return True
	return False


def _get_all_integration_configuration(hotel_code: str):
	all_integrations_configurations = get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel_code)
	return all_integrations_configurations


def _get_integration_configuration_properties(integration_name: str, hotel_code: str) -> dict:
	all_integration_configs = _get_all_integration_configuration(hotel_code)
	integrationConfig = [x for x in all_integration_configs if x['name'] == integration_name]

	if integrationConfig and len(integrationConfig) > 0:
		result = integrationConfig[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('configurations', [])}
		return properties

	return {}


def cancel_reservations_expired_link():
	hotels = get_all_valid_hotels_pep_paylinks()
	
	for hotel in hotels:
		CLOUD_FUNCTIONS_URL = "https://europe-west1-build-tools-2.cloudfunctions.net"
		timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
		hotel_code = hotel['applicationId']
		
		logging.info(f"cancel_reservations_hotel_{hotel_code}_{timestamp}")
		# cancel_reservations_hotel(hotel_code)
		
		queue_utils.create_task_with_url_get_target("cancel-pending-reservations-expired-link",
		                                            f'{CLOUD_FUNCTIONS_URL}/cancel_reservations_expired_link_task?hotel_code={hotel_code}',
		                                            f"cancel_reservations_expired_link_task_{hotel_code}_{timestamp}")

	return "OK"


def cancel_reservations_hotel(hotel_code):
	pep_paylinks = list(
		get_using_entity_and_params("IntegrationConfiguration", hotel_code=hotel_code, return_cursor=True,
		                            search_params=[("name", "=", "PEP_PAYLINKS COBRADOR")]))

	if not pep_paylinks:
		logging.info(f"Hotel {hotel_code} doesnt have PEP_PAYLINKS COBRADOR")
		return "OK"
	automatic_promotion, promotion, entry_time_limit = get_automatic_promotion_pending(pep_paylinks[0])
	pep_configurations = {
		'expire_hours_link': get_expire_hours_link(pep_paylinks[0]),
		'expire_hours_error_link': get_expire_error_link(pep_paylinks[0]),
		'automatic_cancell_pending': get_automatic_cancel_pending(pep_paylinks[0]),
		'automatic_cancel_error_link': get_automatic_cancel_error_link(pep_paylinks[0]),
		'send_email_cancellation': get_send_email_cancellation(pep_paylinks[0]),
		'alert_expire_hours_link': get_pep_configuration(pep_paylinks[0], 'alert_expire_hours_link'),
		'alert_with_automatic_cancellation': get_pep_configuration(pep_paylinks[0], 'alert_with_automatic_cancellation'),
		'automatic_promotion': automatic_promotion,
		'promotion': promotion,
		'entry_time_limit': entry_time_limit,
		'allow_cancel_with_pending_payment': get_allow_cancel_with_pending_payment(pep_paylinks[0])
	}
	
	if pep_configurations.get('automatic_cancell_pending') or automatic_promotion:
		cancel_pending_reservations_hotel(hotel_code, pep_configurations)
	
	if pep_configurations.get('automatic_cancel_error_link'):
		cancel_reservations_link_error(hotel_code, pep_configurations.get('automatic_cancel_error_link'), pep_configurations.get('expire_hours_error_link'))
	
	return "Ok"


def cancel_pending_reservations_hotel(hotel_code, pep_configurations):
	logging.info(f"cancel_pending_reservations_hotel_{hotel_code}")
	expire_hours_link = pep_configurations.get('expire_hours_link')

	start_date, end_date = get_start_end_date_add_days(hours=float(expire_hours_link) + 24)
	reservations = list(get_using_entity_and_params("Reservation", hotel_code=hotel_code, return_cursor=True,
	                                                search_params=[("timestamp", ">=", start_date), ("timestamp", "<", end_date)]))

	if pep_configurations.get('allow_cancel_with_pending_payment'):
		reservations_link_sent_not_paid = get_reservations_link_sent_not_paid(hotel_code, end_date, expire_hours_link)
		reservations = combine_lists(reservations, reservations_link_sent_not_paid)

	if not reservations:
		logging.info(f"No hay reservas en {hotel_code} en los últimos {1} días")
		return "ok"

	automatic_promotion = pep_configurations.get('automatic_promotion')
	alert_expire_hours_link = float(pep_configurations.get('alert_expire_hours_link')) if pep_configurations.get('alert_expire_hours_link') else None

	today = get_hotel_datetime(hotel_code)
	for reservation in reservations:
		expired_timestamp = get_fixed_payment_link_send_date(reservation, expire_hours_link)
		logging.info(f"Checking reservation {reservation.get('identifier')} expired_timestamp: {expired_timestamp} --->>>>> now: {today.strftime('%Y-%m-%d %H:%M:%S')}")
		extra_info = json.loads(reservation.get("extraInfo", ""))
		status_reservation = extra_info.get("status_reservation")
		nothing_paid = get_nothing_paid(extra_info) or pep_configurations.get('allow_cancel_with_pending_payment')
		reservation_in_time_limit = get_reservation_in_time_limit(reservation.get("startDate"), pep_configurations.get('entry_time_limit'))

		if extra_info.get('SIBS_MULTIBANCO'):
			logging.info(f"Not cancelling because it's SIBS_MULTIBANCO")
			continue

		if fully_paid(reservation, extra_info):
			logging.info(f"Not cancelling because it's fully paid")
			continue

		if automatic_promotion and reservation_in_time_limit:
			if process_automatic_promotion(reservation, automatic_promotion, today, extra_info, status_reservation, nothing_paid, hotel_code, pep_configurations):
				logging.info(f"Not cancelling because automatic_promotion is True and reservation_in_time_limit is True and process_automatic_promotion is True")
				continue

		if not pep_configurations.get('automatic_cancell_pending'):
			logging.info(f"Not cancelling because automatic_cancell_pending is False")
			continue
		
		if not expired_timestamp:
			logging.info(f"Not cancelling because expired_timestamp is None")
			continue

		expired_link = expired_timestamp < today
		almost_expired_link = False
		if alert_expire_hours_link:
			almost_expired_link = expired_timestamp < today + timedelta(hours=alert_expire_hours_link)

		logging.info(
			f"Reservations {reservation.get('identifier')}  --->>>> expired_timestamp: {expired_timestamp} --->>>>>   now:   {today.strftime('%Y-%m-%d %H:%M:%S')}")
		
		if status_reservation == "pending" and nothing_paid and not reservation.get("cancelled"):
			logging.info(f"Reservation {reservation.get('identifier')} is pending, nothing paid and not cancelled")
			if expired_link:
				logging.info(f"Reservation {reservation.get('identifier')} link is expired")
				cancel_reservation_and_send_email(hotel_code, reservation, pep_configurations)
			elif alert_expire_hours_link and almost_expired_link and not extra_info.get("alert_expire_hours_link_sent"):
				logging.info(f"Reservation {reservation.get('identifier')} link is almost expired")
				f_send_email_cacellation_to_customer(hotel_code, reservation, 'alert_expire_hours_link')
	
	return "OK"


def fully_paid(reservation, extra_info):
	total_paid = _get_total_payed_amount_from_all_sources(extra_info)
	total_price = float(reservation.get('price', 0)) + float(reservation.get('priceSupplements', 0))
	if abs(total_price - total_paid) > 1:
		return False
	return True


def process_automatic_promotion(reservation, automatic_promotion, today, extra_info, status_reservation, nothing_paid, hotel_code, pep_configurations):
	expired_timestamp_automatic_promotion = get_fixed_payment_link_send_date(reservation, automatic_promotion)
	if not expired_timestamp_automatic_promotion:
		return True

	expired_link_mail = expired_timestamp_automatic_promotion < today
	cancell = reservation.get("cancelled")
	send_email_link = extra_info.get("send_email_link_for_promotion")

	if not send_email_link and status_reservation == "pending" and expired_link_mail and nothing_paid and not cancell:
		send_mails_automatic_promotion_for_pending(hotel_code, reservation, pep_configurations.get('promotion'))
		return True

	return False


def cancel_reservation_and_send_email(hotel_code, reservation, pep_configurations):
	res = cancel_if_reservation_is_not_expired(reservation, hotel_code)
	if res == 200:
		if pep_configurations.get('alert_with_automatic_cancellation'):
			f_send_email_cacellation_to_customer(hotel_code, reservation, 'alert_with_automatic_cancellation')
		else:
			f_send_email_cancellation(hotel_code, reservation)
		send_internal_email(hotel_code, reservation)
		logging.info(f"OK: cancel reservation {reservation.get('identifier')}")
	elif res == "ko":
		logging.info(f"KO: cancel reservation {reservation.get('identifier')}")
	else:
		logging.info(f"KO: cancel reservation {reservation.get('identifier')}")


def send_internal_email(hotel_code, reservation):
	if 'prinsotel' in hotel_code:
		m = f'Reservation {hotel_code} {reservation.get("identifier")} cancelled automatically'
		emails_to_notify = "<EMAIL>,<EMAIL>,<EMAIL>"
		email_utils.sendEmail(emails_to_notify, m, "", m, send_default_bcc=False)


def f_send_email_cacellation_to_customer(hotel_code, reservation, action):
	logging.info(f"send_email_cacellation_to_customer Hotel: {hotel_code} Action: {action} Reservation: {reservation.get('identifier')}")
	url = COBRADOR_BASE_URL + f"/pages/cobrador/send_mail_for_pending_reservation?hotel_code={hotel_code}&identifier={reservation.get('identifier')}&action={action}"
	requests.post(url)


def f_send_email_cancellation(hotel_code, reservation):
	base_url = get_internal_url(hotel_code)
	url = "%s/utils" % base_url
	params = {
		"action": "sendCancelReservation",
		"localizador": reservation.get("identifier"),
		"email": reservation.get("email"),
		"language": reservation.get("language")
	}
	requests.get(url, params)


def combine_lists(reservations,reservations_link_sent_not_paid):
	combined_dict = {}

	for reservation in reservations_link_sent_not_paid:
		combined_dict[reservation.get('identifier')] = reservation

	for reservation in reservations:
		combined_dict[reservation.get('identifier')] = reservation

	return list(combined_dict.values())


def get_reservations_link_sent_not_paid(hotel_code, end_date, expire_hours_link):
	start_date = datetime.now() - timedelta(hours=float(expire_hours_link) + 24)
	payments = get_all_payments_info_list([("timestamp", ">", start_date),("timestamp", "<", end_date)], hotel_code, only_real_payments=False)
	reservations_link_sent_not_paid = []
	for payment in payments:
		if payment.get('type') == 'Envío de link al cliente':
			reservation = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code, search_params=[('identifier', '=', payment.get('reservation_identifier'))])
			if reservation:
				reservation = reservation[0]
				extra_info = json.loads(reservation.get('extraInfo'))
				if not fully_paid(reservation, extra_info):
					reservations_link_sent_not_paid.append(reservation)
	return reservations_link_sent_not_paid


def get_nothing_paid(extra_info):
	return not float(extra_info.get("payed", 0) or 0) and not extra_info.get("payed_by_tpv_link") and not extra_info.get('payed_by_cobrador')


def cancel_reservations_link_error(hotel_code, automatic_cancel_error_link, expire_hours_link_error):
	logging.info(f"cancel_pending_reservations_hotel_{hotel_code}")
	start_date, end_date = get_start_end_date_link_error(expire_hours_link_error)
	payments_reservations = list(
		get_using_entity_and_params("PaymentsReservation", hotel_code=hotel_code, return_cursor=True,
		                            search_params=[("timestamp", ">", start_date),
		                                           ("timestamp", "<", end_date)]))
	
	reservations_with_failed_payment = [reservation for reservation in payments_reservations if reservation.get('failed_payment_link_date') is not None]
	
	if not reservations_with_failed_payment:
		logging.info(f"No hay reservas con enlace de pago fallido {hotel_code} en las últimas {expire_hours_link_error} horas")
		return "ok"
	
	today = get_hotel_datetime(hotel_code)
	
	for reservation_with_failed_payment in reservations_with_failed_payment:
		expired_error_link = calculate_expiry_datetime_payment_link(reservation_with_failed_payment, expire_hours_link_error)
		if not expired_error_link:
			continue
		
		expired_error = expired_error_link < today
		
		logging.info(
			f"Reservations {reservation_with_failed_payment.get('identifier')} --->>>> expired_error_link: {expired_error_link} --->>>>> now: {today.strftime('%Y-%m-%d %H:%M:%S')}")
		
		reservation = get_reservation_by_identifier(hotel_code, reservation_with_failed_payment)
		if reservation:
			reservation = reservation[0]
		extra_info = json.loads(reservation.get("extraInfo", ""))
		status_reservation = not extra_info.get("status_reservation") and not reservation.get("cancellationTimestamp")
		nothing_paid = not extra_info.get("payed") and not extra_info.get("payed_by_tpv_link") and not extra_info.get(
			'payed_by_cobrador')
		
		if status_reservation and expired_error and nothing_paid and not reservation_with_failed_payment.get("cancelled"):
			
			res = cancel_if_reservation_is_not_expired(reservation, hotel_code)
			
			if res == 200:
				logging.info(f"OK: cancel reservation (error link) {reservation_with_failed_payment.get('identifier')}")
			elif res == "ko":
				logging.info(f"KO: cancel reservation (error link) {reservation_with_failed_payment.get('identifier')}")
			else:
				logging.info(f"KO: cancel reservation (error link) {reservation_with_failed_payment.get('identifier')}")
	
	return "OK"


def cancel_if_reservation_is_not_expired(reservation, hotel):
	identifier = reservation.get("identifier")
	logging.info(f"CANCELLING RESERVATION NOT PAID AND EXPIRED LINK IN {hotel}: {identifier}")
	
	if reservation.get("incidents"):
		reservation["incidents"] += " Automatic cancelled"
	else:
		reservation["incidents"] = "Automatic cancelled"
	try:
		extra_info = json.loads(reservation.get('extraInfo'))
		if extra_info.get('status_reservation'):
			del extra_info['status_reservation']
		reservation['extraInfo'] = json.dumps(extra_info)
	except:
		logging.inf('something went wrong in cancel_if_reservation_is_not_expired')
	
	try:
		datastore_communicator.save_to_datastore("Reservation", reservation.id, reservation, hotel_code=hotel)
		# TODO: send to manager
		key_str = id_to_entity_key(hotel, reservation.key)
		cancelled = cancelBooking(key_str, hotel)
		return cancelled
	except Exception as e:
		logging.error(e.args)
		return "ko"


def get_automatic_cancel_pending(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "automatic_cancell_pending" in configs:
			return configs.split(" @@ ")[1]
	return None


def get_send_email_cancellation(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "send email cancellation with automatic cancellation" in configs:
			return configs.split(" @@ ")[1]
	return None


def get_allow_cancel_with_pending_payment(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "allow automatic cancellation with pending payment" in configs:
			return configs.split(" @@ ")[1]
	return None


def get_pep_configuration(pep_paylinks, configuration):
	for configs in pep_paylinks.get("configurations", []):
		if configuration in configs:
			return configs.split(" @@ ")[1]
	return None


def get_automatic_cancel_error_link(pep_paylinks):
	for configs in pep_paylinks.get("configurations", []):
		if "automatic_cancel_error_link" in configs:
			return configs.split(" @@ ")[1]
	return None


def send_mails_automatic_promotion_for_pending(hotel_code, reservation, promotion_name):
	USER = 'paratytech'
	PASS = 's3cur1tyRul3s@2021!'

	hotel = get_hotel_by_application_id(hotel_code)
	all_promotions = get_promotions_of_hotel(hotel, "SPANISH")
	
	if not promotion_name:
		return "KO"
	
	promotion = [x for x in all_promotions if x.get("identifier") == promotion_name]
	if len(promotion) > 0:
		promotion = promotion[0]
	else:
		return f"HOTEL {hotel_code}  does not have the offer configured"
	
	discount_promo = promotion.get("discount")
	
	discount_promo = discount_promo.replace("%", "")

	logging.info(f"send_mails_automatic_promotion_for_pending Hotel: {hotel_code} Reservation: {reservation.get('identifier')} Promotion: {promotion_name} Discount: {discount_promo}")
	application_promotions_reservation(hotel_code, reservation, promotion)
	action = 'automatic_promotion'
	requestString = f'/pages/cobrador/send_mail_for_pending_reservation?hotel_code={hotel_code}&identifier={reservation.get("identifier")}&discount={discount_promo}&action={action}'
	
	logging.info(COBRADOR_BASE_URL + requestString)
	
	response = requests.post(COBRADOR_BASE_URL + requestString, auth=(USER, PASSWORD))
	
	logging.info(f"send mail promotions: {reservation.get('identifier')}")
	
	# TODO Process response
	
	return response


def application_promotions_reservation(hotel_code, reservation, promotion):
	extra_info = json.loads(reservation.get("extraInfo", ""))
	
	key_promotion = promotion.get("key")
	discount_promo = promotion.get("discount")
	name_promotion = promotion.get("name")
	new_promotion = {
		"name": name_promotion,
		"percentage_value": str(discount_promo)
	}
	if not discount_promo:
		return "KO"
	discount_promo = float(discount_promo.replace("%", ""))
	discount_promotion = discount_promo / 100
	
	shopping_cart_human_read = extra_info.get("shopping_cart_human_read")
	if shopping_cart_human_read:
		for room in shopping_cart_human_read.get("rooms"):
			room.setdefault("promotions_list", []).append(new_promotion)
			room["promotion_name"] = room["promotion_name"] + "+" + name_promotion
			room_price = float(room.get("price"))
			logging.info(f"Room: {room_price} dicount: {discount_promotion}")
			discount_prices = room_price * (discount_promotion)
			prices = room_price - discount_prices
			room["price"] = str(round(prices, 2))
		shopping_cart_human_read_total = float(shopping_cart_human_read.get("total"))
		discount_prices = shopping_cart_human_read_total * (discount_promotion)
		prices = shopping_cart_human_read_total - discount_prices
		shopping_cart_human_read["total"] = str(round(prices, 2))
	
	num_rooms = reservation.get("numRooms")
	for i in range(1, num_rooms + 1, 1):
		index = "price_room_" + str(i)
		if extra_info.get(index):
			discount_price_rooms = extra_info.get(index) * (discount_promotion)
			prices = extra_info.get(index) - discount_price_rooms
			extra_info[index] = round(prices, 2)
	
	shopping_cart = extra_info.get("shopping_cart")
	if shopping_cart:
		for room in shopping_cart:
			discount_prices = float(room.get("price")) * (discount_promotion)
			prices = float(room.get("price")) - discount_prices
			room["price"] = str(round(prices, 2))
	
	new_promotion = {key_promotion: name_promotion}
	if not extra_info.get("real_promotions_name"):
		extra_info["real_promotions_name"] = {}
	
	extra_info["real_promotions_name"].update(new_promotion)
	if not extra_info.get("real_promotions_keys"):
		extra_info["real_promotions_keys"] = []
	extra_info["real_promotions_keys"].append(key_promotion)
	
	price_info = extra_info.get("price_info")
	taxes = float(price_info.get("total_with_all_taxes")) - float(price_info.get("total_with_taxes"))
	discount_prices = price_info.get("total_with_taxes") * (discount_promotion)
	prices = price_info.get("total_with_taxes") - discount_prices
	price_info["total_with_taxes"] = round(prices, 2)
	price_info["total_with_all_taxes"] = round(prices + taxes, 2)
	taxes = float(price_info.get("total_with_all_taxes")) - float(price_info.get("total_without_taxes"))
	price_info["total_without_taxes"] = round(prices - taxes, 2)
	if price_info.get("total_rooms"):
		discount_prices = price_info.get("total_rooms") * (discount_promotion)
		prices = price_info.get("total_rooms") - discount_prices
		price_info["total_rooms"] = round(prices, 2)
	
	extra_info["price_info"] = price_info
	
	prices_per_day = extra_info.get("prices_per_day")
	
	for room_key in prices_per_day:
		for date in prices_per_day[room_key]:
			if date != "total":
				discount_prices = float(prices_per_day[room_key][date][2]) * (discount_promotion)
				prices = float(prices_per_day[room_key][date][2]) - discount_prices
				prices_per_day[room_key][date][1] = str(round(float(prices_per_day[room_key][date][0]) - prices, 2))
				prices_per_day[room_key][date][2] = str(round(prices, 2))
				continue
		discount_prices = prices_per_day[room_key]["total"] * (discount_promotion)
		prices = prices_per_day[room_key]["total"] - discount_prices
		prices_per_day[room_key]["total"] = round(prices, 2)
	extra_info["prices_per_day"] = prices_per_day
	
	discount_prices = float(reservation.get("price")) * (discount_promotion)
	prices = float(reservation.get("price")) - discount_prices
	reservation["price"] = str(round(prices, 2))
	extra_info["send_email_link_for_promotion"] = True
	extra_info_dumps = json.dumps(extra_info)
	reservation["extraInfo"] = extra_info_dumps
	promotions = key_promotion
	if reservation.get("promotions"):
		promotions = reservation.get("promotions") + ";" + key_promotion
	reservation["promotions"] = promotions
	
	reservation_id = int(reservation.key.id)
	datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation,
	                                         hotel_code=hotel_code)
	
	return "OK"


def get_reservation_in_time_limit(start_date_str, entry_time_limit):
	start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
	date_now = datetime.now()
	diff = date_now - start_date
	diff_hours = abs(diff.total_seconds() / 3600)
	return float(entry_time_limit) < diff_hours


def get_hotel_datetime(hotel_code):
	default_tz = 'Europe/Madrid'
	
	time_zone = default_tz
	
	specific_timezone = get_config_property_value(hotel_code, "Specific timezone")
	if specific_timezone:
		time_zone = specific_timezone
	
	my_tz = timezone(time_zone)
	if not my_tz:
		my_tz = timezone(default_tz)
	
	geolocalized_now = datetime.now(my_tz)
	
	timezone_adjust = get_config_property_value(hotel_code, "Timezone adjust")
	
	if timezone_adjust:
		res = re.match("([+-])(\d+)", timezone_adjust)
		if res:
			symbol = res.group(1)
			amount = int(res.group(2))
			
			if symbol == "+":
				geolocalized_now += timedelta(hours=amount)
			else:
				geolocalized_now -= timedelta(hours=amount)
	
	return geolocalized_now.replace(tzinfo=None)


@timed_cache(hours=2)
def get_config_property_value(hotel_code, config):
	return get_config_property_value_without_cache(hotel_code, config)


def get_config_property_value_without_cache(hotel_code, config):
	configurations = datastore_communicator.get_using_entity_and_params('ConfigurationProperty', search_params=[("mainKey", "=", config)],
	                                                                    hotel_code=hotel_code)
	if configurations:
		return configurations[0].get("value")
	
	return None
