import json
import sys

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

sys.path.append('..')
import datetime
import requests

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, get_reservations_of_hotel




__author__ = 'nmarin'

class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)


def get_cancelled_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	for hotel in all_hotels:

		url_push = ""


		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			print("%s adapter found in %s", (adapter_name, hotel["name"]))

			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break


			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):

					for reservation in reservations:

						extra_info = json.loads(reservation.get("extraInfo", "{}"))
						if not extra_info.get("external_identifier"):
							#print("reservstion NOT already sent " + extra_info.get("external_identifier"))
							continue


							print("reservation not cancelled " + extra_info.get("external_identifier"))
							continue

						print(reservation.get("identifier"))
						url_push_reservation = url_push + "&identifier=" +  reservation["identifier"]
						print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								url = hotel.get("url")
								rest_client.update(url + "/rest", 'Reservation', reservation)


							#print "pushing: %s" % url_push_reservation
							#print "pushing: %s" % reservation["identifier"]
							#print



							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								print(response.content)
							else:
								print('ERROR reservation could not be pushed')
								print("")




def get_valid_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	for hotel in all_hotels:

		'''if not hotel.get("applicationId", "") == "soho-vistahermosa-apartamentos":
			continue'''

		if chain_name and not chain_name in hotel["name"].lower():
			continue
		if only_hotel_code and not only_hotel_code == hotel.get("applicationId", ""):
			continue

		url_push = ""



		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			print("%s adapter found in %s", (adapter_name, hotel["name"]))

			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break


			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):
					num_iter =1
					for reservation in reservations:

						extra_info = json.loads(reservation.get("extraInfo", "{}"))

						if reservation.get('comments', '') and '@@@TEST@@@' in reservation.get('comments', '').upper():
							#print("reservstion already sent " + extra_info.get("external_identifier"))
							continue

						#if not reservation.get('cancelled'):

						modified = 'modificationTimestamp' in reservation and reservation['modificationTimestamp']
						cancelled = reservation.get('cancelled', False)
						if cancelled:
							print("Discarting %s because IS  CANCELLED" % reservation["identifier"])
							continue


						if "pciTimestamp" in extra_info:
							# sure is firstime
							print("Discarting %s because IS ALLREADY INTEGRATED" % reservation["identifier"])
							continue



						print(hotel["name"] + ": " + reservation.get("identifier"))
						url_push_reservation = url_push + "&identifier=" + reservation["identifier"]
						#print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								#reservation["cancellationTimestamp"] = None
								datastore_communicator.save_entity(reservation, hotel_code=hotel.get("applicationId", ""))





							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								#print(response.content)
								print(str(num_iter) + "OK!: " + hotel.get("applicationId", "") + " " + reservation["identifier"])

								num_iter += 1

							else:
								print('ERROR reservation could not be pushed '+  reservation["identifier"])
						else:
							print(str(num_iter) + ": PREVIEW " + reservation["startDate"] + " " + hotel.get("applicationId", "") + " " + reservation["identifier"])
							num_iter += 1

if __name__ == "__main__":

	# if len(sys.argv) < 2:
	# 	print
	# 	print "Usage: python force_push_reservations_full_adapter.py adapter_name"
	# 	print
	# 	sys.exit(1)
	#
	# adapter_name = sys.argv[1]

	# string contained in name. examples:
	# chain_name = "park royal"
	# chain_name = "oasis hoteles"

	chain_name = "impressive"
	only_hotel_code = ""

	post_it_really = False
	modify_reservation = False
	adapter_name = "dingus"

	timestamp = '2025-03-05 00:00:00'
	to_datetime = '2025-03-09 10:30:00'
	print("ALL HAS FINISHED")




	get_valid_reservations_from_adapter(adapter_name, timestamp, to_datetime)


