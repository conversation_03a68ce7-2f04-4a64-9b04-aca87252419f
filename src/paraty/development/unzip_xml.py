# -*- coding: utf-8 -*-
import zlib


import base64
raw_data_b64 = r'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'
raw_data_bytes = base64.b64decode(raw_data_b64)

# Decompress
decompressed = zlib.decompress(raw_data_bytes).decode("utf-8")
print(decompressed)


def check_filters_inXml(filters_to_compare, decompressed):
    """
    Verifica si alguno de los filtros coincide con el contenido del XML descomprimido.

    Condiciones:
    1. day: Debe estar entre cualquier EffectiveDate y ExpireDate del XML
    2. roomId, boardId, rateId: Deben estar dentro del XML

    Args:
        filters_to_compare: Lista de diccionarios con filtros a comparar
        decompressed: String con el contenido XML descomprimido

    Returns:
        bool: True si encuentra coincidencias, False en caso contrario
    """
    import xml.etree.ElementTree as ET
    from datetime import datetime

    try:
        # Parsear el XML - manejar encoding
        if isinstance(decompressed, str):
            xml_content = decompressed.encode('utf-8')
        else:
            xml_content = decompressed
        root = ET.fromstring(xml_content)

        # Buscar en cada filtro
        for filter_item in filters_to_compare:
            day = filter_item.get('day')
            room_id = filter_item.get('roomId')
            board_id = filter_item.get('boardId')
            rate_id = filter_item.get('rateId')

            if not all([day, room_id, board_id, rate_id]):
                continue  # Saltar si faltan campos requeridos

            # Condición 1: Verificar que day esté entre EffectiveDate y ExpireDate
            date_valid = False
            try:
                filter_date = datetime.strptime(day, '%Y-%m-%d')

                # Usar búsqueda directa en texto plano para fechas (más confiable)
                import re
                # Buscar patrones EffectiveDate="YYYY-MM-DD" ExpireDate="YYYY-MM-DD"
                rate_pattern = r'EffectiveDate="([^"]+)"[^>]*ExpireDate="([^"]+)"'
                matches = re.findall(rate_pattern, decompressed)

                if matches:
                    for effective_str, expire_str in matches:
                        try:
                            effective_dt = datetime.strptime(effective_str, '%Y-%m-%d')
                            expire_dt = datetime.strptime(expire_str, '%Y-%m-%d')

                            # Verificar si la fecha del filtro está en el rango (inclusive)
                            if effective_dt <= filter_date < expire_dt:  # ExpireDate es exclusivo
                                date_valid = True
                                break
                        except ValueError:
                            continue  # Formato de fecha inválido, continuar

            except ValueError:
                continue  # Formato de fecha del filtro inválido

            if not date_valid:
                continue  # Si la fecha no está en rango, continuar con el siguiente filtro

            # Condición 2: Verificar que roomId, boardId y rateId están en el XML
            room_id_found = False
            board_id_found = False
            rate_id_found = False

            # Buscar en elementos RoomRate
            for room_rate_elem in root.iter():
                if room_rate_elem.tag.endswith('RoomRate'):
                    room_type_code = room_rate_elem.get('RoomTypeCode', '')
                    rate_plan_code = room_rate_elem.get('RatePlanCode', '')

                    # El RoomTypeCode contiene roomId-boardId
                    if str(room_id) in room_type_code:
                        room_id_found = True
                    if str(board_id) in room_type_code:
                        board_id_found = True

                    # El RatePlanCode contiene el rateId
                    if str(rate_id) in rate_plan_code:
                        rate_id_found = True

            # También buscar en texto completo como fallback
            if not room_id_found:
                room_id_found = str(room_id) in decompressed
            if not board_id_found:
                board_id_found = str(board_id) in decompressed
            if not rate_id_found:
                rate_id_found = str(rate_id) in decompressed

            # Si se cumplen ambas condiciones
            if date_valid and room_id_found and board_id_found and rate_id_found:
                return True

        return False

    except (ET.ParseError, UnicodeEncodeError, Exception) as e:
        # Error parseando XML, usando busqueda en texto plano
        # Si no se puede parsear como XML, buscar en texto plano
        for filter_item in filters_to_compare:
            day = filter_item.get('day')
            room_id = filter_item.get('roomId')
            board_id = filter_item.get('boardId')
            rate_id = filter_item.get('rateId')

            if not all([day, room_id, board_id, rate_id]):
                continue

            # Buscar fechas en texto plano
            date_valid = False
            import re
            # Buscar patrones EffectiveDate="YYYY-MM-DD" ExpireDate="YYYY-MM-DD"
            rate_pattern = r'EffectiveDate="([^"]+)"[^>]*ExpireDate="([^"]+)"'
            matches = re.findall(rate_pattern, decompressed)

            if matches:
                try:
                    filter_date = datetime.strptime(day, '%Y-%m-%d')
                    for effective_str, expire_str in matches:
                        try:
                            effective_dt = datetime.strptime(effective_str, '%Y-%m-%d')
                            expire_dt = datetime.strptime(expire_str, '%Y-%m-%d')

                            if effective_dt <= filter_date < expire_dt:
                                date_valid = True
                                break
                        except ValueError:
                            continue
                except ValueError:
                    continue

            # Buscar IDs en texto plano
            room_id_found = str(room_id) in decompressed
            board_id_found = str(board_id) in decompressed
            rate_id_found = str(rate_id) in decompressed

            if date_valid and room_id_found and board_id_found and rate_id_found:
                return True

        return False

def process_json_and_find_xml(filters_to_compare):
    import json

    json_path = "puertobahi_audits.json"
    with open(json_path, 'r') as f:
        content_json = f.read()
    data = json.loads(content_json)
    for item in data:
        if item.get("xml"):
            raw_data_bytes = base64.b64decode(item.get("xml"))
            # Decompress
            decompressed = zlib.decompress(raw_data_bytes).decode("utf-8")
            if check_filters_inXml(filters_to_compare, decompressed):
                print(item.get("timestamp"))
                print(item.get("id"))
                print(decompressed)


if __name__ == '__main__':
    filters_to_compare = [{
          "day": "2025-08-09",
          "roomId": 6443166777475072,
          "boardId": 6043082948608000,
          "rateId": 4585725987127296,
         },
        {
          "day": "2025-08-09",
          "roomId": 6063469614858240,
          "boardId": 6043082948608000,
          "rateId": 4585725987127296,
         }]
    process_json_and_find_xml(filters_to_compare)



