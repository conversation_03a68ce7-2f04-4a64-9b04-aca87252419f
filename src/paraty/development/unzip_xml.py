import zlib


import base64
raw_data_b64 = r'eJztW9tu4jgYvu9TWNnLEeQA4aSAlpNaVtOWAdq5WK1GHjBtNEnM2k5bLvf99qHWzgFCTiRDZ1UkKg1N/B/8n/z579gY8/v+tDK+e+yMnRdk4Q0Cb7bl0E443pWeGdt0ZJkun5ENaZWTKYabKiZPsniQUSAoS70rAIydwhsEV4icqI4rfKUUdeZo6RKTbYHg6dguZQ8O104ZdFZdSZWCaQTvbooVXtIqhtSkFa7P8abgDLKmKHVZUWWfJN4UtcIJ4l8wTYU/oDdWUatK9Y2uQvVi8vIehD48UEQcaKMF/oGckBQn9qaQQLa9wQxZdHw77hvyIT0mN4WUvmKyAovt5gTX3UB9hQnjKhuC16aFhP+/hTMseECk3nVj+ofenuO3m1u91SKBdSHPzmE5y+OAEubTKxk5VjOHdTTAq+3pVXS/6H/zgjpD9A4zcz374ivd6Xp9fa2KYDECuayniguJkNVkRZfAIy83Ezu82qqKBMbLZ+y51ZW04bCt1psDPsqVzxlkLtc6hM4SWRJYmDbiYzYvHE3R9IpSq6jthVrr1PSOpn1S1I6i+EaGs<PERSON>vez/dZ5oHALllG8u4NztDfLqIMk8koyLymSWAy6krTxY0kx7gHGP8wnafhM3QcZIEpMW1Itl2JERdJgbwqHQoJuSG2N9DZ3vEUgiFecS4pqE/gF6ghR1hik8qHs0ZdkhM+7eom4rwR5guRF8h46GlUR5wIPkPKbvHKXG9HkCER9jDiLV7pIuIKj3jrk6LtIh6oenBMHs1dJNW6H8l9XuPxnGFs85xuaSJk8fcod4I5WyYUm3FPkpMkeYB4ENb7WdKbaqOpN9uqomoNva1V9Fq9qbR0rV5TNbXR4A4KqakFHV+grrf0pqa3W01Va2rthgTuXPs7IvdrHhtGU6vj0JB8Q/McTegBYs5b12LmxjIR8eB9FqRUkLrSCG75Clyv0ZKZL0ikO0x1uyLWwfhtY5L4eC3fhb0NA0gRAH0buw4boDUmaAHfulKrUW1x3f54f80Q8YbbelXnK3/oEoKc5daP5/hhVnC2QmHZ28anPB7nFKHAbO5FtdFSIj/NNNPlMjMYcjmjiiVBFlkomLAFZtC6JKyw+l+TMC8LBZQasljMHwMrahlYUb9gxTmV3gUrzixh54gV9Qys0C9YcU6ld8GKM0vYOWKFnoEVjQtWnFPpXbDizBJ2jljRyMCK5olYwWtMUZKlpyr1s6q9dlX/qNV2OjxccrRXeEGE/cpPR4TWBRE+eLVdEOHj5+j/QwRfwxEHsg935PB0J0PBniFriqvU4Qze9DmuxTHjUCQ1x488B4/ELzIB6D+hLy60zPVWHBt6RaOKmvdLSj1SPSWtM+Tjrhne4e0GOmDOIGGxQyZx8yCCzVnWZa10vdasCqiPL3W9rXoQUGap51ZhoVV0sHr1WrVZK79+jy7YrEzkLUmxmZnLKcEbRNh24qyxf/bsG7Vx+Sj+Dp9NWKEbKPk0cRrdlaYeDQwEEfAsZmdoiG0b5VZ4yPJzFRjoEDcoelf3s8n1+A7MxvPx7LHfAX9+HQ/+4jHwiD+j3TuEzzFuR+f+FV77PrikHltfpfPFonfINUPUW27xEGdYHLKHVxUmzspcQoZJ/km0MfXvreRXe8glaunosgh4QfA7865EiujQpQzb/nWW4z/GCC9dkSXAH8T1A0VpaWpL00ZAEkPBzBwwBiZhz35rqLYVpaI0Kmq96NZqTBGh2Ene18iTueY9qi9yaz65iIOZ88Q/OYTuCIV1zV3/HtM1JEvz338g4IkmBIpLIW7shlOuHrmsI3z5WWjzjB2eTPHp33DoSo1aA6gK0HXQUkDhMI5taFo9G357En7A358xEyNVRA3ZpxXT01+tCKLFm5xQ4LPpoJ4hR98KqxiazL+ww5EhfCwsPMWUg7VAXy4eeSleAExcPSH4hcvvn4sbL7YoEr2TNJ5Lvfm0P7kTQLcjFi2j4gng6guuaB4YHy6ObJeFgGjHltmlyCFcpjBcxdE6HYg9xLX4RmqlGpO4bTUZpVqTwifuxE1G3/ZXq/z3R2i5KOeOlWdskVmD9irZq5TuuNLDm52X433VyU1VbkeVjFfQRyW4M3t7SKDDUE6Z7nlof7lEG4ZW+R7v+AuxC4kp3IptbwjJCoiPEJfrSltRa1+Gw8fRV0VrtCWPGm7AB/9Doqo1PjJHxEQ0uKfoMfvPj5NCfyQbQuIGWytEklsdSG5XMfbjkLD38wgslIhhhPlYgiKsyZbuaMnktlbFOqoiPVnZPqtMk1W69Xm3vuddmp6SHc/7tTvv0uuUa3RO7XJ+vsU5rb85pbl5z86mVFvzq//wKNg3FWiayhDyu6uspspvkNIaoWQ3EhKTFF/plSGnfOtAkCLfeBDfbuhdRQbC7+H0/gMhQ+ua'
raw_data_bytes = base64.b64decode(raw_data_b64)

# Decompress
decompressed = zlib.decompress(raw_data_bytes).decode("utf-8")
print(decompressed)



def process_json_and_find_xml(filters_to_compare):
    import json

    json_path = "puertobahi_audits.json"
    content_json = json.loads(json_path)
    with open(json_path, 'r') as f:
        content_json = f.read()
    data = json.loads(content_json)
    for item in data:
        if item.get("xml"):
            raw_data_bytes = base64.b64decode(item.get("xml"))
            # Decompress
            decompressed = zlib.decompress(raw_data_bytes).decode("utf-8")
            if check_filters_inXml():
                print(item.get("xml"))
                print(decompressed)


if __name__ == '__main__':
    filters_to_compare = ["<RatePlanCode>BAR</RatePlanCode>"]
    process_json_and_find_xml(filters_to_compare)

item.get("xml")

