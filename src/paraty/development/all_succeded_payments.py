import json

import stripe
from datetime import datetime, timedelta

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_hotel_by_application_id

IDENTIFIERS = []
USED_KEYS = []
ALL_SUCC = []
# ALL_OASIS = ['oasishoteles-oasispalm']
ALL_OASIS = ['oasishoteles-grandpalm', 'oasishoteles-senscancun', 'oasishoteles-tulum', 'oasishoteles-smart', 	         'oasishoteles-oasispalm', 'oasishoteles-pyramid', 'oasishoteles-grandcancun', 'oasishoteles-demo', 	         'oasishoteles-extras', 'oasishoteles-corpo', 'oasishoteles-senstulum', 'oasishoteles-ohurban']

def get_all_payments():
	limit = 100
	payments = []

	payment_intents = stripe.PaymentIntent.search(query="created>********** and created<********** and status: 'succeeded'", limit=100)
	# payment_intents = stripe.PaymentIntent.search(query="metadata['Paraty Order Id']:'98329472'", limit=100)
	while True:

		ALL_SUCC.extend(payment_intents)
		try:
			if payment_intents.get("has_more"):
				try:
					payment_intents = stripe.PaymentIntent.search(query="created>1705078033 and created<1707756433",
					                                              limit=100, page=payment_intents.get("next_page"))
				except:
					payment_intents = ""
			else:
				break
		except:
			pass

	return ALL_SUCC


def get_succeeded_payments():
	all_succeeded = []

	for hotel_code in ALL_OASIS:

		integration_config = get_integration_configuration_of_hotel(get_hotel_by_application_id(hotel_code), "STRIPE COBRADOR")
		if integration_config:
			integration_config = integration_config[0]
			config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
			private_key = config.get("private_key")
			if private_key in USED_KEYS:
				continue
			USED_KEYS.append(private_key)
			client = stripe.http_client.RequestsClient()
			stripe.default_http_client = client

			stripe.api_key = private_key
			stripe.api_version = "2020-08-27"

			all_succeeded.extend(get_all_payments())
	return all_succeeded
	pass


def get_reservation_by_payment_order_id():
	unknown_payment_order_id = []
	all_succ = get_succeeded_payments()
	for orders in all_succ:
		try:
			order = orders.get('charges').get('data')[0].get('metadata', {}).get('Paraty Order Id', '')
		except:
			continue
		exists = False
		for hc in ALL_OASIS:
			reservation = datastore_communicator.get_using_entity_and_params('PaymentsReservation', search_params=[('order', '=', order)], hotel_code=hc) or datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('identifier', '=', order)], hotel_code=hc)
			if reservation:
				exists = True
				break
		if not exists:
			unknown_payment_order_id.append(order)

	return unknown_payment_order_id


def luckily(reservations_to_check):
	print(f"Reservations to check: {reservations_to_check}")

	for hc in ALL_OASIS:
		if len(reservations_to_check) < 1:
			break
		reservas = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('timestamp', '>', "2024-01-01 00:00:00")],
			                                                   hotel_code=hc)
		for r in reservas:

			if len(reservations_to_check) < 1:
				break
			try:
				ei = json.loads(r.get("extraInfo"))
				payed_by_tpv_link = ei.get("payed_by_tpv_link", [])
				for x in payed_by_tpv_link:
					order = x.get("order", "")
					if order in reservations_to_check:
						reservations_to_check.remove(order)
			except:
				continue
	pass
check = []
r = []
for x in r:
	y = datastore_communicator.get_using_entity_and_params('BookingRequestLog',search_params=[('identifier', '=', x)], hotel_code='admin-hotel')
	if len(x) <= 1:
		check.append(x)


reservations_to_check = get_reservation_by_payment_order_id()
reservations_we_must_check_manually = luckily(list(set(reservations_to_check)))
print(f"reservations_we_must_check_manually{reservations_we_must_check_manually}")





