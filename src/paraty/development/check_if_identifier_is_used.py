
import json
from datetime import datetime, timedelta

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.datastore import datastore_communicator
import logging


USED = {"result": "used"}
NOT_USED = {"result": "not_used"}


def check_identifier_used(request, manual=False, identifier='', hotel_code=''):
	if not manual:
		identifier = request.values.get("identifier", '')
		hotel_code = request.values.get("hotel_code", '')

	if hotel_code and not get_integration_configuration_of_hotel(get_hotel_by_application_id(hotel_code), 'siteminder'):
		logging.info(f'Skipping identifier {identifier}, hotel {hotel_code} does not use siteminder ')
		return NOT_USED
	message = f' Identifier: {identifier}'
	message += f' - Hotel Code: {hotel_code}' if hotel_code else ''
	logging.info(message)
	identifier_records = get_BookingRequestLog(identifier) or get_BookingRequestLog(str(identifier.upper()))

	identifier_list = json.loads(json.dumps(identifier_records))
	used_identifier = False
	for item_identifier in identifier_list:
		try:
			reservation_pushed_date = None
			if item_identifier.get("integrationPushTimestamp"):
				item_identifier_timestamp = item_identifier.get("integrationPushTimestamp")
				reservation_pushed_date = datetime.strptime(item_identifier_timestamp, '%Y-%m-%d %H:%M:%S')
			hours_to_check = 48

			if check_push_timestamp(reservation_pushed_date, hours=hours_to_check):
				# if integrationPushTimestamp is less than days_to_check, we assume that the locator is OK!
				used_identifier = False
				break
			else:
				used_identifier = True
				break
		except ValueError as e:
			logging.error(f'Error parsing timestamp: {e}')
	try:
		if not used_identifier:
			audit_records = list(datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent', hotel_code='siteminder-adapter:', return_cursor=True, search_params=[('request_id', '=', identifier)]))
			for record in audit_records:
				if record.get('hotel_code', '') != hotel_code:
					used_identifier = True
					break
	except:
		logging.error(f'Error getting EndpointCallAuditEvent from siteminder')

	result = USED if used_identifier else NOT_USED
	logging.info(f'Identifier {result.get("result")}')
	return result


def check_push_timestamp(reservation_pushed_date, hours=2):
	# if integrationPushTimestamp was less than days ago, we assume it's the same reservation
	if not reservation_pushed_date:
		return True
	date_now = datetime.now() - timedelta(hours=hours)
	return reservation_pushed_date > date_now


def get_BookingRequestLog(identifier):
	return list(datastore_communicator.get_using_entity_and_params('BookingRequestLog', hotel_code='admin-hotel',
		return_cursor=True,
		search_params=[('identifier', '=', identifier)]))


if __name__ == "__main__":
	identifier = '86203636'
	hotel_code = ''
	check_identifier_used("", True, identifier, hotel_code)
