import json

import requests
import threading

from paraty_commons_3.datastore import datastore_communicator
import logging

semaphore = threading.Semaphore(30)
def extract_club_contact_data(hotel_code):
    users_club = datastore_communicator.get_using_entity_and_params("UserClub", [],
                                                       hotel_code=hotel_code)
    final_loyalty_seeker_url = f"https://loyalty-seeker.appspot.com/transactions/?action=get_category&idmember=%s&namespace={hotel_code}&language=SPANISH"

    list_users = []
    if users_club:
        threads = []
        for contact in users_club:
            list_users.append(contact)

            if len(list_users) == 400:
                del list_users[:]

            id_member = contact.get("idmember", '')
            url_to_push = final_loyalty_seeker_url % (id_member)


            thread = threading.Thread(target=make_request_loyalty, args=(url_to_push, contact))
            threads.append(thread)
            thread.start()



def push_contact(contact, hotel_code):
    url_server = "https://paratytech-adapter.appspot.com/pushtech/send_contact?hotel_code=%s" % hotel_code
    initial_count = 0


    # if request_count

    response = requests.post(url_server, json=contact)
    logging.info("Status: %s, message:%s", response.status_code, response.text)

def make_request_loyalty(url, contact):
    with semaphore:
        response = requests.get(url)

        final_loyalty_user = ""
        if response and response.status_code == 200:
            final_loyalty_user = json.loads(response.text)

            data = {
                'email': contact.get("email"),
                'loyalty_member': True,
                'loyalty_level': final_loyalty_user.get("category") if final_loyalty_user.get(
                    "category") else "Premium",
                'loyalty_id': contact.get("idmember", ''),
                'loyalty_member_since': contact.get("timestamp", ''),
                'gdpr_marketing_consent': True,
                'name_first': contact.get("name", ''),
                'name_last': contact.get("surname", ''),
                'city': contact.get("city", ''),
                'user_id': contact.get("email", ''),

            }
            if str(final_loyalty_user.get("actual_level_points")) == "0":
                data["loyalty_points"] = final_loyalty_user.get("actual_level_points")
            print(final_loyalty_user)
            push_contact(data, hotel_code)
        return response

if __name__ == "__main__":
    hotel_code = "psantiago-iii"
    extract_club_contact_data(hotel_code)