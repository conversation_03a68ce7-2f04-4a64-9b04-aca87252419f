import json
import logging
import re

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item, get_hotel_web_config_item, \
    get_integration_configuration_of_hotel
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_all_hotels_with_config(config_name, config_type='advanced', hotels_filter='', include_not_found=False, show_value=True):
    results = {}
    all_valid_hotels = get_all_valid_hotels()

    if hotels_filter:
        all_valid_hotels = list(filter(lambda x: re.match(hotels_filter, x.get('applicationId', '')), all_valid_hotels))
        logging.info(f"Target hotels: {len(all_valid_hotels)}")
        logging.info(json.dumps(sorted([x['applicationId'] for x in all_valid_hotels])))

    params = [(results, x, config_name, config_type, include_not_found, show_value) for x in all_valid_hotels]
    concurrency_utils.execute_in_parallel(_get_configuration_containing_text_in_key, params, max_concurrency=50)
    return dict(sorted(results.items()))


def _get_configuration_containing_text_in_key(all_results, hotel, text, config_type, include_not_found, show_value):
    config = None
    if config_type == 'advanced':
        config = get_hotel_advance_config_item(hotel, text)
        config = config[0].get('value', '') if config else ''
    elif config_type == 'web':
        config = get_hotel_web_config_item(hotel['applicationId'], text)
    elif config_type == 'integration':
        config = get_integration_configuration_of_hotel(hotel, text)

    if config:
        all_results[hotel['applicationId']] = config if show_value else 'Found'
    elif include_not_found:
        all_results[hotel['applicationId']] = 'Not found'


if __name__ == '__main__':
    hotels = get_all_hotels_with_config("Gotrip config",
                                        config_type='web',
                                        hotels_filter='parkroyal-',
                                        include_not_found=True,
                                        show_value=True)
    print(json.dumps(hotels, indent=4))
