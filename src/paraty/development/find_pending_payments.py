import datetime
import json

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
   get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, _get_all_hotels_metadata
import stripe

from paraty_commons_3.logging.my_gae_logging import logging

TIMESTAMP = "2023-03-01 00:00:00"
TO_TIMESTAMP = "2023-12-31 00:00:00"




def find_pending_reservations(identifier, only_this_hotel, stripe_customer_id, hotel_name_filter):
   hotels = get_all_hotels()

   if only_this_hotel:
      hotel_codes = [only_this_hotel]
   else:
      hotel_codes = [hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter]

   payments = None

   for hotel_code in hotel_codes:

      only_payments_ok = []
      import_all = 0

      res_search_param = [('identifier', '=', only_this_identifier)]

      reservations = list(get_using_entity_and_params('PendingReservation', hotel_code=hotel_code, return_cursor=True,
                                                      search_params=res_search_param))
      for reservation in reservations:
         print(reservation)








def get_account_manager(hotel_code):
   my_metadata = filter(lambda x: x['applicationId'] == hotel_code, _get_all_hotels_metadata())
   return next(my_metadata)['accountManager']



only_this_hotel = None
#only_this_hotel = "oasishoteles-grancancun"


only_this_identifier = "********"
#only_this_identifier=None


name_filter = None
name_filter = 'oasis'

#only_this_stripe_customer_id = "cus_P6thuyoXXA37PR"
only_this_stripe_customer_id = None

find_pending_reservations(only_this_identifier, only_this_hotel, only_this_stripe_customer_id, name_filter)








