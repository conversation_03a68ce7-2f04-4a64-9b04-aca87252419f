from paraty_commons_3.datastore import datastore_communicator
import requests

from google.cloud import storage

def _copy_file_in_path_to_other_path(path_from, path_to):

    print("Copying file from {} to {}".format(path_from, path_to))

    # Initialize the client
    storage_client = storage.Client(project="webspeed-seeker")
    
    # Extract bucket name and blob path from the source path
    # Assuming paths are in format: 'gs://bucket_name/path/to/file'
    bucket_name_from = 'cdn.paraty.es'
    blob_path_from = '/'.join(path_from.split('/')[3:])
    
    # Extract bucket name and blob path from the destination path
    bucket_name_to = path_to.split('/')[2]
    blob_path_to = '/'.join(path_to.split('/')[3:])
    
    # Get the source bucket and blob
    source_bucket = storage_client.bucket(bucket_name_from)
    source_blob = source_bucket.blob(blob_path_from)
    
    # Get the destination bucket - this needs to be a bucket object, not a string
    destination_bucket = storage_client.bucket(bucket_name_from)
    
    # Copy the blob
    source_bucket.copy_blob(source_blob, destination_bucket, blob_path_to)

def clean_pictures():
    hotel_code = 'cancun-bay'
    all_pictures = datastore_communicator.get_using_entity_and_params(entity_name='Picture', return_cursor=True, hotel_code=hotel_code)
    for picture in all_pictures:

        if picture['servingUrl'] and 'storage.googleapis' in picture['servingUrl']:
            print("Ignoring %s" % picture['servingUrl'])
            continue

        if picture['servingUrl'] and 'wyndham-bay' in picture['servingUrl']:
            current_url = picture['servingUrl']
            expected_url = current_url.replace('wyndham-bay', 'cancun-bay')
            
            if requests.get(expected_url).status_code != 200:
                _copy_file_in_path_to_other_path(current_url, expected_url)

            picture['servingUrl'] = expected_url
            datastore_communicator.save_entity(picture, hotel_code)


if __name__ == '__main__':
    clean_pictures()
