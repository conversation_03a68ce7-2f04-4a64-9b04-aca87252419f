import json, io

import openpyxl

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.common_data.common_data_provider import get_hotel_all_advance_configs, \
    get_hotel_advance_config_item


def gateway_name_configuration(gtw_word, hotel):

    param_to_find = "Use Payment Gateway"
    hotel_config = get_hotel_advance_config_item(hotel, param_to_find)
    param_to_get = "value"
    config_values = get_advance_hotel_configuration_value(hotel_config, param_to_get)
    if config_values:
        for gatw in config_values:
            if gtw_word.lower() in gatw.lower():
                return gatw
    return ""


def gateway_configuration(gateway_name, hotel, param_to_get="configurations"):

    hotel_config = get_all_integration_configuration_of_hotel(hotel, gateway_name, param_to_get)
    return hotel_config






def get_advance_hotel_configuration_value(hotel_config, name_value: str = None):
    if len(hotel_config) > 0 and (hotel_config[0].get(name_value) or hotel_config.get(name_value)):
        value_list = hotel_config[0][name_value].split(";")
        return value_list


def get_all_integration_configuration_of_hotel(hotel, param_to_find: str, param_to_get: str):
    result_config = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration',
                                                                         hotel_code=hotel['applicationId'])
    for config in result_config:
        if param_to_find in config["name"]:
            return config[param_to_get]
    return None


def find_gtw_elements(gtw_configs, items_to_find):
    founds = []
    result = None
    if gtw_configs:
        for config in gtw_configs:

            name = config.split(" @@ ")[0]
            value = config.split(" @@ ")[1]

            if name in items_to_find:
                founds.append("%s: %s" % (name, value))
        if founds:
            result = ", ".join(founds)

    return result





if __name__ == '__main__':

    gtw_word = "sermepa"
    #items_to_find = ["async_reservation", "url_ok"]
    items_to_find = ["async_reservation"]

    all_valid_hotels = get_all_valid_hotels()
    for hotel in all_valid_hotels:
        if hotel.get("applicationId"):
            hotel_code = hotel["applicationId"]
            gateway_amount_name = gateway_name_configuration(gtw_word, hotel)
            if gateway_amount_name:
                gtw_config = gateway_configuration(gateway_amount_name, hotel)

                result_items = find_gtw_elements(gtw_config, items_to_find)

                if result_items:
                    print("%s %s %s" % (hotel.get("applicationId"), gateway_amount_name, result_items))

