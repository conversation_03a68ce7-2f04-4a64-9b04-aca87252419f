import os
import xlsxwriter
from paraty_commons_3.datastore import datastore_communicator
from datetime import datetime

DESKTOP = os.path.join(os.path.join(os.path.expanduser('~')), 'Desktop')


def compare_configurations(configs: dict, hotel_name: str):
    count = 2

    workbook = xlsxwriter.Workbook(
        os.path.join(DESKTOP, f'Configuraciones avanzadas-{hotel_name}-Web_test_{datetime.now().strftime("%d-%m-%y_%H-%M-%S")}.xlsx'))
    worksheet = workbook.add_worksheet()

    header_format_production = workbook.add_format({
        'bold': 1,
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True,
        'fg_color': '#b0f2c2'})

    header_format_test = workbook.add_format({
        'bold': 1,
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True,
        'fg_color': '#ffe4e1'})

    header_format_legend = workbook.add_format({
        'italic': 1,
        'bold': 1,
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True})

    default_format = workbook.add_format({
        'text_wrap': True,
        'border': 1,
        'border_color': '#abaaaa',
        'valign': 'vcenter'})

    not_equal_format = workbook.add_format({
        'bold': 1,
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True,
        'fg_color': '#b2e2f2'})

    not_found_format = workbook.add_format({
        'bold': 1,
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True,
        'fg_color': '#fabfb7'})

    disabled_format = workbook.add_format({
        'bold': 1,
        'valign': 'vcenter',
        'border': 1,
        'border_color': '#abaaaa',
        'text_wrap': True,
        'fg_color': '#ffda9e'})

    worksheet.set_default_row(25)
    worksheet.set_column(1, 1, 25)
    worksheet.set_column(2, 2, 40)
    worksheet.set_column(4, 4, 25)
    worksheet.set_column(5, 5, 40)

    worksheet.merge_range('A1:C1', 'Web Producción', header_format_production)
    worksheet.merge_range('D1:F1', 'Web Test', header_format_test)

    for production_key, production_value in configs.get('web_production').items():
        worksheet.merge_range(f'A{count}:B{count}', production_key, default_format)
        worksheet.write(f'C{count}', production_value, default_format)
        found_key = False
        for test_key, test_value in configs.get('web_test').items():
            if production_key == test_key:
                worksheet.merge_range(f'D{count}:E{count}', production_key, default_format)
                found_key = True
                if production_value != test_value:
                    worksheet.write(f'F{count}', test_value, not_equal_format)
                else:
                    worksheet.merge_range(f'D{count}:E{count}', test_key, default_format)
                    worksheet.write(f'F{count}', test_value, default_format)

        if not found_key:
            is_disabled = False
            new_key = production_key.replace('-', '').replace('_', '').replace(' ', '').lower()
            for test_key, test_value in configs.get('web_test').items():
                if new_key == test_key.replace('-', '').replace('_', '').replace(' ', '').lower():
                    is_disabled = True
                    if not production_key.startswith('_'):
                        worksheet.merge_range(f'D{count}:E{count}', test_key, disabled_format)
                        worksheet.write(f'F{count}', test_value, default_format)

                    else:
                        worksheet.merge_range(f'A{count}:B{count}', production_key, disabled_format)
                        worksheet.merge_range(f'D{count}:E{count}', test_key, default_format)

                    worksheet.write(f'F{count}', test_value,
                                    not_equal_format) if test_value != production_value else worksheet.write(f'F{count}',
                                                                                                           test_value,
                                                                                                           default_format)

            if not is_disabled:
                worksheet.merge_range(f'A{count}:B{count}', production_key, not_found_format)
                worksheet.write(f'C{count}', production_value, not_found_format)
                worksheet.merge_range(f'D{count}:E{count}', '', not_found_format)
                worksheet.write(f'F{count}', '', not_found_format)
        count += 1

    LEGEND_PARAMS = {
        not_found_format: 'La propiedad no se ha encontrado en la web test',
        disabled_format: 'La propiedad está comentada en la web test o en la web en producción',
        not_equal_format: 'El valor de la propiedad es distinto en la web test'
    }

    add_legend(worksheet=worksheet, idx=count, title_format=header_format_legend, default_format=default_format,
               legend_params=LEGEND_PARAMS)

    workbook.close()


def add_legend(worksheet, idx, default_format, title_format, legend_params):
    worksheet.merge_range(f'A{idx}:C{idx + 3}', '')
    worksheet.merge_range(f'D{idx}:F{idx + 3}', '')

    worksheet.merge_range(f'A{idx + 4}:C{idx + 4}', 'Leyenda', title_format)
    worksheet.merge_range(f'D{idx + 4}:F{idx + 4}', '', default_format)
    idx += 5

    for color, description in legend_params.items():
        worksheet.write(f'A{idx}', '', color)
        worksheet.merge_range(f'B{idx}:C{idx}', description, default_format)
        worksheet.merge_range(f'D{idx}:F{idx}', '', default_format)
        idx += 1


def get_hotel_config(hotel_code: str) -> dict:
    all_configurations = datastore_communicator.get_using_entity_and_params('ConfigurationProperty',
                                                                            hotel_code=hotel_code)
    return dict(sorted({config.get('mainKey'): config.get('value') for config in all_configurations}.items()))


def execute(hotel_codes: list):
    hotel_config = {'web_production': get_hotel_config(hotel_codes[0]), 'web_test': get_hotel_config(hotel_codes[1])}

    compare_configurations(hotel_config, hotel_codes[0])


if __name__ == '__main__':
    # Compara las configuraciones avanzadas de dos hoteles(Usado en renovaciones) y genera un excel con los resultados
    execute(['bg-ebeso', 'test4-copia17'])
