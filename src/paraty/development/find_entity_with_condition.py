from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils


def find_entities_with_condition(hotel_code, entity_name, filter_to_apply):

    all_entities = datastore_communicator.get_using_entity_and_params(entity_name, hotel_code=hotel_code)
    result = [x for x in all_entities if filter_to_apply(x, hotel_code)]
    return result


def _filter_and_process_entity(entity, hotel_code):

    if entity.get('minStay') is not None:
        if type(entity.get('minStay')) is str:
            print("Found entity with str unexpected: %s", dict(entity))
            entity['minStay'] = 0
            datastore_communicator.save_entity(entity, hotel_code)

    if entity.get('maxStay') is not None:
        if type(entity.get('maxStay')) is str:
            print("Found entity with str unexpected: %s", dict(entity))
            entity['maxStay'] = 0
            datastore_communicator.save_entity(entity, hotel_code)

    return True


if __name__ == '__main__':

    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    for hotel in all_hotels:

        if 'hotansa' in hotel['applicationId']:
            print(hotel['applicationId'])
            find_entities_with_condition(hotel['applicationId'], 'PriceIncrease', _filter_and_process_entity)