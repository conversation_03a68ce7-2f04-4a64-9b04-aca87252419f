import os
import sys
import requests

from paraty_commons_3.datastore import datastore_communicator


def main():
    result = datastore_communicator.get_using_entity_and_params("IntegrationPendingTask3", hotel_code="admin-hotel")

    for pending_reservation in result:
        query = {
            "security": "03ndpx0n3dn0s;wne00000s;;;po828ndpwiejdfn",
            "action": "push_bookings",
            "body": pending_reservation["identifier"]
        }
        response = requests.post("https://admin-hotel.appspot.com/tasks/execute", json=query)

        print("%s - %s" % (pending_reservation["identifier"], str(response.status_code)))


if __name__ == "__main__":
    main()
