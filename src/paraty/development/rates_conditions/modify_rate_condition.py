import requests
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels
import concurrent.futures
import logging

SPANISH = """<div><b>
    Errores evidentes en precios</b></div><div>El hotel se reserva el derecho a cancelar cualquier reserva cuyo precio esté afectado por un error técnico o humano claramente identificable como un precio irreal o manifiestamente desproporcionado. 
    En estos casos, se informará debidamente al cliente, ofreciéndole, si es posible, una alternativa ajustada a la tarifa correcta.
</div>"""

ENGLISH = """<div><b>
    Obvious Pricing Errors</b></div><div>
    The hotel reserves the right to cancel any booking where the price has been affected by a technical or human error, clearly identifiable as unreal or grossly disproportionate. 
    In such cases, the customer will be duly informed and, if possible, offered an alternative based on the correct rate.
</div>"""

FRENCH = """<div><b>
    Erreurs évidentes de tarification</b></div><div>
    L'hôtel se réserve le droit d'annuler toute réservation dont le prix résulte d'une erreur technique ou humaine, clairement identifiable comme irréaliste ou manifestement disproportionné. 
    Dans ce cas, le client sera dûment informé et une alternative conforme au tarif correct pourra être proposée, si possible.
</div>"""

GERMAN = """<div><b>
    Offensichtliche Preisfehler</b></div><div>
    Das Hotel behält sich das Recht vor, Buchungen zu stornieren, bei denen der Preis aufgrund eines technischen oder menschlichen Fehlers offensichtlich unrealistisch oder unverhältnismäßig niedrig ist. 
    In solchen Fällen wird der Kunde entsprechend informiert und erhält, wenn möglich, ein alternatives Angebot zum korrekten Tarif.
</div>"""

PORTUGUESE = """<div><b>
    Erros evidentes nos preços</b></div><div>
    O hotel reserva-se o direito de cancelar qualquer reserva cujo preço esteja afetado por um erro técnico ou humano claramente identificável como um valor irreal ou manifestamente desproporcionado. Nestes casos, o cliente será devidamente informado, sendo-lhe oferecida, sempre que possível, uma alternativa ajustada à tarifa correta.
</div>"""

ITALIAN = """<div><b>
    Error evidenti di prezzo</b></div><div>
    L'albergo si riserva il diritto di annullare qualsiasi prenotazione il cui prezzo sia influenzato da un errore tecnico o umano chiaramente identificabile come un valore irrealistico o manifestamente disproporzionato. 
    In questi casi, il cliente sarà informato in modo appropriato e, se possibile, sarà offerto un'alternativa adattata al prezzo corretto.
</div>"""

DUTCH = """<div><b>
    Duidelijke prijsfouten</b></div><div>
    Het hotel behoudt zich het recht voor om elke boeking te annuleren waarin de prijs is beïnvloed door een technisch of menselijk fout die duidelijk herkenbaar is als een onrealistisch of ongepast lage prijs. 
    In dergelijke gevallen wordt de klant op de juiste wijze geïnformeerd en wordt, indien mogelijk, een alternatief aangeboden dat aansluit bij de correcte prijs.
</div>"""

BASQUE = """<div><b>
    Prezioen errore nabariak</b></div><div>
    Hotelek erreserba bat bertan behera uzteko eskubidea gordetzen du, prezioak errore tekniko edo giza batek eragindakoa denean, irreal edo nabarmen desproporzionatua dena argi identifika daitekeenean.
    Kasu horietan, bezeroari behar bezala informazioa emango zaio eta, posible bada, tarifa zuzenera egokitutako alternatiba bat eskainiko zaio.
</div>"""

RUSSIAN = """<div><b>
    Очевидные ошибки в ценах</b></div><div>
    Отель оставляет за собой право отменить любое бронирование, цена которого была затронута технической или человеческой ошибкой, явно идентифицируемой как нереалистичная или явно непропорциональная.
    В таких случаях клиент будет должным образом проинформирован и, если возможно, ему будет предложена альтернатива по правильной цене.
</div>"""

CATALAN = """<div><b>
    Errors evidents en preus</b></div><div>
    L'hotel es reserva el dret a cancel·lar qualsevol reserva el preu de la qual estigui afectat per un error tècnic o humà clarament identificable com a preu irreal o manifestament desproporcionat.
    En aquests casos, s'informarà degudament al client, oferint-li, si és possible, una alternativa ajustada a la tarifa correcta.
</div>"""

FINNISH = """<div><b>
    Ilmeiset hinnoitteluvirheet</b></div><div>
    Hotelli pidättää oikeuden peruuttaa minkä tahansa varauksen, jonka hinta on vaikuttanut teknisestä tai inhimillisestä virheestä, joka on selkeästi tunnistettavissa epärealistiseksi tai kohtuuttomaksi.
    Tällaisissa tapauksissa asiakas ilmoitetaan asianmukaisesti ja tarjotaan mahdollisuuksien mukaan vaihtoehto oikeaan hintaan.
</div>"""

POLISH = """<div><b>
    Oczywiste błędy w cenach</b></div><div>
    Hotel zastrzega sobie prawo do anulowania każdej rezerwacji, której cena została dotknięta błędem technicznym lub ludzkim, wyraźnie identyfikowalnym jako nierealistyczna lub rażąco nieproporcjonalna.
    W takich przypadkach klient zostanie odpowiednio poinformowany i, jeśli to możliwe, otrzyma alternatywną ofertę dostosowaną do prawidłowej stawki.
</div>"""

SWEDISH = """<div><b>
    Uppenbara prisfel</b></div><div>
    Hotellet förbehåller sig rätten att avboka eventuell bokning där priset har påverkats av ett tekniskt eller mänskligt fel som tydligt kan identifieras som orealistiskt eller grovt oproportionerligt.
    I sådana fall kommer kunden att informeras på lämpligt sätt och, om möjligt, erbjudas ett alternativ baserat på korrekt pris.
</div>"""

JAPANESE = """<div><b>
    明らかな価格エラー</b></div><div>
    ホテルは、技術的または人為的なエラーにより、明らかに非現実的または著しく不均衡な価格となった予約をキャンセルする権利を留保します。
    このような場合、お客様には適切に通知され、可能であれば正しい料金に基づく代替案が提供されます。
</div>"""

KOREAN = """<div><b>
    명백한 가격 오류</b></div><div>
    호텔은 기술적 또는 인적 오류로 인해 비현실적이거나 심각하게 불균형한 가격으로 영향을 받은 예약을 취소할 권리를 보유합니다.
    이러한 경우 고객에게 적절히 통지되며, 가능한 경우 올바른 요금을 기준으로 한 대안이 제공됩니다.
</div>"""

CHINESE_TRADITIONAL = """<div><b>
    明顯的價格錯誤</b></div><div>
    酒店保留取消任何因技術或人為錯誤而導致價格明顯不切實際或嚴重失衡的預訂的權利。
    在這種情況下，客戶將被適當地通知，如果可能的話，將提供基於正確價格的替代方案。
</div>"""

CHINESE_SIMPLIFIED = """<div><b>
    明显的价格错误</b></div><div>
    酒店保留取消任何因技术或人为错误而导致价格明显不切实际或严重失衡的预订的权利。
    在这种情况下，客户将被适当地通知，如果可能的话，将提供基于正确价格的替代方案。
</div>"""

TRANSLATIONS = {
    'SPANISH': SPANISH,
    'BASQUE': BASQUE,
    'ENGLISH': ENGLISH,
    'GERMAN': GERMAN,
    'PORTUGUESE': PORTUGUESE,
    'ITALIAN': ITALIAN,
    'FRENCH': FRENCH,
    'DUTCH': DUTCH,
    'RUSSIAN': RUSSIAN,
    'CATALAN': CATALAN,
    'FINNISH': FINNISH,
    'POLISH': POLISH,
    'SWEDISH': SWEDISH,
    'JAPANESE': JAPANESE,
    'KOREAN': KOREAN,
    'CHINESE_TRADITIONAL': CHINESE_TRADITIONAL,
    'CHINESE_SIMPLIFIED': CHINESE_SIMPLIFIED
}

TRANSLATIONS_START = {
    'SPANISH': 'Errores evidentes en precios',
    'BASQUE': 'Prezioen errore nabariak',
    'ENGLISH': 'Obvious Pricing Errors',
    'GERMAN': 'Offensichtliche Preisfehler',
    'PORTUGUESE': 'Erros evidentes nos preços',
    'ITALIAN': 'Error evidenti di prezzo',
    'FRENCH': 'Erreurs évidentes de tarification',
    'DUTCH': 'Duidelijke prijsfouten',
    'RUSSIAN': 'Очевидные ошибки в ценах',
    'CATALAN': 'Errors evidents en preus',
    'FINNISH': 'Ilmeiset hinnoitteluvirheet',
    'POLISH': 'Oczywiste błędy w cenach',
    'SWEDISH': 'Uppenbara prisfel',
    'JAPANESE': '明らかな価格エラー',
    'KOREAN': '명백한 가격 오류',
    'CHINESE_TRADITIONAL': '明顯的價格錯誤',
    'CHINESE_SIMPLIFIED': '明显的价格错误'
}

ENTITY = 'WebPageProperty'
REFRESH_ENTITY_ENDPOINT = 'https://notify-entity-change-lr52ctruda-ew.a.run.app?hotel_code={hotel_code}&entity={entity}&referrer=conditions-script'

def get_hotel_conditions(hotel_code):
    params = [('mainKey', '=', 'rateConditionDescription')]
    conditionsDescriptions = datastore_communicator.get_using_entity_and_params(ENTITY, search_params=params,
                                                                                hotel_code=hotel_code)
    return conditionsDescriptions

def process_hotel(hotel_code):
    try:
        # Search hotel conditions
        hotel_conditions = get_hotel_conditions(hotel_code)
        for condition in hotel_conditions:
            if condition['languageKey'] in TRANSLATIONS:
                translation_text = TRANSLATIONS[condition['languageKey']].strip()
                if condition['value'] == '':
                    condition['value'] = translation_text
                    datastore_communicator.save_to_datastore(ENTITY, condition.id, condition, hotel_code=hotel_code)
                elif TRANSLATIONS_START[condition['languageKey']] not in condition['value']:
                    condition['value'] = condition['value'] + '<br>' + translation_text
                    datastore_communicator.save_to_datastore(ENTITY, condition.id, condition, hotel_code=hotel_code)
                    
        endpoint = REFRESH_ENTITY_ENDPOINT.format(hotel_code=hotel_code, entity=ENTITY)
        requests.get(endpoint)
        logging.info(f"Successfully processed hotel {hotel_code}")
    except Exception as e:
        logging.error(f"Error processing hotel {hotel_code}: {str(e)}")

def modify_rate_condition(hotel_codes=None):
    if hotel_codes is None:
        hotels = get_all_hotels()
        hotel_codes = [x['applicationId'] for x in hotels.values()]
    
    # Process hotels in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        executor.map(process_hotel, hotel_codes)

if __name__ == "__main__":
    modify_rate_condition()
