import base64
import hashlib
import json
import logging
import os
from urllib.parse import urlencode, urlparse, parse_qs

from Crypto.Cipher import AES

from paraty.development.generate_payment_link_encrypt import get_configuration_property_value
from paraty_commons_3.datastore import datastore_communicator

MESSAGE_KEY = b")RF@aTfUjXn2r4u9"

def encrypt_data(data):
	iv = os.urandom(16)
	encryptor = AES.new(MESSAGE_KEY, AES.MODE_CFB, iv)
	encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
	return base64.urlsafe_b64encode(iv + encrypted_data).decode("utf-8")


def build_encrypted_url(url):
	if "from_manager2" not in url:
		parsed_url = urlparse(url)
		query_params = parse_qs(parsed_url.query)
		data = {}
		for key, values in query_params.items():
			data[key] = values[0]
		encrypted_data = encrypt_data(data)
		if parsed_url.netloc:
			domain = '{}://{}'.format(parsed_url.scheme, parsed_url.netloc)
			path = parsed_url.path
			encrypted_url = '{}{}?{}'.format(domain, path, urlencode({"data": encrypted_data}))
		else:
			path = parsed_url.path
			encrypted_url = '{}?{}'.format(path, urlencode({"data": encrypted_data}))
		return encrypted_url
	return url


def generate_confirmation_link_reservation(hotel_code, identifier, email, is_for_manager=True):
	send_confirmation = ''
	try:
		hotel_booking_domain = get_configuration_property_value(hotel_code, 'Dominio booking')

		secure_key_email = hashlib.md5(email.encode('utf-8')).hexdigest()
		reservation_email = email

		if is_for_manager:
			send_confirmation = build_encrypted_url("%s/send-confirmation/?id=%s&type=%s&from_manager2=%s&html=true&namespace=%s" % (
				hotel_booking_domain, identifier, 'manager', secure_key_email, hotel_code))
			logging.info(send_confirmation)
		else:
			send_confirmation = build_encrypted_url("%s/send-confirmation/?id=%s&type=%s&from_manager2=%s&email=%s&html=true&namespace=%s" % (
				hotel_booking_domain, identifier, 'customer', secure_key_email, reservation_email, hotel_code))
			logging.info(send_confirmation)

	except Exception as e:
		logging.error("Something wrong trying to send confirmations emails to manager or customer")
		logging.exception(e)

	return send_confirmation
