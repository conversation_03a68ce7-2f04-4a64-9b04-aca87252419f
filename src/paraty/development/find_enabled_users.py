import os
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from datetime import datetime
import csv
from paraty_commons_3.datastore.datastore_communicator import get_entity


def get_all_hotels():
    all_hotels = datastore_communicator.get_using_entity_and_params('HotelApplication', hotel_code="admin-hotel")
    return all_hotels


def get_all_users(function_filter_enabled_users, function_filter_by_cancelation, params):
    all_hotels = get_all_hotels()
    all_users_access = datastore_communicator.get_using_entity_and_params('ParatyUser', hotel_code="admin-hotel")
    for user_access in all_users_access:
        if function_filter_enabled_users(user_access, params):
            if function_filter_by_cancelation(user_access):
                print(translate_hotel_code(all_hotels, user_access))


def access_enabled_users(user_access, params):
    enabled_user = params[0]
    return user_access.get('enabled') == enabled_user


def user_access_by_allow_cancel_reservation(user_access):
    if user_access.get("configurationMap"):
        for configuration in user_access["configurationMap"]:
            return user_access if "allowCancelReservation" in configuration else ""


def translate_hotel_code(all_hotels, user_accces):
    user_name = user_accces.get("name")

    hotel_info = {"usuario": user_name,
                  "hotel": []}
    if user_accces.get("accesibleApplications"):
        for aplication_id in user_accces["accesibleApplications"]:
            accesible_application = get_entity('HotelApplication', int(aplication_id), 'admin-hotel')
            if accesible_application.get("name"):
                hotel_name = accesible_application["name"]
                hotel_info["hotel"].append(hotel_name)
    return hotel_info



if __name__ == '__main__':

    params = [True]
    function_filter_enabled_users = access_enabled_users
    function_filter_by_cancelation = user_access_by_allow_cancel_reservation
    get_all_users(function_filter_enabled_users, function_filter_by_cancelation, params)
