import os
import re
import time

import pandas as pd
import concurrent.futures
from validate_email import validate_email

from paraty_commons_3.audit_utils import make_traceback

INPUT_DIR = 'input_emails/'
OUTPUT_DIR = 'output_emails/'

EMAILS_TO_AVOID = frozenset({
    '<EMAIL>'
})

SUPPORTED_FILE_EXTENSIONS = {'.xlsx', '.xls', '.csv'}

# Output dataframes
ORIGINAL_EMAILS = 'Original emails'
VALID_EMAILS = 'Valid emails'
INVALID_EMAILS = 'Invalid emails'
UNKNOWN_EMAILS = 'Unknown'
ERRORS_EMAILS = 'Errors'

# Emails status
VALID_STATUS = 'Valid'
INVALID_STATUS = 'Invalid'
UNKNOWN_STATUS = 'Unknown'
ERROR_STATUS = 'Error'

STATUS_TO_DATAFRAME = {
    VALID_STATUS: VALID_EMAILS,
    INVALID_STATUS: INVALID_EMAILS,
    UNKNOWN_STATUS: UNKNOWN_EMAILS,
    ERROR_STATUS: ERRORS_EMAILS
}

THREAD_WORKERS = 100

EMAILS_CACHE = {}


def _validate_email_multithread(email):
    result = {
        'email': email,
        'cached': False
    }

    ts = time.time()
    try:
        if email in EMAILS_CACHE:
            status = EMAILS_CACHE[email]
            result['cached'] = True
        elif email and email not in EMAILS_TO_AVOID:
            is_valid = validate_email(email, verify=True)
            if is_valid:
                status = VALID_STATUS
            else:
                status = INVALID_STATUS if is_valid is False else UNKNOWN_STATUS
        else:
            status = INVALID_STATUS
    except Exception as e:
        print(f'Error validating email: {email}')
        print(make_traceback())
        status = ERROR_STATUS

    te = time.time()
    result['time'] = "%2.2f" % (te - ts)
    result['status'] = status

    EMAILS_CACHE[email] = status

    return result


def _search_email_column(input_df):
    for index, row in input_df.iterrows():
        for col_name in input_df.columns:
            cell_value = row[col_name]
            if type(cell_value) is str and cell_value and validate_email(cell_value):
                return col_name
    return None


def _process_emails_in_parallel(emails):
    processed_emails = {}
    emails_count = len(emails)

    with concurrent.futures.ThreadPoolExecutor(max_workers=THREAD_WORKERS) as executor:
        try:
            futures = [executor.submit(_validate_email_multithread, email) for email in emails]

            for index, future in enumerate(concurrent.futures.as_completed(futures), start=1):
                data = future.result()
                processed_emails[data["email"]] = data["status"]
                message = (f'({index}/{emails_count}) Email: {data["email"]}. '
                           f'Status: {data["status"]}. Time: {data["time"]}')
                if data['cached']:
                    message += ' (cached)'

                print(message)
        except Exception as e:
            print(make_traceback())

    return processed_emails

def _save_filtered_emails_to_file(output_filename, input_df, processed_emails, email_column):
    # Create output dataframes
    output_dfs = {
        ORIGINAL_EMAILS: pd.DataFrame(),
        VALID_EMAILS: pd.DataFrame(),
        INVALID_EMAILS: pd.DataFrame(),
        UNKNOWN_EMAILS: pd.DataFrame(),
        ERRORS_EMAILS: pd.DataFrame()
    }

    # Add rows to corresponding worksheets
    for row_index, row in input_df.iterrows():
        email = row[email_column]
        email_status = processed_emails.get(email)

        if email_status not in STATUS_TO_DATAFRAME:
            email_status = ERROR_STATUS

        output_dfs[ORIGINAL_EMAILS] = pd.concat([output_dfs[ORIGINAL_EMAILS], row.to_frame().T])

        for status, df_name in STATUS_TO_DATAFRAME.items():
            if email_status == status:
                output_dfs[df_name] = pd.concat([output_dfs[df_name], row.to_frame().T])
                break

    # Save output file
    with pd.ExcelWriter(os.path.join(OUTPUT_DIR, output_filename)) as output_writer:
        for sheet_name, df in output_dfs.items():
            df.to_excel(output_writer, sheet_name=sheet_name, index=False)

        print(f"File saved: {output_filename}")


def process_emails_from_file(filename, extension, email_column=None):
    input_df = None
    full_filename = filename + extension
    output_filename = f'filtered_{filename}.xlsx'

    if extension in {'.xlsx', '.xls'}:
        input_df = pd.read_excel(os.path.join(INPUT_DIR, full_filename), na_filter=False)
    elif extension == '.csv':
        input_df = pd.read_csv(os.path.join(INPUT_DIR, full_filename), sep=';', na_filter=False)

    if input_df is not None:
        if email_column is None:
            email_column = _search_email_column(input_df)

        print(f'Email column found: {email_column}')

        if email_column is not None:
            processed_emails = _process_emails_in_parallel(input_df[email_column])
            _save_filtered_emails_to_file(output_filename, input_df, processed_emails, email_column)
        else:
            print(f'No email column found in file: {full_filename}. Email column: {email_column}.')

    else:
        print(f'Error reading file: {full_filename}')


def process_file(full_filename):
    if not full_filename.startswith('_'):
        filename_match = re.search('(.*)(\.[^.]+)$', full_filename)
        if filename_match and len(filename_match.groups()) == 2:
            filename, extension = filename_match.groups()

            if extension in SUPPORTED_FILE_EXTENSIONS:
                print(f'Processing file: {full_filename}')
                process_emails_from_file(filename, extension)
                print('File processed\n\n')
            else:
                print(f'File extension not supported: {full_filename}')
        else:
            print(f'Error processing filename: {full_filename}')
    else:
        print(f'File ignored: {full_filename}')


if __name__ == '__main__':
    input_files = os.listdir(INPUT_DIR)

    for input_filename in input_files:
        process_file(input_filename)
