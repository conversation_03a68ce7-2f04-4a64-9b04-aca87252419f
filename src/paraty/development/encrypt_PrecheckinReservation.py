import datetime

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.security_utils import get_secret, decrypt_data, encrypt_data

try:
	ENCRYPTION_KEY = get_secret('security-seeker', 'prebookings-encrypt', 2).encode('utf-8')
except Exception as e:
	logging.error(f"Error getting encryption key: {e}")
	ENCRYPTION_KEY = None


def get_all_reservations():
	return datastore_communicator.get_using_entity_and_params('PrecheckinReservation', hotel_code="hotel-tools:")


def encrypt_or_decrypt_precheckin_reservations(operation_type="encrypt"):
	faltan_permisos = []
	encryption_key = ENCRYPTION_KEY
	if not encryption_key:
		return "Missing permissions to get encryption key"
	end_date_to_encrypt_to = get_end_date_to_encrypt_to()
	for precheckin in get_all_reservations():
		end_date = ''
		save = False
		identifier = precheckin.get('identifier')
		hotel_code = precheckin.get('hotel_code')
		precheckin_end_date = precheckin.get('end_date')
		if not precheckin_end_date:
			try:
				reservation = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code, search_params=[('identifier', '=', identifier)])
			except Exception as e:
				faltan_permisos.append(hotel_code)
				logging.warning(f"Faltan permisos en {hotel_code}")
				continue

			if len(reservation) > 0:
				reservation = reservation[0]
				end_date = reservation.get('endDate')
				precheckin["end_date"] = end_date
		else:
			end_date = precheckin_end_date
		if end_date and end_date < end_date_to_encrypt_to:
			save = process_reservation(precheckin, operation_type, encryption_key)
			if save:
				save_reservation(precheckin)
		if not precheckin_end_date and not save:
			save_reservation(precheckin)

	logging("Encryption finished")
	return "OK"


def get_end_date_to_encrypt_to():
	return str((datetime.datetime.now() - datetime.timedelta(days=2)).replace(hour=0, minute=0, second=0, microsecond=0))


def process_reservation(reservation, operation_type, encryption_key):
	save = False
	if operation_type == 'decrypt':
		reservation['rooms'] = decrypt_data(reservation.get('rooms'), encryption_key)
		save = True
	elif operation_type == 'encrypt':
		reservation['rooms'], save = encrypt_data(reservation.get('rooms'), encryption_key)
	return save


def save_reservation(reservation):
	datastore_communicator.save_to_datastore("PrecheckinReservation", reservation.id, reservation,hotel_code="hotel-tools:")

