import datetime
import json

from paraty.development.check_payments_stripe import configure_stripe_conection
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
	get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, _get_all_hotels_metadata, \
	get_hotel_by_application_id
import stripe

from paraty_commons_3.logging.my_gae_logging import logging
from datetime import datetime, timedelta

def get_all_payments():
	all_payments = []
	limit = 100  # Puedes ajustar este valor según tus necesidades

	while True:

		payments = stripe.PaymentIntent.list(limit=limit)

		succeded_payments = [x for x in payments.get('data') if x.status == 'succeeded']

		all_payments.extend(succeded_payments)

		# Verifica si hay más páginas
		if not payments.data or len(payments.data) < limit:
			break

		# Actualiza el punto de inicio para la siguiente página
		starting_after = payments.data[-1].id
		payments = stripe.PaymentIntent.list(limit=limit, starting_after=starting_after)

	return all_payments


def check_if_reservation_hasmissing_payments(reservation, hotel_code, hotel_info, fix_missing_payment_link=False,
                                             fix_in_datastore=False):
	id = reservation.get("identifier")
	extra_info = json.loads(reservation.get('extraInfo'))
	original_hotel_code = extra_info.get('origin_hotel_code', '')
	original_hotel_info = get_hotel_by_application_id(original_hotel_code)
	# TODO: Nacho2
	import_paid_in_stripe = 0
	pagado_total_reserva = 0

	origins_hotels = [(id, hotel_info)]

	if "M-" in id:
		origins_hotels.append((id.replace("M-", ""), original_hotel_info))
		logging.info(f"Reservation moved from {original_hotel_code} to {hotel_code}")

	for identifier, hotel_info in origins_hotels:

		integration_config = get_integration_configuration_of_hotel(hotel_info, "STRIPE COBRADOR")
		if integration_config:
			integration_config = integration_config[0]
			config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
			private_key = config.get("private_key")

			client = stripe.http_client.RequestsClient()
			stripe.default_http_client = client

			stripe.api_key = private_key
			stripe.api_version = "2020-08-27"

			same_id = False

			all_succeded_payments = get_all_payments()

			customer = stripe.Customer.search(query="name:'%s'" % identifier)
			for cus in customer.get("data", []):
				payments = stripe.PaymentIntent.search(query="customer:'%s'" % cus.stripe_id)
				for payment in payments.get("data", []):
					if payment.status == "succeeded":
						import_paid_in_stripe += (int(payment.get("amount")) / 100)
					if payments.get("data", [])[0].get('metadata', {}).get('Paraty Order Id', '') == str(identifier):
						same_id = True

				if not same_id and payments.get("data", []):
					pass

		if import_paid_in_stripe:
			new_payed_by_tpv_link = {}
			payed_by_tpv_link = []
			extra_info = json.loads(reservation.get("extraInfo", "{}"))
			paid_by_link = 0
			if extra_info.get("payed_by_tpv_link"):
				payed_by_tpv_link = extra_info.get("payed_by_tpv_link")
				if payed_by_tpv_link:
					for paid_line in payed_by_tpv_link:
						paid_by_link += paid_line.get("amount")

			status_reservation = extra_info.get("status_reservation", "")

			pago_proceso = float(extra_info.get("payed", "0"))
			pagos_cobrador = float(extra_info.get("payed_by_cobrador", "0"))

			pagado_total_reserva += paid_by_link + pago_proceso + pagos_cobrador

	if not pagado_total_reserva == import_paid_in_stripe:
		if fix_missing_payment_link and (import_paid_in_stripe > pagado_total_reserva):
			import_to_add = import_paid_in_stripe - pagado_total_reserva

			new_payed_by_tpv_link = {'order': reservation.get('identifier'), 'amount': import_to_add,
			                         'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
			                         'fix_manual_added': True, "amount": import_to_add, "fix_manual_added": True}

			if not "payed_by_tpv_link" in extra_info:
				extra_info["payed_by_tpv_link"] = []

			extra_info["status_reservation"] = "confirmed"

			if fix_in_datastore:
				reservation["extraInfo"] = json.dumps(extra_info)

				ids = [reservation.key.id]
				resertacions_to_save = [reservation]
				new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids,
				                                                                resertacions_to_save,
				                                                                hotel_code=hotel_code)

			bad_res_info = {
				"hotel": hotel_code,
				"identifier": reservation.get('identifier'),
				"timestamp": reservation.get('timestamp'),
				"startDate": reservation.get('startDate'),
				"endDate": reservation.get('endDate'),
				"Total Reserva": float(reservation.get("price", 0)) + float(reservation.get("priceSupplements", 0)),
				"Cancelled": reservation.get("cancelled"),
				"status_reservation": status_reservation,
				"pagado_total_reserva": pagado_total_reserva,
				"import_paid_in_stripe": import_paid_in_stripe,
				"paid_by_link": paid_by_link,
				"pago_proceso": pago_proceso,
				"pagos_cobrador": pagos_cobrador,
				"paylinks_list": payed_by_tpv_link,
				"FIXED_new_payed_by_tpv_link": new_payed_by_tpv_link,
				"final_payment_list": extra_info.get("payed_by_tpv_link"),
				"FIXED_status_reservation": extra_info.get("status_reservation"),

			}
			if "M-" in identifier:
				bad_res_info['all_hotels'] = f"Moved from {original_hotel_code} to {hotel_code}"

			extra_info["payed_by_tpv_link"].append(new_payed_by_tpv_link)

			return bad_res_info


# return None


def _check_reservations_with_missing_payments(only_this_hotel, only_this_identifier, hotel_name_filter, days=100,
                                              fix_missing_payment_link=False, fix_in_datastore=False):
	bad_reservations = {}
	hotels = get_all_hotels()

	if only_this_hotel:
		hotel_codes = [only_this_hotel]
	else:
		hotel_codes = [hotel for hotel in hotels if hotel_name_filter in hotel.lower() and hotel_name_filter]

	for hotel_code in hotel_codes:
		hotel_info = hotels[hotel_code]
		res_search_param = [('timestamp', '>', (str(datetime.now() - timedelta(days=days))).split(' ')[0])]
		if only_this_identifier:
			res_search_param = [('identifier', '=', only_this_identifier)]

		reservations = list(get_using_entity_and_params('Reservation', hotel_code=hotel_code, return_cursor=True,
		                                                search_params=res_search_param))

		for reservation in reservations:

			if reservation.get("identifier") in EXCLUDED_RESERVATIONS:
				continue

			bad_res_info = check_if_reservation_hasmissing_payments(reservation, hotel_code, hotel_info,
			                                                        fix_missing_payment_link, fix_in_datastore)

			if bad_res_info:
				if hotel_code not in bad_reservations:
					bad_reservations[hotel_code] = []
				bad_reservations[hotel_code].append(bad_res_info)

	return bad_reservations


def get_account_manager(hotel_code):
	my_metadata = filter(lambda x: x['applicationId'] == hotel_code, _get_all_hotels_metadata())
	return next(my_metadata)['accountManager']


only_this_hotel = None
# only_this_hotel = "oasishoteles-grandcancun"

only_this_identifier = None
# only_this_identifier="********"


hotel_name_filter = None
hotel_name_filter = 'oasis'

# EXCLUDED_RESERVATIONS = ["R53405541"]
EXCLUDED_RESERVATIONS = []

days = 20

fix_missing_payment_link = True
fix_in_datastore = False

# res_bad_reservations = _check_reservations_with_missing_payments(only_this_hotel, only_this_identifier,
#                                                                  hotel_name_filter, days, fix_missing_payment_link,
#                                                                  fix_in_datastore)

# print(json.dumps(res_bad_reservations, indent=4))


def get_reservation_by_payment_order_id(all_success_payments):
	hoteles = ['oasishoteles-grandpalm', 'oasishoteles-senscancun', 'oasishoteles-tulum', 'oasishoteles-smart', 'oasishoteles-oasispalm', 'oasishoteles-pyramid', 'oasishoteles-grandcancun', 'oasishoteles-demo', 'oasishoteles-extras', 'oasishoteles-corpo', 'oasishoteles-senstulum', 'oasishoteles-ohurban']
	unknown_payment_order_id = []
	for order in all_success_payments:
		exists = False
		for hc in hoteles:
			reservation = datastore_communicator.get_using_entity_and_params('PaymentsReservation', search_params=[('order', '=', order)], hotel_code=hc) or datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('identifier', '=', order)], hotel_code=hc)
			if reservation:
				exists = True
				break
		if not exists:
			unknown_payment_order_id.append(order)

		pass

	return unknown_payment_order_id

get_reservation_by_payment_order_id(['59794845', '69877065', '42345709', '61160674', '41077644', '22291118', '54616444', '71348301', '71719825', '12832708', '29223164', '46745953', '98459676', '91286988', '37242522', '32108784', '37859687', '93811192', '79197781', '67600351', '51387157', '45669147', '34111009', '18292622', '52703647', '50616700', '82112407', '63083426', '38299307', '94452294', '85102874', '52991229', '14100947', '67601678', '80984358', '15995971', '12535018', '40888907', '94035966', '49112868', '17000651', '21520430', '11286590', '97683103', '36950652', '51758611', '49156629', '31645729', '65455686', '66970176', '24049673', '52040810', '86009472', '74114967', '66081179', '68319981', '91361706', '19686205', '20452324', '42001874', '65424767', '37657660', '87177967', '36608982', '21379988', '32023854', '50031021', '71472032', '25045561', '70690198', '47044577', '77315848', '60812000', '69747119', '65966855', '65229954', '48706895', '52562943', '32365927', '53852663', '65169241', '75967438', '60934750', '11974492', '89931539', '86387258', '65391489', '11407023', '91985716', '17739616', '30354103', '93135676', '44986900', '24652001', '99666263', '90190320', '23274039', '12048226', '99666260', '37675999', '93927294', '67886735', '49635706', '49635706', '53776332', '74401593', '74401593', '21637177', '21637177', '57338329', '73343217', '47411775', '41383343', '85513698', '37261285', '41383343', '85513698', '47411775', '37261285', '99685809', '99685809', '30341573', '10229891', '69427631', '61575103', '90669052', '69427631', '29847656', '61575103', '90669052', '29847656', '48586284', '31544414', '16414637', '74023964', '80973611', '80973611', '85688135', '23553618', '36383360', '45148258', '63879239', '63879239', '88530874', '49809306', '90624295', '88530874', '49809306', '52409394', '90624295', '52409394', '74943973', '86805843', '74943973', '13033917', '32543573', '13033917', '86805843', '32543573', '93588145', '88549972', '93588145', '93588145', '12376288', '44496723', '12376288', '44496723', '25510300', '98016613', '25510300', '98016613', '71301381', '60328099', '13737105', '10014395', '71301381', '60328099', '13737105', '31339765', '93588145', '46722998', '93588145', '86277011', '46722998', '70318678', '86277011', '55421771', '70318678', '55421771', '93588145', '66558780', '84965220', '16645031', '93588145', '66558780', '57369849', '93588145', '84965220', '29005117', '77447757', '57369849', '29005117', '77447757', '42946957', '41406483', '88728578', '88728578', '42946957', '41406483', '71664606', '71664606', '40942169', '79578398', '40942169', '50794287', '79578398', '33441681', '23820521', '50794287', '93329985', '23820521', '33441681', '58380828', '93329985', '21716834', '87670536', '87662640', '59856876', '21716834', '87662640', '87670536', '59856876', '93588145', '94479218', '97571751', '72878289', '94479218', '25069091', '97571751', '72878289', '25069091', '93588145', '93588145', '78762013', '78762013', '50788840', '67330850', '67330850', '67330850', '67330850', '14503694', '14503694', '66786422', '92773166', '66786422', '70699266', '92773166', '70699266', '10555879', '95502338', '10555879', '95502338', '36073078', '36073078', '88206793', '88206793', '85851599', '58367444', '85851599', '48860865', '58367444', '82305197', '48860865', '92194477', '82305197', '69808610', '13722728', '28602176', '92194477', '69808610', '28602176', '13722728', '41285231', '41285231', '79336004', '79336004', '74234569', '74234569', '99643423', '32459054', '58876230', '72334503', '99643423', '72334503', '58876230', '32459054', '51113745', '79687949', '20294303', '76916965', '97697842', '43479986', '20294303', '43479986', '97697842', '30927015', '27682741', '27404027', '99666262', '27404027', '10788254', '44194963', '44194963', '46047448', '35871742', '14426300', '46047448', '35871742', '14426300', '70016117', '26832347', '70016117', '26832347', '25539439', '75620158', '25539439', '75620158', '71421798', '71421798', '41927580', '21660492', '11374882', '85810374', '10709462', '27917783', '43807050', '10709462', '85810374', '27917783', '21660492', '43807050', '94825018', '64541235', '94825018', '64541235', '47575111', '47575111', '95777917', '94605510', '59804201', '27364940', '94818390', '94818390', '53921832', '53921832', '57158562', '68862355', '78171040', '67324607', '68862355', '57158562', '67324607', '78171040', '88615131', '22859610', '88615131', '10026984', '22859610', '10026984', '51736525', '18018516', '21404376', '51736525', '21404376', '18018516', '64177434', '64177434', '95363941', '58396004', '65631256', '95363941', '58396004', '65631256', '11252035', '11252035', '41113607', '16129553', '41113607', '16129553', '67778709', '18642920', '18642920', '67778709', '86394610', '86394610', '87557222', '40307189', '71070807', '32945373', '32945373', '40307189', '87557222', '71070807', '86080795', '88895065', '34811655', '86080795', '88895065', '77318510', '34811655', '77318510', '59006831', '13545038', '43208330', '67058366', '13545038', '59006831', '43208330', '67058366', '55718283', '55748535', '35977636', '90826004', '81039158', '81039158', '55748535', '35977636', '90826004', '75607851', '29575161', '75607851', '29575161', '78615930', '78615930', '24890235', '44356127', '24890235', '16502690', '35604291', '44356127', '16502690', '27593309', '35604291', '27593309', '53206722', '69901162', '88132307', '69901162', '84935924', '40196327', '88132307', '84935924', '40196327', '62539699', '31452304', '62539699', '41546886', '31452304', '74131744', '24308940', '59458934', '41546886', '69744001', '24308940', '74131744', '59458934', '50139418', '69744001', '50139418', '58984104', '58984104', '18517364', '18517364', '88864930', '88864930', '17302940', '52097806', '17302940', '86911157', '52097806', '68567760', '86911157', '85398882', '68567760', '92366596', '92366596', '32491682', '32491682', '58350157', '39200541', '17262050', '84176025', '39200541', '58350157', '23164498', '84176025', '17262050', '98679212', '20487961', '23164498', '98679212', '20487961', '32922153', '32922153', '18475616', '88187988', '18475616', '77370094', '78386968', '88187988', '77370094', '95148704', '28776375', '95856634', '52664786', '95856634', '95148704', '28776375', '52664786', '26663601', '26663601', '39637064', '39637064', '28543481', '77299770', '80075593', '28543481', '77299770', '80075593', '49765188', '49765188', '76321578', '90855201', '35741023', '87260491', '87260491', '83498643', '60384432', '83498643', '77174809', '60384432', '77174809', '63107542', '51506319', '68230543', '63107542', '26016571', '46005975', '27327264', '35741023', '35741023', '43306736', '27548564', '82130385', '43306736', '27548564', '82130385', '10130075', '10097459', '58911618', '10097459', '20048024', '27687017', '58911618', '33846597', '33846597', '62223082', '66605287', '50040699', '73237911', '54646534', '54646534', '46109780', '19833921', '51924236', '51924236', '51924236', '51924236', '29094997', '15377348', '15377348', '89148880', '89148880', '55344030', '55344030', '55344030', '55344030', '72358374', '72358374', '72358374', '72358374', '78485234', '78485234', '92082900', '92082900', '27682740', '99666261', '10788253', '63907598', '63907598', '56354723', '56354723', '37987836', '79136202', '79136202', '37987836', '37987836', '37987836', '37987836', '12675073', '36166694', '36166694', '88161488', '88161488', '62105311', '61247485', '86397776', '62105311', '32381267', '90372455', '61247485', '86397776', '91941977', '32381267', '90372455', '33064160', '91941977', '33064160', '29210827', '29210827', '60986961', '60986961', '46839699', '46839699', '69800291', '70099770', '12022241', '51763029', '70099770', '56464124', '61146645', '56464124', '51763029', '12022241', '38471834', '61146645', '29461222', '59200490', '38471834', '29461222', '59200490', '61093002', '79125728', '79125728', '61093002', '65660578', '65660578', '77887642', '77887642', '54649662', '15706555', '54649662', '15706555', '72537048', '91052140', '72537048', '91052140', '28472821', '59508667', '40122681', '28472821', '12433956', '59508667', '40122681', '12433956', '13638903', '48167944', '52185749', '13638903', '83171213', '83171213', '49046727', '80934560', '74784424', '30037067', '90066613', '10788252', '94291493', '50036857', '37703325', '77828016', '50036857', '94291493', '37703325', '60260051', '25833506', '13319622', '60260051', '13319622', '57240934', '25833506', '57240934', '41162058', '41162058', '64816515', '43159058', '10593077', '46477468', '10593077', '64816515', '43159058', '46477468', '78464777', '78464777', '78464777', '50428345', '50428345', '35327454', '35327454', '35327454', '35327454', '18282510', '18282510', '18282510', '18282510', '92998681', '92998681', '12007287', '12007287', '18174095', '61094656', '18174095', '61094656', '22768868', '19924640', '78613972', '58232067', '78613972', '58232067', '19924640', '22768868', '89950765', '89950765', '64263484', '93216919', '64263484', '93216919', '77095326', '24480658', '77095326', '24480658', '97044413', '97044413', '97044413', '35479276', '35479276', '28302095', '28302095', '33364800', '87799463', '55717644', '33364800', '87799463', '55717644', '10788251', '67467486', '67467486', '67467486', '67467486', '44720615', '85588512', '44720615', '91566582', '85588512', '91566582', '21474863', '21474863', '19888371', '22919423', '19888371', '24203981', '55243972', '22919423', '55243972', '24203981', '51619439', '71064067', '51046145', '91568691', '57877757', '57877757', '25897416', '49658696', '49658696', '87506874', '11229800', '81241153', '82094935', '87506874', '11229800', '82094935', '81241153', '55960353', '14257705', '14257705', '71659156', '18310361', '71659156', '84542048', '30219923', '18310361', '84542048', '30219923', '56088278', '56088278', '56088278', '56088278', '37272530', '93676222', '77373982', '37272530', '93676222', '81101970', '77373982', '10788250', '93241723', '16038017', '46533020', '99643197', '42133692', '16038017', '23465995', '87388601', '46533020', '57474425', '99643197', '23465995', '42133692', '87388601', '95679622', '57474425', '95679622', '38484918', '93125096', '68969377', '38484918', '31011932', '68969377', '93125096', '74085134', '31011932', '74085134', '67119059', '93027992', '32140918', '67119059', '32140918', '93027992', '69868608', '69868608', '97132121', '49310806', '22564461', '90119012', '68881243', '35573205', '14694057', '14853895', '21622200', '57201448', '21622200', '57201448', '53684074', '73111807', '44146905', '68055251', '53684074', '73111807', '68055251', '59185424', '44146905', '48303496', '59185424', '48303496', '55766524', '36957726', '36957726', '59309552', '98673101', '56785896', '60359590', '60359590', '18387479', '90706295', '21035169', '90706295', '18387479', '21035169', '38715956', '53505798', '19236193', '67209706', '95560067', '18919937', '67044791', '95560067', '67209706', '18919937', '67044791', '93241722', '26020948', '26020948', '19478479', '19478479', '29702066', '30880631', '69385778', '19273615', '89037007', '75652495', '19365335', '19365335', '65283637', '59335604', '83747452', '83747452', '83747452', '71721083', '86471101', '55722878', '36417197', '17716922', '23373473', '17716922', '23373473', '39459727', '39459727', '69245285', '62689241', '44126432', '69245285', '62689241', '18256612', '44126432', '18256612', '34999847', '22252019', '42264382', '45458973', '34999847', '22252019', '42264382', '45458973', '43176710', '76441642', '43176710', '76441642', '69302774', '65743701', '23107048', '51963875', '69302774', '65743701', '23107048', '91856083', '24362086', '51963875', '91856083', '24362086', '60020505', '22487402', '87382088', '22487402', '71560865', '87382088', '55144008', '71560865', '55144008', '54906109', '30484473', '15945321', '56721121', '41128879', '84940503', '58344979', '15945321', '56721121', '41128879', '58344979', '48578459', '48578459', '73675514', '46007296', '81391202', '73675514', '81391202', '46007296', '51102070', '96209842', '80815982', '51102070', '34894731', '34894731', '94608459', '48741732', '79216627', '34114421', '72149418', '94608459', '48741732', '67114982', '69376553', '84427943', '72149418', '34114421', '79216627', '69376553', '67114982', '84427943', '12651986', '66758577', '66758577', '18849968', '35287324', '11548271', '11548271', '61867774', '92489008', '11576471', '61867774', '92489008', '11576471', '63760757', '33000286', '63760757', '70202187', '33000286', '70202187', '19581679', '62108127', '19581679', '87127787', '62108127', '53359801', '87127787', '53359801', '74481377', '74481377', '32584900', '71407261', '32584900', '18711873', '71407261', '18711873', '98333154', '31688236', '10788249', '93241721', '48682860', '51977990', '24978214', '65046051', '48682860', '24978214', '51977990', '65046051', '17307784', '57319064', '48125216', '50012643', '50012643', '57319064', '48125216', '17307784', '91799522', '85403010', '73464991', '97809346', '97809346', '97809346', '97809346', '88409281', '36523920', '62635759', '98234308', '88409281', '98234308', '36523920', '62635759', '61675670', '13519631', '11372315', '68583297', '65929040', '33975028', '68583297', '11372315', '65365084', '65929040', '65365084', '21627771', '13853159', '79333841', '21627771', '13853159', '95685208', '79333841', '95685208', '60221441', '41267966', '31951797', '27074127', '48388218', '48388218', '31951797', '41267966', '75836294', '85616730', '25657429', '85616730', '75836294', '25657429', '95426078', '95426078', '60974910', '29258881', '72068842', '29075588', '60974910', '50561992', '29258881', '38592077', '72068842', '29075588', '50561992', '38592077', '81185967', '65058289', '81185967', '65058289', '10935917', '48627044', '10935917', '47383128', '23139340', '47383128', '48627044', '23139340', '29068013', '29068013', '29068013', '29068013', '78246158', '98524506', '70880865', '98524506', '29330459', '70880865', '29330459', '32005442', '32005442', '39286193', '39286193', '29504478', '64666557', '68513637', '29504478', '68513637', '64666557', '92034733', '99802394', '73906886', '11033248', '92034733', '99802394', '73906886', '11033248', '72031025', '72031025', '78650663', '43785428', '43785428', '78650663', '32012553', '74287346', '74287346', '32012553', '30296014', '30296014', '38663545', '38663545', '62415012', '31521384', '62415012', '31521384', '41651979', '91306487', '10266241', '40134596', '41651979', '10266241', '91306487', '40134596', '78246158', '78246158', '86891170', '86891170', '97522359', '66436525', '97522359', '28114968', '66436525', '28114968', '38077545', '64705057', '38077545', '64705057', '37058042', '37058042', '21869069', '21869069', '31935331', '31935331', '72131480', '72131480', '11168078', '72255178', '11168078', '72255178', '57762769', '18697807', '15795938', '57762769', '18697807', '15795938', '23421244', '23421244', '46913901', '46913901', '60113428', '32587293', '30658194', '81835490', '60113428', '32587293', '30658194', '81835490', '93112559', '93112559', '73867539', '20661605', '52393533', '20661605', '73867539', '52393533', '71973119', '35736850', '71973119', '20402474', '16552754', '35736850', '20402474', '16552754', '31688235', '93241720', '52800345', '33497542', '39656678', '33497542', '52206511', '63741528', '74280847', '52206511', '63741528', '62729748', '62729748', '53369443', '82623282', '53369443', '82623282', '99221788', '99221788', '38400955', '89052817', '89052817', '44003230', '44003230', '64810575', '91990426', '57209677', '75842375', '64810575', '91990426', '75842375', '57209677', '65573827', '54880059', '34293875', '81961778', '65573827', '54880059', '34293875', '81961778', '24498382', '57412999', '24498382', '57412999', '85687324', '73208735', '85687324', '73208735', '40471495', '40471495', '68215605', '68215605', '87320637', '87320637', '12337055', '77707717', '12337055', '77707717', '10712100', '89702575', '89702575', '10712100', '55115135', '55060703', '60633995', '55115135', '55060703', '10641592', '60633995', '10641592', 'R62024822', '29025327', 'R48933995', '39894928', 'R25281261', '51011674', '51011674', '78872930', '78872930', '94949641', '96755926', '54716821', '68643865', '94949641', '96755926', '54716821', '68643865', 'R95290778', 'R95290778', 'R15848969', 'R95290778', '51302858', '17705239', '51302858', '21684118', '17705239', '51477064', '21684118', '51477064', '85358069', 'R95290778', '93314745', '93314745', '36572968', '36572968', '80231427', '25921895', '80231427', '25921895', '31247315', '96833391', '31247315', '98772183', '96833391', '57964204', '50351273', '32998069', '76197623', '98772183', '41458423', '57964204', '50351273', '32998069', '76197623', '41458423', '85603721', '18225676', '85603721', '18225676', '85793777', '64133713', '64133713', '15042982', '15042982', '65274928', '18272171', '65274928', '18272171', '58522030', '42975923', '31103959', '34192847', '98607021', '42975923', '31103959', '34192847', '98607021', '64100399', '77485487', '64100399', '77485487', '23207796', '92014639', '83881468', '23207796', '94671165', '83881468', '94671165', '32972084', '35524211', '32972084', '35524211', '92259315', '36747203', '92259315', '87425378', '56348695', '87425378', '36747203', '56348695', '16630425', '80191390', '48202953', '13801058', '16630425', '80191390', '48202953', '13801058', '65298651', '37735715', '22966845', '65298651', '31337103', '37735715', '22966845', '31337103', 'R37289414', '88480600', '88480600', '88480600', '88480600', '51788069', '86500956', '51788069', '86500956', '87942935', '19616833', '87942935', '19616833', '23185096', '35539762', '23185096', '26257851', '74633527', '26257851', '40995534', '61741236', '74633527', '40995534', '61741236', '31688234', '93241719', '40023773', '44489481', '66444820', '40023773', '44489481', '66444820', 'R92609896', 'R54649834', '18540586', '18540586', '74844127', '33526447', '61423547', '74844127', '33526447', '61423547', '97832373', '94903193', '45472361', '30889803', '97832373', '30889803', '45472361', '94903193', 'R54649834', '86697229', '86697229', '48368055', '48368055', 'R54649834', '68830822', '79364025', '68830822', '79364025', '73854827', '83668410', '73854827', '87831273', '77754509', '83668410', '87831273', '77754509', '29398625', 'R14144181', '10311430', '29103076', '65874838', '10311430', '65874838', '29103076', '31121379', '31121379', '27084664', '58340908', '74665100', '58340908', '27084664', '74665100', '25335138', '79639451', '90348357', '25335138', '73752629', '79639451', '90348357', '73752629', '20519087', '30538964', '57700453', '18712229', '57700453', '30538964', '20519087', '18712229', '21807960', '47090528', '21807960', '57675491', '47090528', '57675491', '25844200', '25844200', '71012620', '87944885', '87944885', '71012620', '17297977', '62462409', '62462409', '17297977', '57765910', '65219889', '21333593', '42365442', '22419459', '57765910', '51257209', '39014920', '42365442', '21333593', '65219889', '51257209', '22419459', '11881989', '39014920', '11881989', '72269316', '74178705', '18029508', '74178705', '74569364', '18029508', '74569364', '99468087', '34901905', '99468087', '86394985', '34901905', '86394985', '68674449', '24522398', '68674449', '24522398', '58880896', '58880896', '21352523', '99532469', '19299981', '21352523', '99532469', '19299981', '88587580', '88587580', '62779118', '11727370', '62779118', '11727370', '81735578', '86681561', '81735578', '86681561', '66278548', '44008361', '66278548', '82600131', '44008361', '69833857', '82600131', '69833857', '53713853', '69194365', '69194365', '53713853', '20110942', 'R37696598', '79521152', '98235308', '98235308', '41572843', '41572843', '21420666', '74474150', '61068245', '74474150', '21420666', '51474868', '61068245', '51474868', '94782007', '94782007', '98960704', '98960704', 'R37696598', 'R92970236', '94782007', '94782007', '94782007', '94782007', '94782007', 'R28837088', 'R28837088', 'R28837088', '76734734', 'R28837088', '32392883', '81516769', '32392883', '81516769', '79690648', '47949631', '79690648', '12310946', '47949631', '82924909', '27751742', '12310946', '44219709', '82924909', '27751742', '44219709', '78749469', '28931706', '78749469', '28931706', '94236980', '32111789', '94236980', '32111789', '33937331', '25804705', '74081069', '31857143', '33937331', '25804705', '74081069', '31857143', '60437700', '60437700', '21277253', '67754797', '33285728', '21277253', '67754797', '33285728', '22809937', '57441500', '50349390', '22809937', '57441500', '50349390', '95663937', '95663937', '85390050', '15264963', '48242636', '62696101', '85390050', '15264963', '48242636', '62696101', '53395568', '53395568', '51357450', '92475134', '51357450', '63289561', '92475134', '63289561', '64056022', '64056022', '87979164', '22254901', '87979164', '48418062', '22254901', '40262550', '48418062', '40262550', '74648087', '62339929', '37970712', '62339929', '37970712', '96648251', '52970082', '96648251', '52970082', '26476948', '26476948', '26476948', '26476948', '20132971', '20132971', '98859428', '48970226', '98859428', '48970226', '32809594', '32809594', '35422625', '35422625', '29413206', '29413206', '14537517', '14537517', '39962112', '39962112', '93031067', '89666913', '93031067', '89666913', '39162796', '57776104', '39162796', '57776104', '74648087', '74648087', '33552609', '72909987', '72909987', '29051773', '27440734', '27440734', '27440734', '27440734', '27440734', '27440734', '93139576', '93139576', '31688233', '93241718', '55070251', '67937328', '67866959', '45781538', '67866959', '67937328', '32761553', '45781538', '32761553', '18361934', '95799790', '68171395', '18361934', '22031115', '95799790', '68171395', '22031115', '11263462', '28024946', '11263462', '65263004', '10390921', '28024946', '65263004', '10390921', '80733394', '53842591', '53842591', '80733394', '95906030', '40810785', '95906030', '40810785', '65472651', '65472651', '61944460', '61944460', '49811914', '49811914', '54278319', '44339207', '84358356', '33119718', '54278319', '97661648', '97661648', '68711312', '22037419', '68711312', '97642294', '23673459', '23673459', '29851841', 'R31013187', '87394131', '37003113', '37003113', 'R95290778', 'R82240826', '96463518', '96463518', '97024459', '97024459', '66151848', '66151848', 'R52097747', 'R52097747', 'R52097747', 'R52097747', '29059446', '29059446', '69696316', '69696316', '91558623', 'R21815166', 'R37696598', '89928136', '87514887', '89928136', '89928136', '28893817', '28893817', '11296200', '11296200', '74948262', '74115654', '74115654', '74534057', '10377621', 'R37696598', '60285063', '93052192', '11797178', '60285063', '17079285', '64794606', '54376832', '37094405', '93052192', '11797178', '35531586', '64794606', '54376832', '17079285', '37094405', '35531586', '37503418', '41361136', '48099993', '48099993', '98230306', '72586292', '98230306', '65274537', '45374509', '72586292', '34553713', '21298567', '49899943', '34553713', '49899943', '82272332', '24664007', '21298567', '48448274', '24664007', '48448274', '18751279', '38955226', '73078142', '18751279', '38955226', '82120958', '30317988', '82120958', '30317988', '78078358', '20642871', '20642871', 'R74004589', '46998621', '46998621', '58118932', '58118932', '30372284', '30372284', '65435715', '65435715', '41244632', 'R37696598', '63849849', '75782636', '63849849', '63073954', '85253845', '12478365', '28014054', '75782636', '63073954', '85253845', '12478365', '38211315', '28014054', '70666879', '38211315', '70666879', 'R37696598', 'R37696598', '72564347', '45943410', '39932172', '72564347', '39932172', '18544885', '45943410', '61812082', '53193698', '18544885', '21350245', '61812082', '53193698', '39668289', 'R17669977', '21350245', '39668289', 'R37696598', '13275191', '41327957', '85458786', '13275191', '41327957', '85458786', '23331850', '23331850', 'R37696598', 'R37696598', '67130953', '67130953', 'R72873265', '62044239', '62044239', '14467837', '66931436', '14467837', '66931436', '26474417', '74467575', '26474417', '99977474', '74467575', '99977474', '68444308', '30477207', '68444308', '30477207', '79934598', '84953518', '79934598', '84953518', 'R12175685', '34327259', '34327259', 'R37696598', '53004164', '53004164', '37491678', '41064673', '21533320', '37491678', '21533320', '41064673', 'R37696598', '14912350', '14912350', '27471465', '81225720', '35742507', '81225720', '84173278', '49847481', '27471465', '35742507', '18190354', '49847481', '84173278', '18190354', 'R37696598', '41440477', '11480371', '11480371', '41440477', '76574729', '93125926', '76574729', 'R37696598', '27350814', '27350814', 'R37696598', '92869679', '92869679', '73259444', '73259444', '15663835', '29467060', '20392326', '15663835', '29467060', '20392326', '48761126', '74054486', '75913705', '87877080', '87877080', '48761126', '75913705', '74054486', '17976751', '90127558', '70731146', '17976751', '95460089', '90127558', '70731146', '82231133', '49158426', '49158426', '52444235', '49299263', '82231133', '52444235', '49299263', 'R37696598', '93241717', '28297587', '72379264', '19840521', '66536535', '44395145', '66536535', '44395145', '17880176', '48517519', '17880176', '48517519', 'R79601514', 'R79601514', 'R79601514', '55047898', '55047898', '28574108', '56419177', '64356239', '28574108', '64356239', '56419177', '26527087', '26527087', '67824534', '67824534', '43907515', '43907515', '71063070', '49043668', '74927932', '71063070', '74927932', '49043668', '54970766', '54970766', '59974762', '92226327', '92226327', '59974762', '35976461', '35976461', '61354052', '63299708', '61354052', '78690378', '40125744', '63299708', '78690378', '40125744', '65169004', '65169004', '53486418', '20659645', '53179110', '54001970', '53486418', '20659645', '17142977', '54001970', '53179110', '64065646', '17142977', '68740066', '64065646', '68740066', '23212276', '77746411', '23413447', '96399072', '23212276', '77746411', '96399072', '23413447', '58016953', '31038479', '31038479', '25053654', '90058583', '29744207', '29744207', '54775399', '54775399', '63902950', '63902950', '22382998', '54705035', '93514450', '80916636', '54705035', '97731701', '80916636', '93514450', '97731701', '54160991', '98052543', '61797218', '72620949', '54160991', '61797218', '98052543', '72620949', '38384997', '39410507', '38384997', '13430551', '89888595', '13430551', '65166909', '14042661', '65166909', '14042661', '72112050', '64360306', '72112050', '64360306', 'R81005215', '48205620', '31267269', '48205620', '56721868', '39667400', '31267269', '56721868', '39667400', '54668444', '12058291', '54668444', '83566413', '12058291', '30795228', '83566413', '30795228', '76326719', '88780242', '25178689', '21736759', '44104214', '44104214', '87524647', '87524647', '30663367', '33942430', '33942430', '30663367', 'R67218573', '25398031', '25398031', '25398031', '25398031', '57321226', '63794043', '77723284', '77116347', '57321226', '63794043', '77723284', '77116347', '48127804', '45307573', '83551870', '33236071', '48127804', '45307573', '83551870', '33236071', '24037187', '58170353', '55773829', '65632135', '24037187', '58170353', '55773829', '65632135', '32528407', '91995102', '11013342', '32528407', '91995102', '11013342', '87244195', '87244195', '43523883', '63528712', '77279795', '43523883', '14980299', '63528712', '14980299', '22306951', '63085368', '34208490', '63085368', '32690328', '22306951', '34208490', '32690328', '72906613', '72906613', '46865431', '23076590', '72035359', '23076590', '46865431', '72035359', '59212843', '38181217', '86149301', '86149301', '29813206', '96430986', '29813206', '28718114', '28718114', '96430986', '21174018', '15063377', '15063377', '10909399', '10909399', '26413971', '26413971', '92974115', '85056128', '59704438', '92974115', '59704438', '85056128', '14266298', '14266298', '13298824', '13298824', '10494618', '10494618', '35003382', '35003382', '34826483', '34826483', '20435988', '79401726', '77602857', '20435988', '82913420', '70511552', '77602857', '79401726', '82913420', '44840103', '70511552', '44819902', '44840103', '44819902', '44806429', '77579019', '44806429', '81149873', '77579019', '21294406', '81149873', '21294406', '88852494', '88852494', '15680825', '15680825', '96127132', '48507539', '56742644', '96127132', '77517694', '48507539', '56742644', '77517694', '74794790', '71031884', '62681608', '74794790', '40059652', '71031884', '62681608', '40059652', '44371656', '88767952', '38424122', '63742979', '88767952', '44371656', '38424122', '63742979', '57784957', '57784957', '40195554', '40195554', '38699119', '38699119', '93241716', '53905044', '81097160', '53905044', '76225871', '69858726', '81097160', '76225871', '69858726', '93956687', '90666108', '20194109', '93956687', '36527587', '90666108', '20194109', '36527587', '97999263', '97999263', '59189184', '18267122', '25650304', '18267122', '25650304', '59189184', '45148080', '68585428', '39667347', '24277797', '45148080', '68585428', '39667347', '24277797', 'R37984827', '46224644', '12284803', '94790831', '35814318', '46224644', '35814318', '94790831', '12284803', '15970633', '15970633', '98700493', '61815924', '70415253', '98700493', '61815924', '70415253', 'R10819213', '70883995', '70883995', '38870398', '57098997', '38870398', '57098997', '64279060', '10487840', '20311163', '20311163', '84306309', '33757736', '33757736', '33757736', '33757736', '33757736', '33757736', '33757736', '37183577', '37183577', '37183577', '37183577', 'R63259107', '80594627', '80594627', '92805574', '13995179', '13995179', '92805574', 'R63259107', '46909210', '21678723', '21678723', '46909210', '46909210', 'R20843691', 'R20843691', 'R41721803', 'R41721803', 'R67261849', 'R41721803', '99131467', '99131467', '53139546', '80964999', 'R63259107', 'R63259107', '69938737', '69938737', '26072291', '26072291', '13626084', '27709724', '28143411', '13626084', '28143411', '27709724', '77829503', '62670825', '77829503', '62670825', '28863942', '28863942', '21990438', '21990438', '33938804', '63058670', '63058670', '12175692', '76297270', '12175692', '57036176', '76297270', '94245767', '95593135', '57036176', '94245767', '95593135', '12696099', '12696099', '22837955', '85202200', '22837955', '51366960', '85202200', '86954878', '51366960', '86954878', '75434900', '75434900', '90357286', '35299335', '65644363', '90357286', '35299335', '65644363', '70305910', '47989458', '42058113', '70305910', '42058113', '47989458', '76913187', '76913187', '33212790', '72104239', '33212790', '72104239', '53198558', '53198558', '26298818', '25474766', '25474766', '26298818', '90451562', '51125354', '90451562', '51125354', '58970090', '44828470', '77125503', '86651661', '58970090', '44828470', '77125503', '86651661', '90684784', '53647656', '79135151', '53647656', '79135151', '26713286', '29245447', '26713286', '29245447', '35274995', '55865440', '37530888', '35274995', '55865440', '37530888', '38477811', '69212427', '38477811', '33376430', '69212427', '71023992', '86715275', '33376430', '88237371', '33696465', '71023992', '86715275', '88237371', '22805774', '33696465', '22805774', '56010101', '56010101', '56010101', '56010101', 'R80173881', 'R80173881', '12462758', '20296213', '71015277', '65157109', '20296213', '12462758', '71015277', '65157109', 'R21481655', '81152593', '68101658', '18211416', '81152593', '79872092', '68101658', '18211416', '79872092', '40867643', 'R84812835', '24998574', '73691894', '41575316', '73691894', '24998574', '99771945', '41575316', '99771945', '73866245', '92034770', '55087638', '13313407', '49634204', '92034770', '49634204', '55087638', '13313407', '46909211', '93241715', 'R14710147', '19978021', '19978021', '48786188', '48786188', '57465699', '57465699', 'R14710147', '80184589', '63279493', '61931322', '80184589', '63279493', '61931322', '13182139', '13182139', '38369943', '25429293', '10784987', '38369943', '25429293', '10784987', '33999523', '73552583', '29020915', '58027887', '73552583', '33999523', '29020915', '58027887', '71720050', '23716492', '72517798', '71720050', '23716492', '72517798', '38439605', '38439605', '63909059', '63909059', '66581787', '74115145', '83303702', '66581787', '74115145', '83303702', '50237254', '79225738', '50743106', '50237254', '79225738', '50743106', '49275772', '95247320', '95247320', '47517623', '14286925', '47517623', '82316728', '14286925', '82316728', '68263540', '68263540', '74043260', '41001066', '22455083', '10373938', '74043260', '41001066', '22455083', '10373938', '64554089', '60187467', '64554089', '60187467', '32552763', '66854361', '32552763', '66854361', '87962465', '87962465', '49827438', '78972366', '24287580', '78972366', '49827438', '24287580', '59004570', '59004570', '61932317', '94735269', '86034260', '61932317', '94735269', '86034260', '83364699', '34721536', '83364699', '34721536', '96364691', '96364691', '94068843', '44796337', '94068843', '44796337', '24038365', '11888474', '24038365', '11888474', '55267388', '39467045', '97725943', '32119398', '39467045', '55267388', '97725943', '32119398', '59921393', '19804714', '73108490', '89794602', '59921393', '19804714', '73108490', '89794602', '53668644', '53668644', '25795807', '44266476', '27780855', '25795807', '44266476', '40838563', '27780855', '40838563', '98100727', '97614883', '98100727', '63265916', '97614883', '63265916', '42243033', '42243033', '25299095', '36376351', '25029840', '25299095', '36376351', '25029840', '61147172', '10083748', '65257257', '96366358', '65257257', '61147172', '10083748', '96366358', '48618655', '13353043', '48618655', '35262512', '50312171', '13353043', '35262512', '50312171', '27506303', '36183172', '61523432', '27506303', '36183172', '61523432', 'R21963284', '17790162', '78978516', '88802228', '96292184', '17790162', '88802228', '78978516', '96292184', 'R69523971', 'R69523971', '91384951', '35387366', '23748779', '63174699', '91384951', '23748779', '35387366', '63174699', '49821340', '58723088', '49821340', '86693682', '81804346', '58723088', '81804346', '62183387', '61491850', '62183387', '61491850', '16184070', '74199917', '16184070', '74199917', '48242522', '48242522', '99926762', '69199946', '35480323', '69199946', '35480323', '99926762', '65377922', '27574118', '86002250', '65377922', '27574118', '86002250', '93375158', '93375158', 'R41721803', '53848634', '36327392', '36327392', '55041314', '16680402', '16680402', '15498875', '75243630', '65723488', '75243630', '15498875', '65723488', '16218068', '48859326', '98632651', '16218068', '48859326', '98632651', '26280487', '26280487', '71119453', '11029901', '84908284', '24635310', '71119453', '11029901', '84908284', '24635310', '57728782', '57728782', '57728782', '85891401', '85891401', '12419512', '91853060', '80826483', '12419512', '63671875', '81892694', '91853060', '84588840', '11701061', '84588840', '80826483', '63671875', '81892694', '11701061', '45684358', '45684358', '95901391', '20229664', '95901391', '20229664', '41250005', '41250005', 'R63259107', '91405135', '91405135', '82880831', '82880831', '17502271', '17502271', '54275632', '54275632', '81692839', '67248536', '81692839', '67248536', '57016943', '31012311', '57016943', '31012311', '92016425', '58094862', '68277368', '87442141', '58094862', '68277368', '92016425', '87442141', '97648219', '44870550', '91402437', '16524222', '97648219', '78082197', '35230815', '16524222', '35230815', '40576913', '84225508', '76093973', '40576913', '16742596', '84225508', '76093973', '16742596', '48079412', '34858703', '94228296', '48079412', '34858703', '94228296', '53520261', '29306919', '29306919', '47488360', '53520261', '28481437', '47488360', '28481437', '19771043', '31175200', '19771043', '31175200', '90909496', '84863845', '84863845', '90909496', '38381997', '38811185', '30327075', '60598404', '30327075', '38811185', '38381997', '60598404', '44049038', '44049038', '94949628', '43283567', '94949628', '43283567', '64872862', '64872862', '27591188', '72890442', '27591188', '27133085', '72890442', '77594990', '45480152', '77594990', '78176130', '42376778', '81239431', '44198450', '72021230', '42376778', '81239431', '87443926', '88317614', '87443926', '42210183', '88317614', '97833225', '42210183', '97833225', '93763161', '93763161', '94005940', '47970410', '10871507', '28962698', '47970410', '10871507', '94005940', '28962698', '58487681', '45335487', '50500365', '45335487', '46904983', '36486247', '46904983', '74354127', '68670861', '37041790', '15093606', '86509758', '86509758', '21685204', '86755624', '50956675', '43020233', '86755624', '21685204', '50956675', '43020233', '33612174', '35743131', '33612174', '35743131', '85747493', '51164362', '85747493', '94370131', '76618702', '12917501', '12917501', '94370131', '76618702', '59314219', '59314219', '66059740', '66059740', '47984933', '68297940', '42002968', '47984933', '68297940', '42002968', '62958904', '62958904', '93241714', '91603256', '98436803', '91760207', '24254504', '98436803', '12483892', '24254504', '91760207', '12483892', '10947740', '10947740', '43983293', '11938270', '11938270', '11938270', '11938270', '73279409', '50415739', '68662809', '68662809', '68662809', '68662809', '68662809', '95557449', '95557449', '95557449', '39670950', '39670950', '49821957', '49821957', '29177254', '29177254', '22888816', '67543254', '63203232', '10181222', '10181222', '24501616', '24501616', '59830644', '62678146', '62678146', '59830644', '96097987', '62749817', '54067024', '62749817', '18728739', '96097987', '54067024', '18728739', '50168026', '79116627', '71933060', '44422343', '79116627', '50168026', '71933060', '44422343', '35575458', '35575458', '75145229', '92009645', '28448144', '75145229', '58370420', '92009645', '97054793', '74210743', '58370420', '28448144', '50386055', '97054793', '74210743', '50386055', '58548507', '54994808', '48318893', '34695458', '58548507', '54994808', '34695458', '48318893', '63191626', '58881944', '64287864', '10794487', '63191626', '58881944', '64287864', '10794487', '87558455', '32805491', '87558455', '82146833', '51995449', '32805491', '82146833', '51995449', '77678569', '77678569', '36372587', '36372587', '30857400', '16554589', '91562094', '91562094', '36704158', '10591167', '39389613', '81869560', '36704158', '10591167', '39389613', '56969300', '24196233', '77688964', '56969300', '52339955', '24196233', '77688964', '52339955', '57374045', '50344762', '19059137', '57374045', '19059137', '50344762', '52725459', '52725459', '54681423', '95871189', '91283696', '25844934', '54681423', '95871189', '91283696', '25844934', '50237200', '50237200', '26066069', '26066069', '48058157', '98206200', '75676990', '86812558', '56754664', '70398013', '86812558', '75676990', '48058157', '56754664', '70398013', '98206200', '16362019', '64940970', '64940970', '16362019', '75429141', '93599120', '13640830', '57484036', '52106615', '26736535', '53221702', '26736535', '53221702', '81145132', '53851683', '81145132', '53851683', '45755718', '63966062', '39364829', '45755718', '61078433', '63966062', '39364829', '61078433', '16490888', '11625233', '93828823', '16490888', '11625233', '93828823', '84061012', '76720960', '93241713', '28418838', '78075997', '85253897', '78075997', '85977955', '85253897', '13310728', '28057061', '86070369', '86070369', '77387359', '77387359', '25559513', '35388259', '96080121', '25559513', '35388259', '96080121', '35699181', '35699181', '99208845', '75368919', '75368919', '99208845', '10516253', '60189674', '10516253', '60189674', '44883010', '44883010', '68252582', '34207035', '93521885', '76250667', '34207035', '68252582', '93521885', '76250667', '44883010', '44883010', '52106615', '52106615', '14853484', '35334396', '80610454', '35334396', '14853484', '41795391', '80610454', '41795391', '28194050', '18270406', '28194050', '82508355', '88681804', '18270406', '25529207', '82440603', '59713656', '82508355', '25529207', '82440603', '59713656', 'R29668673', '40483496', '94934079', '65021510', '40483496', '30281133', '94934079', '58713341', '40050548', '65021510', '40050548', '77387519', '30281133', '58713341', '72717468', '77387519', '72717468', 'R29668673', '17511025', '90443235', '72632925', '17511025', '90443235', '37636216', '72632925', '37636216', '70478439', '70478439', '74859726', '45976958', '15722577', '18661644', '74859726', '45976958', '45885061', '69075176', '70456706', '15722577', '70456706', '69075176', '18661644', '45885061', '81464145', '81464145', '45136322', '67421366', '24353715', '67421366', '45136322', '24353715', '30830034', '31059907', '31059907', '31059907', '22752911', '72004059', '66421473', '27954331', '72004059', '22752911', '66421473', '27954331', '45998472', '90135299', '19575156', '19575156', '31059907', '31059907', '45998472', '95298061', '96029746', '24666875', '69368305', '96029746', '24666875', '95298061', '69368305', '75691510', '83691052', '16500530', '24015187', '75691510', '83691052', '16500530', '24015187', '28418838', '56172319', '33546535', '95474853', '15858343', '27074191', '95474853', '15858343', '27074191', '42590079', '42590079', '36429985', '41837217', '74004794', '36429985', '41837217', '99410466', '74004794', '99410466', '34262186', '34262186', '11589618', '52732641', '11589618', '52732641', '43379338', '43379338', '18112186', '50091123', '50091123', '42305969', '91482535', '42305969', '91482535', '20284113', '65765046', '20284113', '65765046', 'R28060448', '62264663', '20800993', '20800993', '14763033', '14763033', '14763033', '14763033', '14763033', '14763033', '14763033', '63193592', '35511612', '79029991', '75018224', '75018224', '93241712', '49544614', '49544614', '20679394', '10026556', '81462104', '11813522', '20679394', '81462104', '23961587', '80333845', '23961587', '80333845', '60409937', '60409937', '19946327', '19946327', '98172224', '98172224', '82736212', '82736212', '48489753', '48489753', '40729232', '40729232', '83794546', '83794546', '22888816', '22888816', 'R91261761', '21412040', '61433200', '61433200', '64276330', '84753213', '53086702', '74855988', '13839611', '74855988', '13839611', '37895403', '37895403', '77857338', '77857338', '93241711', '41529471', '41529471', '33874323', '33874323', '70775774', '15742944', '92927188', '14895340', '70775774', '15742944', '92927188', '14895340', '68493289', '35117611', '68493289', '35117611', '70115711', '37459422', '91227697', '96510292', '96510292', '12078128', '68263353', 'RCC230D3E3', '93704075', '72741873', '93704075', '72741873', '25953665', '58421498', '79245335', '25953665', '16960956', '79245335', '58421498', '16960956', '83277960', '41857943', '83277960', '41857943', '14078441', '14078441', '13184608', '52244135', '13184608', '52244135', '62254294', '32029091', '62254294', '32029091', '57089360', '57089360', '36505800', '36505800', '63731746', '70344232', '63731746', '70344232', '71961743', '36127828', '10706512', '42413819', '71961743', '36127828', '10706512', '42413819', '51374722', '22981564', '62121733', '67235445', '98482430', '98482430', '35590597', '35590597', '94102489', '66378556', '66378556', '22981564', '22981564', '46385204', '46431585', '46431585', '20929942', '20929942', '22592770', 'R41721803', '38567078', 'R41721803', '89524692', 'R41721803', 'R41721803', '49729706', '33231045', '25877918', '33231045', '25877918', '49692372', '34707139', '49692372', '34707139', '28320932', '61401455', '61401455', '71484371', '51307209', '21789116', '31500226', '25641654', '25641654', '49001710', '49001710', '39630056', '39630056', '79908573', '95450271', '23835456', '22434679', '16395330', '22434679', '22434679', '11154206', '44271705', '44271705', '84604634', '84604634', '99276109', '48027700', '99276109', '46763562', '22883205', '22883205', '22883205', '59554343', '59554343', '31642023', '31642023', '95695228', '95695228', '81848899', '33364961', '33364961', '49345324', '49345324', '93055081', '93055081', '13620709', '25242838', '13620709', '25242838', 'R53067430', 'R53067430', '38969911', '93852162', '93852162', '38996950', '26435217', '95101713', '95101713', '63276049', '30465518', '17812644', '60022341', 'R51967057', '55083178', 'R51967057', '15113113', '15113113', '12642634', '12642634', '12642634', '76605934', '76605934', '30465518', '15531001', '15531001', '68412048', '68412048', '61948194', '72929489', '10558349', '92636926', '44724580', '44724580', '80760254', '80760254', '40603007', '40603007', '65166347', '65166347', '75917978', '62593441', '47793411', '93241710', '80448675', '60791713', '60791713', '60791713', '67700671', '53412698', '21129471', '75042777', '62791083', '61464957', '44121944', '44121944', '33977300', '33977300', '73300416', '73300416', '74051985', '74051985', '40634762', '40634762', '44883913', '44883913', '28758116', '87461938', '28758116', '87461938', '43061987', '68706441', '43061987', '68706441', '76534664', '83048031', '76534664', '83048031', '86377168', '86377168', 'R31619590', 'R31619590', 'R31619590', 'R31619590', 'R31619590', 'R31619590', 'R27267028', 'R27267028', '25424284', 'R27267028', '43460401', '97820963', '43460401', '97820963', '42955915', '54713047', '42955915', '54713047', '50760944', '87998836', '50760944', '87998836', '28168393', '71791211', '28168393', '38582757', '71791211', '38582757', '27630020', '27630020', '48946814', '76051326', '48946814', '76051326', '12347475', '12347475', '72988632', '72988632', '33237927', '19892373', '44521763', '44521763', '39899114', '37024481', '37024481', '39899114', '13246474', '20104843', '13246474', '20104843', '40640544', '40640544', '20746196', '97776831', '57069865', '20746196', '57069865', '97776831', '65031417', '65031417', '70974475', '70974475', '76422418', '76422418', '75287215', '11647514', '32730504', '75287215', '11647514', '32730504', '37595024', '48640272', '37595024', '48640272', 'R28060448', '49456506', '66496147', '66496147', '49456506', '58596841', '43528507', '58596841', '43528507', '49210814', '49210814', '14409467', '14409467', '34464729', '54342490', '34464729', '54342490', '93241709', '68614030', '85905913', '85905913', 'R28060448', 'R46812869', '30587964', 'R28060448', 'R76403406', 'R41496638', 'R41496638', 'R41496638', 'R21145270', 'R20044755', 'R50029853', '64817009', '64817009', 'R29097019', 'R94086530', '67433557', '67433557', '15097406', '25296555', '15097406', '25296555', '91673816', '91673816', '45238127', '45238127', '20586409', '85176859', '20586409', '85176859', '93995250', '12998431', '93995250', '12998431', '25822565', '16111872', '16111872', '83066985', '34283052', '83066985', '34283052', '61400867', '61400867', '76823468', '76823468', '48169986', '25686811', '48169986', '25686811', '89531715', '15622746', '87972542', '15622746', '87972542', '82839774', '82839774', '97893258', '97893258', '89531715', '89531715', '89531715', '89531715', 'R28060448', '10305645', '75999381', '84685575', '75999381', '84685575', '64542547', '64542547', '89907316', '45272860', '38638772', '45272860', '38638772', '83754974', '27968277', '83754974', '27968277', '97518988', '18396042', '97518988', '18396042', '42252220', '42252220', '59440668', '59440668', '62018709', '74842024', '74842024', '62018709', '27462790', '27462790', '46833538', '20174155', '46833538', '64036760', '20174155', '64036760', '95251824', '89754430', '95251824', '89754430', 'R24682870', 'R62100341', 'R62100341', 'R24682870', '58582445', '58582445', '28455969', '15586049', '28455969', '15586049', '51277352', '31020782', '51277352', '31020782', '43242700', '43242700', '84217639', '25647980', 'R29097019', '5D0E53A13', '37D651F77', '5D0E53A13', '5D0E53A13', '5D0E53A13', '37D651F77', 'R28060448', '37D651F77', '13567494', '32544287', '13567494', '32544287', '35388119', '21361571', '15128027', '35388119', '21361571', '15128027', '25336833', '25336833', '77009553', '85180223', '77009553', 'R29097019', '55556523', '62225765', '55556523', '62225765', '92634128', '90366087', '92634128', '90366087', '98659187', '98659187', '85866754', '85866754', '51933550', '97137977', '51933550', '97137977', '86858267', '98145992', '86858267', '98145992', '63267516', '32941868', '63267516', '32941868', 'R28060448', '57772124', '16875569', '16875569', '11569576', '29318252', '11569576', '29318252', '68828609', '46364812', '68828609', '46364812', '76188424', '69908508', '69908508', '76188424', '25115271', '25115271', '83667266', '83667266', '59627168', '43002645', '59627168', '43002645', '95121502', '89026208', '89026208', 'RE11DD795F', '80883723', '33135031', '80883723', '33135031', '88044548', '26155469', '88044548', '26155469', '80723620', '34780068', '80723620', '34780068', '81032249', '46008331', '81032249', '46008331', '70423718', '43157391', '70423718', '43157391', '89577457', '49876470', '89577457', '49876470', '76595937', '76595937', '10981353', '61320274', '10981353', '52781833', '61320274', '52781833', '61813803', '60858434', '84045063', '60858434', '61813803', '25005521', '84045063', '25005521', '14694838', '14374219', '14694838', '14374219', '46555586', '21512499', '46555586', '21512499', '11903021', '11903021', '63357899', '63357899', '15256997', '56722522', '15256997', '56722522', '20610364', '58962607', '20610364', '58962607', '83749654', '83749654', '45069016', '45069016', '65806632', '65806632', '69377851', '69377851', '69121599', '73661185', '69121599', '73661185', '92416547', '31787686', '92416547', '31787686', '47601444', '47601444', '48954870', '48954870', '86046020', '86046020', '69048150', '14308111', '69048150', '14308111', '80420911', '95718234', '80420911', '95718234', '98373669', '98373669', '79544289', '79544289', '26989439', '26989439', '84823584', '84823584', '28746204', '49023226', '28746204', '49023226', '11439829', '78371367', '11439829', '78371367', '81498188', '81498188', '80312624', '80312624', '84760751', '84760751', '68802782', '41047068', '68802782', '41047068', '42210895', '42210895', '61378610', '61378610', '56059160', '42420846', '42420846', '74041827', '30255325', '74041827', '53951943', '80070998', '54414675', '86916840', '16381989', '16381989', '86916840', '70478485', '70478485', '68925460', '74891889', '74891889', '43193704', '43193704', '34534021', '30160139', '83870655', '23893495', '83870655', '23893495', '29064260', '35705822', '31067936', '94307107', '29064260', '69829084', '90511621', '52948610', '90511621', '55508777', '52948610', '41938931', '95591614', '55508777', '41938931', '95591614', '49606853', '77200228', '73611128', '97819323', '49606853', '51441011', '77200228', '13644087', '73611128', '56137184', '21398101', '97819323', '51441011', '98792577', '13644087', '66135742', '21398101', '56137184', '12120826', '98792577', '66135742', '88652642', '12120826', '88652642', '94609324', '46048543', '94609324', '46048543', '58930553', '63963086', '58930553', '63963086', '29140275', '84645942', '29140275', '84645942', '71839869', '11892225', '75397711', '11892225', '75397711', '71839869', '14403891', '14403891', 'R90934877', '71103742', '31559641', '71103742', '31559641', '46093987', '68028738', '46093987', '68028738', 'R31196461', '61360467', 'R31196461', 'R31196461', '50947597', '46241359', '50947597', '46241359', '47536313', '38714599', '47536313', '38714599', '65175920', '85096219', '65175920', '72632305', '85096219', '32111220', '14718530', '32111220', '14718530', '32973958', '35344414', '32973958', '35344414', '41902771', '15027787', '41902771', '15027787', '86021772', '93701954', '86021772', '93701954', '28345199', '83297014', '28345199', '83297014', '73995363', '44239892', '73995363', '44239892', '63366581', '63366581', '42201512', '42201512', '16163290', '16163290', '94894356', '94894356', '42880321', '75941059', '42880321', '75941059', '23970037', '23970037', '18485966', '18485966', '35083681', '21149141', '21149141', '35083681', '60725939', '60725939', '66767780', '65730627', '66767780', '65730627', '25393905', '25393905', '36249936', '36249936', '46486227', '13036322', '46486227', '13036322', '54794338', '54794338', '30936291', '30936291', '75619224', '75619224', '55353869', '55353869', '60602518', '60602518', '17123475', '17123475', '48759061', '25805752', '14434179', '25805752', '14434179', '16953312', '94439323', '16953312', '94439323', '16678975', '16678975', '73429648', '73429648', '51172279', '51172279', '49085758', '49085758', '64213793', '64213793', '26857941', '26857941', '83549181', '83549181', '32501572', '32501572', '98828958', '45882195', '98828958', '45882195', '65881053', '65881053', '44350464', '44350464', '89007942', '89007942', '17513051', '36796757', '17513051', '36796757', '59516174', '59516174', '94404634', '94404634', '34857186', '34857186', '62531169', '62531169', '19558494', '19558494', '11514595', '49790902', '11514595', '49790902', '87974542', '87974542', '22907537', '22907537', '84248395', '84248395', '26455494', '26455494', '44775159', '59507880', '44775159', '59507880', '72278787', '16083989', '40149679', '72278787', '54931002', '16083989', '40149679', '54931002', '71920509', '63197888', '63197888', '71920509', '81621330', '23402712', '81621330', '23402712', '88203829', '21230009', '88203829', '21230009', '46738110', '46738110', '69774314', '69774314', '10605914', '15212794', '10605914', '15212794', '23899918', '23899918', '72460597', '26268478', '72460597', '26268478', '83152521', '94542662', '24790720', '83152521', '94542662', '24790720', '27692433', '15517815', '27692433', '15517815', '83526473', '84988502', '83526473', '84988502', '72743592', '72743592', '79547871', '79547871', '48505847', '48505847', '11255630', '11255630', '14841870', '14841870', '36014711', '36014711', '10622757', '47595968', '10622757', '47595968', '80546733', '81287522', '81287522', '80546733', '35783369', '55949044', '35783369', '55949044', '26633754', '65585562', '26633754', '65585562', '49090314', '68305203', '49090314', '68305203', '83766651', '59180741', '83766651', '59180741', '25668173', '25668173', '69281775', '69281775', '66818212', '66818212', '66636085', '66636085', '33503942', '33503942', '64407342', '64407342', '24219830', '24219830', '94852433', '82888601', '94852433', '82888601', '39177791', '68706041', '12926856', '39177791', '68706041', '12926856', '99444935', '99444935', '63961678', '13972565', '13972565', '63961678', '82602088', '51577371', '82602088', '51577371', '69935318', '64048403', '64048403', '69935318', '63107372', '27666547', '63107372', '85269519', '22769543', '82665627', '85269519', '22769543', '82665627', '57398686', '57398686', '23429678', '17066956', '17066956', '23429678', '13283546', '52818543', '98886093', '98886093', '46081124', '46081124', '18838747', '18838747', '42591677', '42591677', '82167273', '24403539', '82167273', '24403539', '87786123', '54548049', '87786123', '54548049', '76828444', '76828444', '58305624', '58305624', '44668181', '44668181', '56779823', '56779823', '93962542', '77819656', '93962542', '77819656', '85498812', '16845973', '85498812', '16845973', '71652888', '71652888', '10109344', '71980635', '71980635', '71980635', '74669215', '74669215', '33923939', '33923939', '51128808', '51128808', '51128808', 'R94086530', 'R57885750', '28247891', 'R51610935', 'R51610935', 'R51610935', 'R51610935', 'R38386327', '96615460', '96615460', 'R38386327', 'R44626690', 'R38386327', 'R72055584', 'R72055584', 'R72055584', 'R72055584', 'R44626690', '5D0E53A13', '5D0E53A13', '5D0E53A13', '37D651F77', '37D651F77', '58124869', 'R72055584', '64158589', 'R38386327', 'R20272532', 'R38386327', '78339469', '78339469', 'R38386327', 'R38386327', 'R38386327', 'R38386327', 'R72055584', 'R20272532', 'R72055584', 'R20272532', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', '67C157FCD', 'R72055584', '67C157FCD', '67C157FCD', '50243968', '50243968', '73164434', '73164434', '59709388', '59709388', '64198525', '67C157FCD', '67C157FCD', '69465010', '96188381', '69465010', '96188381', '17622015', '83369594', '17622015', '83369594', '67C157FCD', '67C157FCD', 'R38386327', '67C157FCD', '67C157FCD', '67C157FCD', '68504304', '86636597', '95131110', '68504304', '86636597', '95131110', '49136870', '33448945', '49136870', '51583481', '20290997', '85008792', '33448945', '20290997', '51583481', '85008792', '67C157FCD', '73315842', '67C157FCD', '67C157FCD', '67C157FCD', '74912687', '98560398', '98560398', '94808253', '94808253', '94021697', '69148283', '58890066', '27916409', '69148283', '94021697', '58890066', '27916409', '79407741', '13699090', '79407741', '13699090', '57026456', '57026456', '48498772', 'R94086530', '75452856', '57526034', '75452856', '57526034', '43883251', '43883251', '85959544', '85959544', 'R85402283', '23165858', '23165858', '46234837', '46234837', '14270242', '14270242', '13881325', '13881325', 'R85402283', 'RR30756183', '65308449', '22365657', '65308449', '22365657', '87851929', '17150905', '87851929', '17150905', '22742694', '22742694', '36578203', '36578203', '69611944', '69611944', '19953137', '19953137', '45025245', '11813558', '45025245', '11813558', '23801673', '32673755', '23801673', '32673755', '78044764', '78044764', '95047920', '59522365', '95047920', '59522365', '75424282', '75424282', '56993774', '56993774', '72285099', '72285099', '41716254', '41716254', '46060324', '46060324', '42159612', '26086294', '42159612', '26086294', '50759289', '50759289', '43906590', '43906590', '67423907', '17528756', '67423907', '17528756', '28067077', '34438368', '28067077', '34438368', '64969096', '74540257', '39103579', '64969096', '74540257', '39103579', '33221316', '33221316', '90328462', '18862318', '53055695', '90328462', '18862318', '60636812', '53055695', '60636812', 'R52030634', '95121802', '95121802', 'R38386327', 'R38386327', 'R72055584', '85656242', '13520441', '13520441', '85656242', '45169519', '91426739', '91426739', '45169519', 'R72055584', '16826316', '16826316', '62095767', '64750845', '62095767', '64750845', '74216658', '74216658', '39741669', '39741669', '58077284', '58077284', '77867417', '77867417', '48230428', '54439205', '48230428', '54439205', '47614898', '12864714', '47614898', '12864714', '96418683', '96418683', '98967205', '98967205', '31514982', '45793488', '31514982', '45793488', '11335157', '51594992', '11335157', '51594992', '92038346', '92038346', '84416775', '55279675', '84416775', '55279675', '50782986', '17244005', '50782986', '17244005', '62528313', '25389132', '25389132', '62528313', '67396021', '67396021', '68125773', '79859167', '68125773', '79859167', '44613871', '44613871', '83340459', '38158095', '83340459', '38158095', '26001062', '26001062', '94191412', '25490635', '94191412', '25490635', '78804622', 'R20272532', 'R94086530', 'R94086530', 'R94086530', '40843532', '64914966', '64914966', '27313452', 'R62458622', '67511288', '72369849', '67511288', '72369849', '79982510', '67526862', '79982510', '67526862', '60478238', '88496640', '60308148', '60308148', '14404384', '32699234', '14404384', '32699234', '48664456', '31073280', '31073280', '18758297', '18758297', '19498871', '32707027', '58982051', '19498871', '32707027', '49418539', '49418539', '19398014', '19398014', '66926683', '66926683', '10534686', '10534686', '38561484', '41495513', '38561484', '41495513', '67404463', '18545957', '44406893', '18545957', '44406893', '48467882', '57813214', '48467882', '69541579', '84489175', '84489175', '69541579', '80664179', '80664179', '33490332', '21885255', '21885255', '33490332', '48063780', '48063780', '52589008', '45672931', '52589008', '56562305', '13599200', '56562305', '13599200', '70657273', '70657273', '16885014', '13897136', '33816760', '33816760', '58722490', '15332148', '58722490', '80426992', '15332148', '63039274', '80426992', '63039274', '76428655', '26738642', '26738642', '76428655', '65237439', '65237439', '54240154', '54240154', '13176431', '13176431', '52749344', '72974810', '52749344', '55720580', '72974810', '55720580', '72075271', '72075271', '48677686', '48677686', '69812280', '15125517', '43260877', '15125517', '69812280', '29415646', '46201368', '79110636', '46201368', '29415646', '43260877', '79110636', '58229987', '60939069', '58229987', '60939069', '46197654', '42772056', '46197654', '42772056', '78006920', '81178135', '78006920', '81178135', '11597705', '74744239', '11597705', '74744239', '52025657', '34279932', '52025657', '34279932', '41588443', '71948831', '41588443', '71948831', '45977963', '59377644', '45977963', '59377644', '23258535', '23258535', '53558524', '53558524', '82850414', '46524371', '82850414', '46524371', '80429617', '30329111', '80429617', '30329111', '61743734', '70304515', '61743734', '70304515', '60354253', '60354253', '64828139', '64828139', '53052800', '47722386', '53052800', '47722386', '92585846', '92585846', '92585846', '45246994', '45246994', '88804410', '88804410', '60664328', '60664328', '68046763', '68046763', '40845790', '40845790', '54625602', '20186778', '75141861', '75141861', '20186778', '13514090', '29123832', '13514090', '29123832', '40398151', '16637306', '40398151', '16637306', '96661534', '84701825', '96661534', '84701825', '38261464', '18674590', '38261464', '18674590', '13578402', '80324830', '13578402', '80324830', '75652362', '43985645', '75652362', '43985645', '27162903', '58400943', '27162903', '58400943', '22603074', '93888256', '93888256', '22603074', '73897703', '73897703', '38874222', '38874222', '53718409', '53718409', '80798827', '29594351', '80798827', '29594351', '30336833', '61123756', '30336833', '61123756', '52662843', '52662843', '37633771', '76189056', '37633771', '78180685', '76189056', '78180685', '91808578', '51560468', '91808578', '51560468', '95841730', '95841730', '99457743', '99457743', '22360271', '22360271', '47028062', '47028062', '85106913', '85106913', '39269786', '39269786', '85899507', '34041841', '85899507', '34041841', '51182565', '22211001', '51182565', '22211001', '12463696', '44250567', '44250567', '83865258', '77100359', '83865258', '77100359', '55161635', '25280157', '55161635', '25280157', '60350467', '60350467', '67277564', '67277564', '26720485', '26720485', '87167356', '87167356', '71938505', '71938505', '92018082', '92018082', '24285410', '24285410', '36180935', '36180935', '10592671', '10592671', '45442177', '45442177', '95778766', '95778766', '49094204', '49094204', '17630952', '17630952', '14260668', '14260668', '30430703', '79948997', '30430703', '79948997', '29456111', '29456111', '88939681', '88939681', '78683411', '78683411', '15776351', '15776351', '11304694', '11304694', '77009169', '77009169', '33405998', '33405998', '78455369', '78455369', '11542431', '11542431', '88500489', '88500489', '14466750', '50230677', '14466750', '50230677', '84325830', '94814401', '84325830', '22194808', '54982415', '22194808', '74361791', '74361791', '73016539', '73016539', '34893961', '88640946', '60393220', '60393220', '88640946', '78921274', '78921274', '10766327', '10766327', '68386341', '68386341', '27227879', '27227879', '25276721', '25276721', '10659163', '44824862', '10659163', '44824862', '99953005', '77383292', '84844505', '43755876', '84844505', '43755876', '38375220', '38375220', '84293976', '84293976', '37949745', '52257179', '37949745', '52257179', '21489643', '94835884', '21489643', '94835884', '96197697', '26568456', '96197697', '26568456', '57442241', '57442241', '17984447', '17984447', '31558325', '21873376', '31558325', '21873376', '23690193', '23690193', '92162691', '92162691', '19037126', '58963932', '19037126', '56471863', '56471863', '33207339', '33207339', '41012833', '62672819', '41012833', '62672819', '27140223', '27140223', '28426922', '28426922', '53946989', '57668604', '53946989', '57668604', '84842573', '72879181', '84842573', '72879181', '34398270', '34398270', '76555644', '26083540', '26083540', '76555644', '32925634', '59901050', '59901050', '32925634', '47514568', '47514568', '35945022', '35945022', '18034403', '18034403', '66903366', '66903366', '88053663', '88053663', '59247221', '59247221', '90908705', '92487015', '90908705', '92487015', '64704327', '64704327', '79317631', '79317631', '69437872', '69437872', '96542758', '25151925', '96542758', '25792055', '25792055', '83225405', '83225405', '73625884', '73625884', '28366615', '28366615', '56261801', '56261801', '95883384', '95883384', '25792055', '25792055', '94569630', '94569630', '20920660', '20920660', '85834753', '85834753', '28544682', '28544682', 'R66686403', '89398106', '13098030', '86705410', '86705410', '13098030', '52862354', '52862354', '91067445', '91672158', '91672158', '91067445', '29182911', '29182911', '31679608', '31679608', '39613300', '39613300', '14512822', '14512822', '63860419', '97117617', '63860419', '97117617', '44299770', '44299770', '97313921', '61836090', '97313921', '61836090', '73269939', '27120085', '73269939', '27120085', '88159647', '22865078', '88159647', '22865078', '97724129', '97724129', '54425564', '54425564', '54422616', '68687585', '54422616', '94122987', '68687585', '94122987', '74700196', '74700196', '97724129', '97724129', '79314416', '79314416', '23268683', '23268683', '10819152', '73358164', '10819152', '73358164', '93992482', '93992482', '25817021', '25817021', '78132042', '94716798', '78132042', '94716798', '39342764', '31207981', '39342764', '31207981', '20937073', '56966191', '20937073', '56966191', '78574877', '78574877', '73227272', '73227272', '68140903', '45590869', '68140903', '45590869', '23939070', '23939070', '36436259', '36436259', '13986584', '51741437', '13986584', '51741437', '83531751', '75395589', '75395589', '16499583', '16499583', '16499583', '16499583', '16499583', '87923244', '87923244', '80836807', '80836807', '57828789', '57828789', '59647555', '59647555', '25621283', '25621283', '12291169', '12291169', '77460516', '79036899', '77460516', '79036899', '85278117', '85278117', '99356224', '99356224', '95728672', '64674381', '95728672', '64674381', '46983877', '41719935', '50579508', '50579508', '46983877', '92800902', '36502527', '92800902', '36502527', '27018493', '27063178', '27018493', '27063178', '32358319', '85170953', '32358319', '85170953', '11849340', '15729196', '15729196', '11849340', '19292063', '48831827', '19292063', '48831827', '80640818', '80640818', '96751134', '96751134', '13079744', '13079744', '19504211', '19504211', '31294544', '31294544', '40533481', '40533481', '57591558', '57591558', '14637842', '14637842', '80741106', '67363199', '67363199', '80741106', '35794699', '35794699', '98709091', '98709091', '33836582', '33836582', '12222621', '12222621', '68674810', '68674810', '62642052', '62642052', '67553325', '67553325', '75605112', '75605112', '65996997', '65996997', '99309511', '54858027', '99309511', '54858027', '80526308', '80526308', 'R69204644', '23080767', '19310526', '23080767', '19310526', '68869331', '64026372', '68869331', '64026372', '49557968', '49557968', '99077636', '99077636', '37295682', '37295682', '57170702', '74364998', '17583415', '57170702', '69041734', '74364998', '17583415', '69041734', '59283340', '46166591', '59283340', '46166591', '64388690', '94322057', '64388690', '94322057', '50282737', '22916589', '22916589', '50282737', '12255555', '12255555', '12255555', '12255555', '12255555', '38131524', '38131524', '95437244', '20295655', '31177084', '31177084', '20295655', '20295655', '20295655', '20295655', '68416806', '68416806', '72764095', 'R54076611', '27084705', '27084705', '25013165', '22157626', '22157626', '87542851', '87542851', '18770061', '18770061', '84112952', '57333662', '57333662', 'R82005231', '92805574', 'R37984671', 'R40022761', 'R21287599', '67824879', '40891031', '67824879', '40891031', '35501486', '82612691', '35501486', '82612691', '78175690', '30966204', '78175690', '30966204', '93515954', '22883767', '93515954', '22883767', '32654308', '32654308', '56498185', '56498185', '71362418', '35955958', '71362418', '35955958', '40715212', '40715212', '40715212', '40715212', '18428964', '18428964', '52876898', '10603329', '52876898', '10603329', '76996134', '99455996', '76996134', '99455996', '78356172', '88486312', '88486312', '93104711', '19640565', '76651911', '39923915', '93104711', '19640565', '39923915', '76651911', '47054059', '15282137', '47054059', '15282137', '91424787', '87813736', '87813736', '35751299', '35751299', '82306071', '58367782', '82306071', '58367782', '44566970', '94670150', '94670150', '94670150', '13305494', 'R87184275', '33383153', '33383153', '33663364', '28399630', '28399630', '33663364', '23990218', '23990218', '29356825', '29356825', '69623213', '87950712', '69623213', '87950712', '61519296', '30306363', '61519296', '30306363', '91733581', '20224543', '94820547', '15170333', '91733581', '20224543', '94820547', '15170333', '54197386', '54197386', '95996890', '95996890', '98345908', '88524419', '88524419', '98345908', '75210492', '16192824', '16192824', '75210492', '67422220', '63027932', '34229289', '64902594', '63027932', '67422220', '64902594', '34229289', '33297446', '89370651', '33297446', '89370651', '78473606', '51026811', '78473606', '51026811', '29634392', '29327986', '29634392', '29460915', '29327986', '24414691', '29460915', '14009706', '41202928', '24414691', '14009706', '41202928', '93738508', '93738508', '73870101', '84334784', '34359720', '73870101', '84334784', '34359720', '71991116', '88887328', '29443350', '56408563', '88887328', '89935856', '29443350', '89935856', '71991116', '66553512', '67230951', '66553512', '26138281', '67230951', '26138281', '58927326', '58927326', '64910423', '64910423', '26106877', '61951440', '26106877', '61951440', '92184501', '38043301', '92184501', '38043301', '75729281', '75729281', '34632535', '34632535', '47848709', '47848709', '37716110', '97787261', '97787261', '37995397', '37716110', '37995397', '58586937', '26169936', '85502541', '58586937', '26169936', '85502541', '36220325', '36220325', '80415802', '73378457', '80415802', '73378457', '26867043', '64264156', '26867043', '64264156', '73724379', '92537082', '79371059', '73724379', '99207746', '92537082', '79371059', '64093707', '30064125', '99207746', '64093707', '30064125', '13770979', '88179376', '13770979', '88179376', '32393120', '97749311', '97749311', '32393120', '95539530', '42231903', '95539530', '42231903', '13314169', '16599310', '13314169', '16599310', '99670992', '50190103', '99670992', '50190103', '50382725', '85304599', '50382725', '85304599', 'R25750702', '62035480', '89418839', '62035480', '89418839', '16855288', '38406856', '16855288', '38406856', '18492148', '18492148', '51162991', '14396109', '14396109', '82994555', '36236193', '82994555', '36236193', '85904786', '85904786', '90447136', '13974606', '90447136', '13974606', '45364622', '82251181', '45364622', '82251181', '36916606', '67328254', '36916606', '67328254', '24625558', '24625558', '52806294', '52806294', '10796966', '79479057', '10796966', '79479057', '21523727', '95367261', '21523727', '95367261', '81766327', '98397990', '81766327', '98397990', '53971291', '95371922', '53971291', '95371922', '35260099', '33224145', '33224145', '70657726', '96743877', '97554865', '35260099', '16201662', '70657726', '96743877', '97554865', '16201662', '56672230', '96017306', '56672230', '96017306', '82736334', '82736334', '42051873', '42051873', '40787770', '40787770', '46035718', '74720710', '46035718', '74720710', '88186896', '72773682', '88186896', '72773682', '91584036', '91584036', '94981092', '94981092', '85135476', '51022278', '52154271', '85135476', '51022278', '69856436', '52154271', '69856436', '85717220', '84395378', '85717220', '84395378', '46056051', '62061718', '62061718', '20119925', '79063822', '20119925', '79063822', '79145347', '39201101', '40569723', '24998812', '29271950', '79145347', '40569723', '39201101', '24998812', '29271950', '61325381', '61325381', '32443168', '74417581', '66740323', '12815214', '74417581', '66740323', '32443168', '21139157', '12815214', '65350015', '21139157', '65350015', '23823874', '61256459', '13304160', '23823874', '61256459', '13304160', '14547428', '14547428', '65384528', '52984533', '61820602', '65384528', '52984533', '61820602', '98914560', '98914560', '23688220', '23688220', '44182920', '23165564', '44182920', '46566529', '23165564', '46566529', '64297227', '64297227', '49713683', '24285924', '49713683', '24285924', '93021678', '49569132', '93021678', '49569132', '17756135', '82779945', '97175131', '68479393', '76580911', '78417696', '99946997', '68479393', '97175131', '92280574', '80226947', '99946997', '92280574', '80226947', '11550227', '58792415', '11550227', '19949966', '58792415', '45982978', '19949966', '45982978', '21483216', '30360690', '21483216', '45121563', '30360690', '15575789', '45121563', '15575789', '26315221', '26315221', '22346925', '22346925', '55300339', '92751722', '29206120', '29206120', '12190774', '12190774', '94034206', '94034206', '40371875', '40371875', '55360397', '55360397', '81864285', '28274763', '81864285', '28274763', '99589255', '99589255', '38590924', '38590924', '66812217', '66812217', '72699335', '72699335', '20248376', '20248376', '49322282', '49322282', '64102978', '90663518', '64102978', '90663518', '82985774', '82985774', '67138145', '67138145', '97690390', '97690390', '31109136', '31109136', '16227607', '16227607', '84181308', '38401712', '44694383', '38401712', '44694383', '85032809', '85032809', '23337772', '23337772', '33143399', '33143399', '89090061', '89090061', '36318038', '36318038', '61364512', '61364512', '83601533', '83601533', '54992794', '50579077', '54992794', '50579077', '12923766', '12923766', '66165623', '66165623', '57279995', '57279995', '97294208', '56273440', '97294208', '56273440', '15767398', '35028345', '15767398', '35028345', '14778962', '55844840', '14778962', '55844840', '24186493', '18871602', '18871602', '24186493', '60608056', '78961286', '44552529', '60608056', '78961286', '44552529', '22625179', '22625179', '39580977', '75218356', '39580977', '75218356', '89188707', '89188707', '11534523', '50714042', '11534523', '50714042', '54814171', '50211744', '50211744', '54814171', '40449616', '91424787', '91424787', '91424787', '91424787', '91424787', '91424787', '91424787', '51274706', '51274706', '29639300', '93840802', '73203457', '73203457', 'R74403286', '74841829', '74841829', '68223540', '68223540', '70159157', '70159157', '90852074', '90852074', '69869993', '69869993', '19164700', '19164700', '12583139', '88919966', '22584883', '22584883', '28239590', '28239590', '87324798', '39275376', '48379780', '87324798', '21836717', '39275376', '48379780', '21836717', '20234813', '27126382', '20234813', '27126382', 'R14068065', '26039260', '30534260', '30534260', 'R46345054', '31017934', '31017934', '13431034', '57949103', '13431034', '57949103', '32967629', '75935840', '75935840', '26228079', '26228079', '31733802', '86731240', '31733802', '86731240', '84626880', '70650776', '84626880', '70650776', '99325606', '28610219', '28610219', '99325606', '50777960', '50777960', '43732925', '43732925', '43732925', '43732925', '43732925', '32967629', '32967629', '42007620', 'R46319142', '72357917', '10446669', '72357917', '10446669', '94809551', '70219405', '94809551', '70219405', '42627359', '36309745', '42627359', '63934830', '36309745', '23093341', '63934830', '23093341', '14082596', '64610973', '14082596', '64610973', '57670527', '74076434', '57670527', '74076434', '98575707', '70763254', '98575707', '70763254', '74169687', '14801679', '74169687', '14801679', 'R40982586', 'R40982586', 'R40982586', 'R40982586', '11422319', '69037499', '11422319', '69037499', '17800230', '85431349', '17800230', '85431349', 'R40982586', '45382993', '73593820', '45382993', '73593820', 'R95177573', '27422342', '67993662', '27422342', '67993662', '17289385', '39311511', '17289385', '39311511', '50697894', '50697894', '78972970', '96232424', '78972970', '33950118', '23791770', '22563589', '33950118', '96232424', '23791770', '22563589', '54016231', '77366965', '54016231', '63279303', '85399469', '77366965', '63279303', '85399469', '46123392', '46123392', '62107691', '26305263', '62107691', '26305263', '96526979', '79153778', '96526979', '79153778', '69158269', '22025149', '69158269', '22025149', '98884686', '56796312', '98884686', '56796312', '96617331', '96617331', '96617331', '96617331', '96617331', '96617331', '96617331', '96617331', '15626742', '15626742', '53889744', '16756917', '53889744', '16756917', '64035176', '26578977', '86923155', '20766162', '86923155', '20766162', '18761581', '60863732', '18761581', '60863732', '66440720', '66440720', '18151363', '18151363', '86751733', '80791255', '34157418', '80791255', '34157418', '62918069', '82269498', '53630760', '62918069', '53630760', '12186261', '28243383', '63248713', '29851744', '28243383', '69485483', '69485483', '45504219', '45504219', '83941465', '37627625', '83941465', '37627625', '75770543', '75770543', '43852451', '43852451', '69091829', '79757624', '69091829', '46221647', '74689485', '46221647', '74689485', '91333203', '91333203', '91333203', '91333203', '91333203', '91333203', 'R91719214', '83380723', '83380723', '83380723', '82640320', '58139616', '82640320', '60964203', '63820491', '36497660', '87248956', '10907410', '17175411', '63820491', '36497660', '87248956', '10907410', '63853882', '17175411', '63853882', '98200216', '38571577', '38571577', '20509570', '98200216', '83380723', '26556466', '20509570', '26556466', '83380723', '83380723', '56865788', '56865788', '40674114', '73903016', '46036746', '40674114', '73903016', '46036746', '98139661', '45744101', '98139661', '95961440', '45744101', '95961440', '81616307', '13196789', '13196789', '13196789', '13196789', '13196789', '74122740', 'R15328599', '26911248', '96686267', '26911248', '96686267', '25718127', '25718127', '36589069', '36589069', '86828550', '86828550', '62019123', '62019123', '66858615', '76306931', '66858615', '76306931', '77331612', '50408285', '50408285', '48102156', '49548433', '49548433', '48102156', '43956593', '96617340', '74822061', '81323673', '81323673', '74822061', '51889441', '60292093', '51889441', '60292093', '94992391', '47322640', '94992391', '47322640', '99082117', '68453758', '99082117', '68453758', '38311839', '38311839', '68404130', '68404130', '89247152', '58273881', '84449978', '31880887', '18825505', '31880887', '18825505', '37876525', '78142233', '78142233', '55276046', '55276046', '30296868', '30296868', '46950994', '74998085', '46950994', '74998085', '20535307', '66765860', '29502847', '76062675', '29502847', '75108788', '56628112', '30783599', '56628112', '30783599', '15386143', '26930130', '26930130', '15386143', '20517519', '20517519', '89595053', '89595053', '22844668', '22844668', '85045922', '85045922', '42805094', '98263118', '98263118', '42805094', '96198202', '37215492', '96198202', '37215492', '85345844', '85345844', '85345844', '85345844', '79706835', '82893746', '79706835', '82893746', '73246678', '73246678', '66530030', '66530030', '98648243', '98648243', '90013558', '37914540', '70426760', '90013558', '70426760', '37914540', '77686043', '77686043', '35926690', '35926690', '97893032', '97893032', '61726600', '61726600', '78903996', '78903996', '16782039', '16782039', '50200933', '50200933', '48399467', '50414540', '50414540', '49108050', '38565691', '49108050', '38565691', '55150422', '57892357', '55150422', '57892357', '45707628', '27240581', '45707628', '27240581', '51975909', '51975909', '58850885', '58850885', '96176692', '99588209', '96176692', '99588209', 'R75556111', '96617339', '63642072', '63642072', '63642072', '63642072', '74459669', '75644705', '35095093', 'R26481138', 'R67880790', '60117084', '69546950', '58957747', '69546950', '58957747', '45927156', '36286179', '36286179', '45927156', 'R68553299', 'R41717409', 'R68553299', '37736000', 'R81880058', '37736000', '37736000', '37736000', '37736000', '37736000', '31185858', '92810153', '31185858', '92810153', '36810835', '68724252', '36810835', '68724252', '40556517', '39104155', '40556517', '39104155', '14178010', '14178010', '56912321', '56912321', '15017963', '14423691', '15017963', '14423691', '57172955', '38829453', '57172955', '38829453', '11886006', '42960628', '42960628', '11886006', '47069857', '46107263', '94863027', '41561346', '96085463', '94863027', '46107263', '41561346', '96085463', '67979297', '67979297', '46406074', '46406074', '46523442', '93523963', '93523963', '46523442', '93742213', '93742213', '58951470', '58951470', '93924770', '93924770', '66221608', '69884753', '66221608', '69884753', '65672857', '69754385', '26666474', '53177943', '61202135', '65672857', '53177943', '27706185', '69754385', '61202135', '27706185', '26666474', '34698130', '99537372', '99537372', '34698130', '55671505', '69883690', '55671505', '69883690', '85554831', '32323452', '32323452', '85554831', '11292179', '11292179', '63502344', '38238199', '63502344', '61043231', '17353493', '97396312', '38238199', '17353493', '97396312', '61043231', '43020750', '16895445', '43020750', '16895445', '34528268', '34528268', '45731194', '11235745', '52178426', '25334531', '15110843', '11235745', '45731194', '52178426', '15110843', '15024273', '25334531', '15024273', '50978134', '32489775', '50978134', '32489775', '17826610', '34026562', '20818111', '28940736', '17826610', '34026562', '28940736', '20818111', '34518107', '79863019', '44749048', '34518107', '35258440', '44749048', '79863019', '35258440', '56248603', '56248603', '61098626', '61098626', '96799554', '96799554', '42208824', '39814966', '12641160', '72751706', '12641160', '42208824', '39814966', '54114376', '44769905', '44769905', '54114376', '18858655', '53428261', '18858655', '53428261', '40815128', '40815128', '47273044', '47273044', '64276424', '64276424', '62031015', '62031015', '11631117', '51219453', '11631117', '51219453', '31179322', '27206254', '31179322', '27206254', '42177808', '66775373', '42177808', '66775373', '46960706', '16453373', '46960706', '16453373', '68381806', '85352289', '68381806', '79689893', '55122069', '55122069', '79689893', '75467000', '22247427', '75467000', '22247427', '34357956', '34357956', '65978417', '37240683', '95794204', '95794204', '37240683', '65147151', '65147151', '67864500', '67864500', '78885639', '42386851', '42386851', '78885639', '77872147', '62652512', '77872147', '62652512', '32223882', '32223882', '85475660', '75726796', '75726796', '71185594', '88876039', '71222414', '88876039', '71222414', '37761939', '53837111', '53837111', '37761939', '36477081', '36477081', '75964057', '75964057', '77372072', '37895310', '77372072', '66461499', '83104656', '47797224', '83104656', '47797224', '26289518', '33307623', '26289518', '33307623', '75293353', '75293353', '58452811', '80285217', '80285217', '58452811', '72257806', '24089653', '72257806', '24089653', '52755471', '44174755', '52755471', '44174755', '57631645', '31466113', '57631645', '99627015', '27158720', '81613917', '31466113', '99627015', '27158720', '81613917', '62600521', '62600521', '58219744', '55575815', '20699509', '45123760', '12989141', '55575815', '49797827', '45123760', '26848298', '49797827', '26848298', '82343614', '57184645', '82073675', '57184645', '20056163', '36381225', '65630099', '82073675', '20056163', '96393326', '65630099', '95421063', '70909126', '95421063', '70909126', '95622406', '95622406', '69368381', '69368381', '52106800', '71539462', '52106800', '71539462', '50411470', '80925653', '96617338', '37736010', '99090302', '29870969', '99090302', '29870969', '79278754', '79278754', '63289816', '63289816', '11764092', '10605724', '11764092', '10605724', '33960008', '80411414', '33960008', '84616883', '27435833', '84616883', '27435833', '24125154', '24125154', '94254580', '94254580', '20163563', '71262833', '20163563', '71262833', '26457219', '58258788', '26457219', '58258788', '11850036', '11850036', '68723002', '68723002', '96984879', '96984879', '17828237', '17828237', '96218298', '96218298', '86985287', '86985287', '62609823', '41693964', '62609823', '41693964', '36540950', '19440063', '36540950', '19440063', '49706738', '51625396', '51625396', '49706738', '80235422', '55622067', '55622067', '80235422', '96257671', '37427185', '96257671', '37427185', '91545278', '91545278', '30039162', '49887115', '58564742', '49887115', '58564742', '29472127', '59832892', '29472127', '59832892', '79024267', '57537836', '79024267', '57537836', '13082053', '54820303', '54820303', '13082053', '10354210', '78650252', '17928853', '19542899', '17928853', '10354210', '78650252', '29503384', '19542899', '32407353', '78471320', '29503384', '78471320', '32407353', '12654824', '10626032', '64939129', '10626032', '12654824', '64603176', '64603176', '15031009', '15031009', '72709906', '94056030', '72709906', '94056030', '80564131', '49853819', '58094937', '58094937', '49853819', '10713460', '10713460', '92083060', '92083060', '40061879', '40061879', '60685902', '94819701', '60685902', '94819701', '18098251', '18098251', '72917633', '72917633', '51175330', '23848618', '51175330', '23848618', '71931636', '71931636', '60784864', '18576084', '18576084', '71417946', '27711110', '71417946', '27711110', '83579408', '54083248', '83579408', '54083248', '71713199', '17480396', '17480396', '71713199', '85025064', '85025064', '73908532', '73908532', '59457568', '25257696', '25257696', '59457568', '16608063', '16608063', '29422164', '29422164', '64589678', '64589678', '73040300', '73040300', '17181825', '17181825', '12073900', '12073900', '39509294', '39509294', '56311551', '56311551', '11988405', '98008983', '17850157', '99029135', '17850157', '40165558', '15808999', '15808999', '49275095', '45434540', '45434540', '71855958', '71855958', '62340541', '64185343', '77631547', '77631547', '64185343', '92215701', '64849595', '92215701', '64849595', '83037325', '66906601', '83037325', '39185780', '11228339', '39185780', '11228339', '24237879', '53092988', '51883468', '51883468', '83529775', '83529775', '31362815', '31362815', '81937080', '38559856', '81937080', '38559856', '69826017', '81617743', '81617743', '69826017', 'R85549089', '33486040', '33486040', '98505564', '98505564', '97725833', '57528626', '97725833', '57528626', '23876206', '62600710', '23876206', '62600710', '11390859', '90889442', '90889442', '11390859', '33510321', '98185759', '33510321', '98185759', '62126380', '47317602', '62126380', '47317602', '18575964', '31491064', '18575964', '31491064', '45953837', '45953837', '27331082', '27331082', '68217109', '68217109', '66532548', '66532548', '36772526', '60554488', '36772526', '60554488', '57580745', '53939925', '57580745', '53939925', '32643633', '44043465', '32643633', '44043465', '38903225', '38903225', '13943443', '13943443', '55837832', '55837832', '77858565', '55680539', '77858565', '55680539', '95795625', '95795625', '97108090', '77017289', '97108090', '77017289', '62240099', '97279010', '62240099', '97279010', '19240313', '19240313', '75524709', '75524709', '81391646', '61160456', '81391646', '61160456', '49661295', '49661295', '84068568', '84068568', '85304299', '85304299', '25257643', '12853113', '25257643', '12853113', '12215824', '44720259', '12215824', '44720259', '85304299', '85304299', '43765824', '50912336', '43765824', '50912336', '98296866', '98296866', '92167397', '92167397', '57354806', '94952853', '57354806', '37603127', '84823669', '37603127', '84823669', '12019045', '49522201', '49522201', '12019045', '64318485', '81610670', '64318485', '81610670', '59072551', '59072551', '96237725', '96237725', '55010370', '88083001', '55010370', '88083001', '33274523', 'R40379108', '66546100', '66546100', '28487319', '52311372', '28487319', '52311372', '37586936', '37586936', '50515071', '24353161', '50515071', '24353161', '25161578', '59535541', '25161578', '59535541', '17519913', '75250082', '75250082', '17519913', '73219208', '90412166', '90412166', '73219208', '92661796', '92661796', '33848470', '33848470', '59613410', '59613410', '96193611', '96193611', '19895353', '19895353', '78690630', '78690630', '95066130', '86509561', '24223730', '61199372', '24223730', '95066130', '86509561', '61199372', '68092048', '90252244', '90252244', '68092048', '13660675', '13660675', '13660675', '72795156', '72795156', '25935963', '86799960', '25935963', '86799960', '13591784', '13591784', '42859500', '75950802', '42859500', '75950802', '37597469', '37597469', '26304556', '26304556', '20317900', '20317900', '20317900', '59683194', '59683194', '59683194', '23281024', '23281024', '23281024', '37736009', '40357533', '63329561', '40357533', '63329561', '65987351', '15098405', '15098405', '65987351', '73029313', '20700850', '73029313', '20700850', '59030176', '78861345', '59030176', '78861345', '19080163', '68733797', '19080163', '68733797', '66299460', '43436289', '92740590', '66299460', '43436289', '92740590', '35066451', '35066451', '17281587', '76702692', '17281587', '76702692', '28452324', '28452324', '28452324', '28452324', '28452324', '38737460', '94079581', '38737460', '94079581', '32885700', '17263540', '45597162', '10375818', '32885700', '17263540', '51899217', '10375818', '26691274', '45597162', '16493671', '51899217', '26691274', '16493671', '15440435', '15440435', '30704835', '27520700', '70920727', '30704835', '27520700', '67873359', '70920727', '69666184', '67873359', '67049718', '69666184', '24772977', '11383855', '34279988', '67049718', '92692773', '11383855', '20935688', '24772977', '92692773', '34279988', '12292436', '13652517', '12292436', '20935688', '13652517', '26383650', '26383650', '58494502', '58494502', '19778862', '10661966', '19778862', '89860213', '10661966', '20655089', '89860213', '20655089', '49325710', '72561743', '25003327', '49325710', '25003327', '53065903', '72561743', '53065903', '51997257', '24863167', '51997257', '24863167', '91047742', '91047742', '21850076', '45455456', '21850076', '45455456', '50891957', '20481258', '27366327', '20481258', '57433449', '50891957', '27366327', '57433449', '33961196', '14649492', '13181275', '57003189', '99574823', '42530836', '14649492', '33961196', '13181275', '57003189', '42530836', '99574823', '87287668', '35927056', '87287668', '35927056', '46818971', '46818971', '11837533', '49063028', '24772969', '11837533', '94962228', '49063028', '94962228', '24772969', '43512717', '43512717', '52813437', '68780383', '52813437', '46448906', '63824290', '68780383', '46448906', '63824290', '90289957', '69219185', '90289957', '69219185', '28556982', '26564437', '20631556', '26564437', '28556982', '20631556', '13531190', '12592462', '50637729', '13531190', '99127994', '12592462', '50637729', '99127994', '53190301', '83631030', '15355930', '53190301', '78455411', '66870324', '34376155', '83631030', '56249278', '15355930', '90649743', '78455411', '56249278', '34376155', '66870324', '90649743', '63848183', '63848183', '75490896', '75490896', '40492517', '20596622', '46081066', '40492517', '46081066', '20596622', '53747876', '81080828', '73446913', '86366303', '66754308', '94485288', '81080828', '53747876', '66754308', '94485288', '63996133', '62171192', '73446913', '86366303', '43078691', '64813929', '63996133', '63117386', '56669915', '62171192', '74973129', '74973129', '43078691', '64813929', '42429932', '31500187', '63117386', '56669915', '42429932', '31500187', '59636590', '38896373', '78878117', '59636590', '52911335', '21365799', '78878117', '66240593', '14062491', '14917796', '38896373', '86416016', '52911335', '21365799', '14062491', '38415071', '66338439', '14917796', '86416016', '28607395', '95089853', '66240593', '66338439', '28607395', '95089853', '38415071', '36063353', '95351466', '36063353', '95351466', '93851868', '93851868', '78859620', '78311609', '78859620', '69082580', '87160260', '78311609', '69082580', '87160260', '92237249', '13019687', '16766147', '16601630', '92237249', '55096265', '13019687', '55096265', '16601630', '16766147', '63818655', '63818655', '68546175', '64113010', '64113010', '68546175', '38872714', '24383225', '91284533', '14159489', '24383225', '38872714', '38912272', '91284533', '14159489', '45017297', '38912272', '37737121', '79732692', '45017297', '37737121', '58102293', '79732692', '40864745', '58102293', '40864745', '95752617', '29319695', '95752617', '29319695', '97973829', '97973829', '13045279', '13045279', '70141473', '92576555', '39681492', '92576555', '41135287', '70141473', '39681492', '41135287', '13591080', '47043790', '15655792', '13591080', '59923435', '47043790', '59923435', '15655792', '53786701', '53786701', '86175318', '94551770', '86175318', '94551770', '95511465', '51031754', '95511465', '51031754', '51962953', '28546076', '51962953', '28546076', '60340701', '44564826', '60340701', '44564826', '58960818', '20683437', '58960818', '20683437', '98026301', '25662294', '98026301', '24268102', '25662294', '45378506', '24268102', '37174407', '45378506', '90453812', '37174407', '90453812', '94024605', '69471107', '94024605', '69471107', '15433292', '15433292', '75531836', '75531836', '83653439', '28331078', '83653439', '28331078', '16930810', '53128968', '16930810', '53128968', '66335913', '66335913', '98037939', '57876016', '98037939', '57876016', '77117699', '49988524', '77117699', '49988524', '25673101', '25673101', '56261850', '56261850', '68165569', '49020443', '68165569', '49020443', '65723370', '59942844', '65723370', '59942844', '12535119', '46926809', '12535119', '46926809', '63753499', '71321442', '63753499', '71321442', '74179126', '23013387', '74179126', '23013387', '20725957', '91489458', '91489458', 'R42AE44725', '26794764', '26794764', '26794764', '63642072', '63642072', '16160852', '16160852', '16160852', 'R8731C1F3F', 'R2DA1993D0', 'R165CCF10B', '66861517', '66861517', '66861517', '46457042', '80344673', '62301173', '80344673', '62301173', '76431459', '76431459', '21495037', '46869099', '21495037', '46869099', '10142712', '67188096', '10142712', '67188096', '77408484', '77408484', '64570264', '64570264', '47031422', '47031422', '74182735', '80971795', '74182735', '80971795', '28540014', '82188759', '28540014', '82188759', '36413134', '36413134', '37428119', '37428119', '40466594', '31880002', '40466594', '31880002', '62493468', '25509205', '62493468', '25509205', '28700898', '38636853', '28700898', '38636853', '45211951', '45211951', '18985476', '18985476', '84074139', '66648643', '66648643', '84074139', '53492756', '46848125', '53492756', '46848125', '16160852', '54529786', '54529786', '16665034', '16665034', '30347940', '26035497', '30347940', '26035497', '33487573', '94673141', '71365829', '94673141', '71365829', '99128673', '44486653', '99128673', '44486653', '14309498', '82828172', '14309498', '82828172', '58205432', '58205432', '59654570', '59654570', '67954109', '58092787', '67954109', '58092787', '17962801', '17962801', '65347159', '65347159', '67134966', '67134966', '90850300', '76909014', '90850300', '76909014', '58850458', '28003236', '58850458', '28003236', '50043911', '50043911', '48921026', '14473063', '48921026', '14473063', '63794139', '63794139', '77681567', '31808173', '36263294', '31808173', '41223747', '77317847', '41223747', '77317847', '69811662', '27636473', '19812683', '19812683', '10443211', '10443211', '77681567', '77681567', '18575728', 'M-R28068934', '36125490', '36125490', '80865431', '80865431', '22419127', '22419127', '32457670', 'M-R28068934', '32457670', '13146894', '37459256', '13146894', '37459256', '18194863', '67546328', '18194863', '67546328', '57633127', '57633127', '56921317', '56921317', '33795043', '33795043', '34435048', '34435048', '80979450', '24483546', '80979450', '24483546', '51325610', '51325610', '65585835', '65585835', '37736008', '40485479', '40485479', '51954591', '51954591', '79099338', '17597519', '79099338', '17597519', '92184135', '92184135', '58156924', '58156924', '47624356', '85923918', '47624356', '85923918', '82192225', '82192225', '74325988', '74325988', '84882144', '84882144', '53840313', '53840313', '80876839', '80876839', '46810987', '25310560', '46810987', '25310560', '22959959', '22959959', '27364762', '27364762', '75264451', '75264451', '30349528', '30349528', '79839389', '79839389', '81445630', '81445630', '24072467', '11747744', '11747744', '41822965', '57020102', '41822965', '76125829', '57020102', '73626097', '76125829', '73626097', '50726540', '50726540', '83041312', '83041312', '64799501', '37013004', '64799501', '37013004', '39064715', '83039564', '83039564', '39064715', '81768700', '81768700', '50175836', '50175836', '18414385', '16800227', '10207299', '10207299', '32414797', '32414797', '55906717', '83600950', '55906717', '83600950', '81873505', '77776638', '81873505', '77776638', '76411667', '41217089', '76411667', '41217089', '53149946', '54512873', '53149946', '54512873', '37736007', '18212983', '78651867', '18212983', '76485674', '97647619', '47424296', '76485674', '97647619', '34107009', '47424296', '15488556', '47316312', '47316312', '15488556', '25215819', '68941838', '62591767', '62591767', '62591767', '62591767', '62591767', 'R69005975', '77127782', '96782923', '77127782', '96782923', '25760960', '10923882', '25760960', '10923882', '17861666', '98970142', '17861666', '50683467', '98970142', '40098267', '77491087', '50683467', '40098267', '77491087', 'R69005975', '83824312', '83824312', '27396481', '69467516', '27396481', '69467516', '80243108', '80243108', '77503479', '77503479', '53561064', '53561064', '51602666', '51602666', '56954363', '56954363', '42113675', '99006813', '99006813', '42113675', '60260823', '52085371', '52085371', '60260823', 'R73237055', '56204792', '56204792', '50735281', '31457989', '50735281', '31457989', '34032739', '94873544', '34032739', '94873544', '93631837', '93631837', '90075774', '90075774', '80416869', '80416869', '36613443', '16934017', '16934017', '36613443', '47552932', '27806776', '47552932', '27806776', '37736006', '97539679', '92524347', '92524347', '90618081', '25236006', '90618081', '25236006', '69950133', '32994824', 'R10939490', '99290559', '99290559', '29584183', '59934961', '29584183', '59934961', '97539679', '70740198', '32118695', '70740198', '32118695', '89791527', '89791527', '32521677', '32521677', 'R14068065', '70810115', '41336233', '70810115', '41336233', '50769463', '79259192', '50769463', '79259192', '94110202', '68189447', '94368118', '68189447', '94368118', '13447889', '12213832', '12213832', '13447889', '78264943', '32435054', '32435054', '35099946', '35099946', '94939355', '94939355', '99401516', '42172366', '74381163', '74381163', '56972292', '56484199', '56972292', '56484199', '16481108', '16481108', '36582198', '97631305', '36582198', '97631305', '46834283', '46834283', '14457498', '14457498', '18532098', '20841638', '20841638', '18532098', '54950045', '54950045', '13863222', '90806116', '13863222', '90806116', '96223261', '60459604', '96223261', '60459604', '10085172', '10085172', '32922189', '32922189', '79795766', '79795766', '96617335', '37736005', '26029490', '96617334', '37736004', '41853073', '20240367', '42642279', '53104363', '41853073', '20240367', '42642279', '53104363', '26004458', '26004458', '26004458', '26004458', '73783672', '75027327', '10319789', '80165848', '75027327', '10319789', '89125116', '80165848', '89125116', '78305757', '15786132', '78305757', '38839543', '31708298', '15786132', '38839543', '31708298', '96617333', '53144581', '37736003', '21375274', '21375274', '97359637', '51391813', '90053748', '81736147', '97359637', '51391813', '90053748', '81736147', '25624296', '25624296', '69113122', 'R44550905', '44899988', '91298089', '87170733', 'R35534095', '65226631', '18931803', '18931803', '18931803', '18931803', '18931803', '18931803'])
