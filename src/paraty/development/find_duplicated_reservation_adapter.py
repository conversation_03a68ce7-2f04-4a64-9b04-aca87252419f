import json
import sys

from paraty_commons_3 import date_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

sys.path.append('..')
import datetime
import requests

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, get_reservations_of_hotel




__author__ = 'nmarin'

class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)




def get_valid_reservations_from_adapter(adapter_name,  timestamp):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hoteles" % len(all_hotels))

	results = {}

	for hotel in all_hotels:

		'''if not hotel.get("applicationId", "") == "aranzazu-abando":
			continue'''

		url_push = ""

		'''if "oasis ho" not in hotel["name"].lower():
			continue'''

		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			min_datetime = date_utils.string_to_date(timestamp, format="%Y-%m-%d %H:%M:%S")

			all_push_wit_posible_errors = datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent',
																			  search_params=[
																				  ('path', '=', '/push_reservation'),
																				  ('hotel_code', '=',
																				   hotel['applicationId']),
																				  ('timestamp', '>=', min_datetime)],
																			  hotel_code="siteminder-adapter:")

			for push_res in all_push_wit_posible_errors:
				if push_res['request_id'][0] == "0":
					original_id = push_res['request_id'][1:]

					identifier_oks = list(datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent',
																							 hotel_code='siteminder-adapter:',
																							 return_cursor=True,
																							 search_params=[(
																								 'request_id',
																								 '=',
																								 original_id)]))

					if len(identifier_oks) > 0:
						print("DUPLICATED!: " + push_res['request_id'])

						if hotel['applicationId'] not in results:
							results[hotel['applicationId']] = []

						if push_res['request_id'] not in results[hotel['applicationId']]:
							results[hotel['applicationId']].append(push_res['request_id'])

	print(json.dumps(results))


if __name__ == "__main__":

	# if len(sys.argv) < 2:
	# 	print
	# 	print "Usage: python force_push_reservations_full_adapter.py adapter_name"
	# 	print
	# 	sys.exit(1)
	#
	# adapter_name = sys.argv[1]

	post_it_really = True
	modify_reservation = False
	adapter_name = "siteminder"

	timestamp = '2023-12-06 00:00:00'

	print("ALL HAS FINISHED")




	get_valid_reservations_from_adapter(adapter_name, timestamp)


