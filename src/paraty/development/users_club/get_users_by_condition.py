import json
import time

from paraty_commons_3.datastore import datastore_communicator


def get_repeated_users(users, field='email', save=True):
    result = {}
    ts = time.time()

    for index, user in enumerate(users):
        user_field = user.get(field)
        if user_field not in result:
            match_users = [u for u in users[index:] if u.get(field) == user_field]

            if len(match_users) > 1:
                result[user_field] = match_users
                if save:
                    with open("repeated_users.json", "w") as file:
                        json.dump(result, file)

        if index % 1000 == 0:
            te = time.time()
            delta_time = te - ts
            ts = time.time()
            print(f'Index: {index}. Time: {delta_time:.2f}')

    return len(result)


def get_incomplete_users(users, fields='email;username;name', save_incomplete=False, save_complete=False):
    incomplete_users = []
    complete_users = []
    ts = time.time()

    for index, user in enumerate(users):
        is_incomplete = not all([user.get(f) for f in fields.split(';')])
        if is_incomplete:
            incomplete_users.append(dict(user))
        else:
            complete_users.append(dict(user))

        if index % 1000 == 0:
            # Autosave in case of a mid execution error
            if save_incomplete:
                with open("incomplete_users.json", "w") as file:
                    json.dump(incomplete_users, file)
            if save_complete:
                with open("complete_users.json", "w") as file:
                    json.dump(complete_users, file)
            te = time.time()
            delta_time = te - ts
            ts = time.time()
            print(f'Index: {index}. Time: {delta_time:.2f}')

    if save_incomplete:
        with open("incomplete_users.json", "w") as file:
            json.dump(incomplete_users, file)
    if save_complete:
        with open("complete_users.json", "w") as file:
            json.dump(complete_users, file)

    return len(incomplete_users), len(complete_users)


if __name__ == '__main__':
    all_users = datastore_communicator.get_using_entity_and_params("UserClub", [], hotel_code='oasishoteles-corpo')

    repeated_users = get_repeated_users(all_users, field='email')
    print(f'Repeated users: {repeated_users}')

    invalid_users, valid_users = get_incomplete_users(all_users, fields='name')

    print(f'Users with invalid name: {invalid_users}')
    print(f'Users with valid name: {valid_users}')

    print('Done')
