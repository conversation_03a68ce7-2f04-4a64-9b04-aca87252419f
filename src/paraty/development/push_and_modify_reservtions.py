import datetime

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
import requests



if __name__ == "__main__":


    reservas_por_hotel = {
        "prinsotel-pineda": [
            "45928212", "59353346", "61133432",
            "62513575", "76041505", "CC-43195700"
        ],
        "prinsotel-caleta": [
            "59102961"
        ],
        "prinsotel-dorada": [
            "68503157", "CC-48436042"
        ],
        "prinsotel-alba": [

        ],
        "prinsotel-malpas": [
            "98239986"
        ]
    }

    spain_now = datetime.datetime.now()
    modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

    for hotel_code, reservas in reservas_por_hotel.items():
        for reserva in reservas:
            print(hotel_code + " - " + reserva)
            reservations = get_reservations_of_hotel({"applicationId": hotel_code}, "", "", reservation_id=reserva,
                                                     include_end_date=False,
                                                     include_cancelled_reservations=True)

            if reservations:
                print(hotel_code + " - " + reserva)
                reservation = reservations[0]

                reservation["modificationTimestamp"] = modification_timestamp
                datastore_communicator.save_entity(reservation, hotel_code=hotel_code)

                url_push = "https://siteminder-adapter.appspot.com/push_reservation?hotel_code=%s" % (hotel_code)

                url_push_reservation = url_push + "&identifier=" + reserva
                print(url_push_reservation)
                response = requests.post(url_push_reservation)
                if response.status_code == 200:
                    print("OK SITEMINDER")
                else:
                    print('ERROR reservation could not be pushed ' + reserva)


