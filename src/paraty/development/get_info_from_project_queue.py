from google.cloud import tasks_v2
from tabulate import tabulate


def list_queues_and_task_counts(project_id, location_id):
    try:
        client = tasks_v2.CloudTasksClient()
        parent = f"projects/{project_id}/locations/{location_id}"
        queues = client.list_queues(parent=parent)

        queue_info = []

        for queue in queues:
            queue_name = queue.name
            
            tasks = client.list_tasks(parent=queue_name)
            task_count = sum(1 for _ in tasks)

            queue_info.append({
                'queue_name': queue_name,
                'task_count': task_count,
	            'rate_limits': queue.rate_limits,
	            'retry_config': queue.retry_config,
	            'state': queue.state
            })

        return queue_info
    except Exception as e:
        print(f"Credentials error for project {project_id}: {e}")
        return []

def get_info_from_project_queue(projects, location_id):
    for project_id in projects:
        print(f"\nProject ID: {project_id}")
        queues_info = list_queues_and_task_counts(project_id, location_id)
        if not queues_info:
            print("No queues found or unable to access project. ")
            continue
        
        table_data = []
        for queue in queues_info:
          
            table_data.append([queue.get('queue_name'), queue.get('task_count'),  queue.get('state') ])
            
            # Print table
        print(tabulate(table_data, headers=["Queue Name", "Number of Tasks", "State"], tablefmt="grid"))


if __name__ == "__main__":
    projects = [
            'build-tools-2',
            'hotel-tools',
       	    'hotel-manager-2'
    ]
    location_id = 'europe-west1'

    get_info_from_project_queue(projects, location_id)