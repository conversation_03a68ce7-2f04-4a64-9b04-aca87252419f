import base64
import json
from urllib.parse import urlencode

from Crypto.Cipher import A<PERSON>

from paraty_commons_3.datastore import datastore_communicator

MESSAGE_KEY = b")RF@aTfUjXn2r4u9"


def generate_payment_link_reservation(hotel_code, identifier):
    amount = get_total_pending_amount_to_pay(identifier, hotel_code)
    if amount <= 0.0:
        return ""

    amount_for_link = "pending"
    booking_domain = get_configuration_property_value(hotel_code, 'Dominio asociado')
    search_domain = get_configuration_property_value(hotel_code, 'Dominio booking')
    if search_domain:
        booking_domain = search_domain

    data = {
        "identifier": identifier,
        "price": amount_for_link
    }

    encryptor = AES.new(MESSAGE_KEY, AES.MODE_CFB)
    encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
    encrypted_data = base64.urlsafe_b64encode(encryptor.iv + encrypted_data).decode("utf-8")

    request_params = {
        "namespace": hotel_code,
        "data": encrypted_data
    }

    link = booking_domain + "/booking3_tpv?" + urlencode(request_params)
    return link


def get_configuration_property_value(hotel_code, config_name):
    configuration_property = list(
        datastore_communicator.get_using_entity_and_params("ConfigurationProperty", search_params=[('mainKey', '=', config_name)], hotel_code=hotel_code))
    if configuration_property:
        return configuration_property[0].get('value')
    return ""


def get_total_pending_amount_to_pay(identifier, hotel_code):
    reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
                                                           hotel_code=hotel_code))
    if reservations:
        reservation = reservations[0]

        total_payed = 0
        total_pending = 0
        extra_info = reservation.get("extraInfo")
        if extra_info:

            extra_info = json.loads(extra_info)
            if extra_info.get("payed"):
                total_payed = float(extra_info.get("payed"))

            if extra_info.get("payed_by_cobrador"):
                total_payed += float(extra_info.get("payed_by_cobrador"))

            if extra_info.get("payed_by_tpv_link"):
                for payment_by_link in extra_info["payed_by_tpv_link"]:
                    total_payed += float(payment_by_link.get("amount"))

            total_price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))
            total_pending = total_price - total_payed
            if total_pending < 0:
                total_pending = 0

        return total_pending
