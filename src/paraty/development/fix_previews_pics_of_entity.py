import datetime
import json
import logging
import math

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels




def fix_pics():

	all_hotels = get_all_valid_hotels()


	for hotel in all_hotels:

		hotel_code = hotel['applicationId']
		if CHAIN_NAME.lower() in hotel["name"].lower():

			for entity_name in MOTHER_ENTITIES_TO_FIX:

				entities_to_fix = datastore_communicator.get_using_entity_and_params(entity_name=entity_name,keys_only=False,hotel_code=hotel_code)
				ids_to_updates = []
				entities_to_update = []
				for entity in entities_to_fix:

						key_str = datastore_utils.id_to_entity_key(hotel_code, entity.key)
						#take the pics
						entity_pics = datastore_communicator.get_using_entity_and_params(entity_name="Picture",
																						 search_params=[("mainKey", '=',key_str)],
																							 keys_only=False,
																							 hotel_code=hotel_code)
						fix_this_entity = False
						for pic_in_entity in entity_pics:
							url_pic = pic_in_entity.get("servingUrl")

							if not entity.get("pictures"):
								entity["pictures"] = []

							if url_pic not in entity.get("pictures", []):
								entity["pictures"].append(url_pic)
								#something to update!
								fix_this_entity = True


						if fix_this_entity:
							entities_to_update.append(entity)
							ids_to_updates.append(entity.key.id)

				if False and entities_to_update:
					new_ids_gerated = datastore_communicator.save_multiple_entities(entity_name, ids_to_updates, entities_to_update, hotel_code=hotel_code)








if __name__ == "__main__":

	#MOTHER_ENTITIES_TO_FIX = ["RoomType", "WenSection"]
	#CHAIN_NAME = "fuerte"

	MOTHER_ENTITIES_TO_FIX = ["RoomType"]
	CHAIN_NAME = "amare"
	fix_pics()
	logging.info("FIN")