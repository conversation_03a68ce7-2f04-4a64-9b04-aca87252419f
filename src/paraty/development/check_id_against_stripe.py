import datetime
import json

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
   get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, _get_all_hotels_metadata
import stripe

from paraty_commons_3.logging.my_gae_logging import logging

TIMESTAMP = "2023-03-01 00:00:00"
TO_TIMESTAMP = "2023-12-31 00:00:00"




def check_if_reservation_is_bad_paid(identifier, only_this_hotel, stripe_customer_id, hotel_name_filter):
   hotels = get_all_hotels()

   if only_this_hotel:
      hotel_codes = [only_this_hotel]
   else:
      hotel_codes = [hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter]


   for hotel in hotel_codes:

      only_payments_ok = []
      import_all = 0

      integration_config = get_integration_configuration_of_hotel(hotels[hotel], "STRIPE COBRADOR")
      if integration_config:
         integration_config = integration_config[0]
         config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
         private_key = config.get("private_key")

         stripe.api_key = private_key
         stripe.api_version = "2020-08-27"


         if identifier:
            customer = stripe.Customer.search(query="name:'%s'" % identifier)
            for cus in customer.get("data", []):

               payments = stripe.PaymentIntent.search(query="customer:'%s'" % cus.stripe_id)


               for payment in payments.get("data", []):
                  if payment.status == "succeeded":
                     import_all += (int(payment.get("amount")) / 100)
                     only_payments_ok.append(payment)

         if stripe_customer_id:
            stripe_id_in_res = stripe_customer_id
            payments = stripe.PaymentIntent.search(query="customer:'%s'" % stripe_id_in_res, limit=100)

            import_all = 0
            for payment in payments.get("data", []):
               if payment.status == "succeeded":
                  import_all += (int(payment.get("amount_received")) / 100)
                  only_payments_ok.append(payment)



      return payments, only_payments_ok, import_all


def get_account_manager(hotel_code):
   my_metadata = filter(lambda x: x['applicationId'] == hotel_code, _get_all_hotels_metadata())
   return next(my_metadata)['accountManager']



only_this_hotel = None
only_this_hotel = "oasishoteles-grandcancun"


only_this_identifier = None
only_this_identifier="R26738547"


name_filter = None
name_filter = 'oasis'

only_this_stripe_customer_id = None

res, only_payments_ok, all_payed = check_if_reservation_is_bad_paid(only_this_identifier, only_this_hotel, only_this_stripe_customer_id, name_filter)
print(res)
print("only_payments_ok:")
print(only_payments_ok)







