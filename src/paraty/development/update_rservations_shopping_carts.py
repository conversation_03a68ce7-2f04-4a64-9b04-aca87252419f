import datetime
import json
import logging
import math

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


COUNTRY_TAX = {"colombia": 19, "panama": 10, "republica_dominicana": 26, "argentina": 21, "eeuu": 13.5, "mexico": 12.16}

def fix_sopping_cart(reservation, tax_percent, hotel_code):

	update_reservation = False


	total_correct_price = float(reservation.get("price"))


	extra_info = json.loads(reservation.get("extraInfo", "{}"))
	shopping_cart = extra_info.get("shopping_cart")
	shopping_cart_humman = extra_info.get("shopping_cart_human_read")

	total_in_cart = 0
	new_total_in_cart = 0

	#prices in str
	if shopping_cart:
		for shop_line in shopping_cart:
			original_price = float(shop_line.get("price"))
			total_in_cart += original_price

			total_increment_amount = (original_price * tax_percent) / 100.0

			new_total_in_cart += original_price + total_increment_amount
			shop_line["price"] = str(original_price + total_increment_amount)


		if math.trunc(total_correct_price) != math.trunc(total_in_cart):
			#logging.info("DIFFERNT!!! total: %s VS total in cart = %s", total_correct_price, total_in_cart)

			if math.trunc(new_total_in_cart) == math.trunc(total_correct_price):
				#logging.info("OK!!! fixed correctly total: %s VS total in cart = %s", total_correct_price, new_total_in_cart)
				extra_info["shopping_cart"] = shopping_cart
			#else:
			#	logging.info("ERRRRORRRRR!!! Fixed GOES BAD total: %s VS total in cart = %s", total_correct_price, new_total_in_cart)


	total_in_cart = 0
	new_total_in_cart = 0

	#prices in Float
	if shopping_cart_humman:
		if shopping_cart_humman.get("total") and math.trunc(float(shopping_cart_humman.get("total"))) != math.trunc(float(total_correct_price)):



			total_in_cart = float(shopping_cart_humman.get("total"))
			total_increment_amount = (float(shopping_cart_humman.get("total")) * tax_percent) / 100.0
			shopping_cart_humman["total"] = float(shopping_cart_humman.get("total")) + total_increment_amount

			new_total_in_cart = shopping_cart_humman["total"]

			if shopping_cart_humman.get("total_without_discount"):
				total_increment_amount = (float(shopping_cart_humman.get("total_without_discount")) * tax_percent) / 100.0
				shopping_cart_humman["total_without_discount"] = float(shopping_cart_humman.get("total_without_discount")) + total_increment_amount

			total_in_room_cart = 0
			new_total_in_room_cart = 0
			for room in shopping_cart_humman.get("rooms"):
				if room.get("price"):

					total_in_room_cart += float(room.get("price"))

					total_increment_amount = (float(room.get("price")) * tax_percent) / 100.0

					room["price"] =  float(room.get("price")) + total_increment_amount

					new_total_in_room_cart += room["price"]



			if math.trunc(total_correct_price) != math.trunc(total_in_cart) or math.trunc(total_correct_price) != math.trunc(total_in_room_cart):
				#logging.info("shopping_cart_human_read DIFFERNT!!! total: %s VS total in cart = %s VS totoal in room carts: %s", total_correct_price, total_in_cart, total_in_room_cart)

				if math.trunc(new_total_in_cart) == math.trunc(total_correct_price) and  math.trunc(new_total_in_room_cart) == math.trunc(total_correct_price):
					#logging.info("OOOOOKKKKKK shopping_cart_human_read OK!!! fixed correctly total: %s VS total in cart = %s", total_correct_price, new_total_in_cart)
					extra_info["shopping_cart_human_read"] = shopping_cart_humman
					update_reservation = True
				#else:
				#	logging.info("ERRORRRRRRRR shopping_cart_human_read ERRRRORRRRR!!! Fixed GOES BAD total: %s VS total in cart = %s", total_correct_price, new_total_in_cart)


	if update_reservation:


		reservation["extraInfo"] = json.dumps(extra_info)

		resertacions_to_save = []
		ids = []
		ids.append(reservation.key.id)
		resertacions_to_save.append(reservation)

		if reservation.get("modificationTimestamp") and reservation.get("timestamp") < "2023-02-11":
			logging.info("DANGER POSIBLE BAD RATE: %s %s", hotel_code, reservation.get("identifier"))


		new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids, resertacions_to_save, hotel_code=hotel_code)
		logging.info("FIXING RESERVATION! %s %s ", reservation.get("identifier"), hotel_code)







def hotel_has_bug(hotel_code):

	hotel_country_location = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Hotel Country Location")])
	hotel_country_location_without_taxes = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Hotel Country Location Tax Included")])
	tax_to_be_paid_at_hotel = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Pay tax at hotel")])

	if hotel_country_location and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
		return True

	return False




def update_reservations_shopping_carts():

	all_hotels = get_all_valid_hotels()


	for hotel in all_hotels:

		hotel_code = hotel['applicationId']
		if "park royal" in hotel["name"].lower():
		#if "mazatlan" in hotel["name"].lower():


			from_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
			to_datetime = datetime.datetime.now().strftime("%Y-%m-%d") + " 23:59:59"

			tax_percent_to_add = 0

			if hotel_has_bug(hotel_code):


				country_config = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Hotel Country Location")])
				if country_config:
					country = country_config[0].get("value")
					tax_percent_to_add = COUNTRY_TAX.get(country)




				custom_tax = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Country location custom tax")])
				if custom_tax:
					tax_percent_to_add = float(custom_tax[0].get("value").replace("%",""))


				if tax_percent_to_add:

					print("")
					logging.info("HOTEL: %s ",  hotel["name"])


					all_reservations = get_reservations_of_hotel(hotel, from_date, to_datetime, include_modified_reservations=False)

					for reservation in all_reservations:
						fix_sopping_cart(reservation, tax_percent_to_add, hotel_code)




if __name__ == "__main__":




	update_reservations_shopping_carts()
	logging.info("FIN")