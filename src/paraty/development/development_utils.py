import datetime
import logging
import time
from datetime import timedelta
import pytz
from paraty.backups.backup_constants import DEVELOPMENT_BUCKET
from paraty.backups.backup_utils import ENTITIES_TO_ENCRYPT, backup_entities
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.storage.storage_utils import get_blob_in_path

MAX_AGE_MINUTES = 60*24*7 #7 days
AUTHENTICATION = 'Basic ZGV2OnJ1bGVz'

def get_all_from_entity(request):

	if not request.headers.get('Authorization', '') == AUTHENTICATION:
		return 'Invalid request, authentication failed', 401

	hotel_code = request.values.get("hotel_code")
	entity_name = request.values.get("entity_name")
	max_age = request.values.get("max_age", MAX_AGE_MINUTES)
	specific_fields = request.values.get("specific_fields")
	if specific_fields:
		specific_fields = specific_fields.split(',')

	logging.info("Getting all from entity %s for hotel %s" % (entity_name, hotel_code))

	if entity_name in ENTITIES_TO_ENCRYPT:
		return 'Invalid request, entity not permitted', 401

	latest_file = get_latest_file(hotel_code, entity_name, specific_fields)

	logging.info(latest_file)

	headers = {
		'Cache-Control': 'no-cache',
	}

	if latest_file:
		timestamp = latest_file.updated
		min_timestamp = datetime.datetime.now(tz=pytz.utc) - timedelta(minutes=int(max_age))
		timestamp_ok = timestamp > min_timestamp
		if timestamp_ok:
			logging.info("Returning latest file")
			headers['Content-Type'] = latest_file.content_type
			return (latest_file.download_as_bytes(), 200, headers)

	backup_entities(DEVELOPMENT_BUCKET, 'datastore_data', hotel_code, [entity_name], date_format="latest", specific_fields=specific_fields)

	latest_file = get_latest_file(hotel_code, entity_name, specific_fields)

	if latest_file:
		headers['Content-Type'] = latest_file.content_type

		try:
			return (latest_file.download_as_bytes(), 200, headers)
		except Exception as e:

			logging.error("Exception reading file at get_all_from_entity: %s" % e)
			# If the file is not ready yet, wait a bit and try again
			time.sleep(2)
			latest_file = get_latest_file(hotel_code, entity_name, specific_fields)
			return (latest_file.download_as_bytes(), 200, headers)
	else:
		headers['Content-Type'] = 'application/json'
		return ('[]', 200, headers)


def get_latest_file(hotel_code, entity_name, specific_fields=None):

	folder_path = 'datastore_data/%s/latest/' % hotel_code
	if hotel_code == 'admin-hotel':
		folder_path = folder_path.replace("/admin-hotel/", "/")

	if not specific_fields:
		base_name = '%s%s' % (folder_path, entity_name)
	else:
		base_name = '%s%s_only_%s' % (folder_path, entity_name, '_'.join(specific_fields))

	json_name = f'{base_name}.json'
	zip_name = f'{base_name}.zip'

	json_version = get_blob_in_path(json_name, DEVELOPMENT_BUCKET)
	zip_version = get_blob_in_path(zip_name, DEVELOPMENT_BUCKET)

	if json_version and zip_version:
		if json_version.time_created > zip_version.time_created:
			return json_version
		else:
			return zip_version

	return json_version or zip_version

def get_all_payments_info_list(filter_params, hotel_code, only_real_payments=True):


	payments_list_by_cobrador = list(
		datastore_communicator.get_using_entity_and_params('PaymentsReservation', filter_params,
														   hotel_code=hotel_code))

	for payment in payments_list_by_cobrador:
		payment["real_kind"] = "PaymentsReservation"

	if not only_real_payments:
		payments_list_extra_info = list(
			datastore_communicator.get_using_entity_and_params('PaymentsReservationExtraInfo', filter_params,
														   hotel_code=hotel_code))

		for payment in payments_list_extra_info:
			payment["real_kind"] = "PaymentsReservationExtraInfo"

	else:
		payments_list_extra_info = []

	combined_payments = payments_list_by_cobrador + payments_list_extra_info
	combined_payments.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

	return combined_payments

def _get_total_payed_amount_from_all_sources(extra_info_reservation):

    payed_amount = 0
    try:
        if not extra_info_reservation:
            return payed_amount
        if extra_info_reservation.get("payed", 0):
            payed_amount = float(extra_info_reservation.get("payed", "0"))
        if extra_info_reservation.get("payed_by_cobrador", 0):
            payed_amount += float(extra_info_reservation.get("payed_by_cobrador", "0"))
        if extra_info_reservation.get("payed_by_tpv_link", 0):
            payed_by_tpv_link = extra_info_reservation.get("payed_by_tpv_link", [])
            for payment in payed_by_tpv_link:
                payed_amount += float(payment.get("amount", "0"))
    except Exception as e:
        logging.warning("Exception calculating already paid in reservation")
        return payed_amount
    return payed_amount
