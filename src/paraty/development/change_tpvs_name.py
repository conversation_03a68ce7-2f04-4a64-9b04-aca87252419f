from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels
from src.paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params


def change_tpvs_name(part_of_hotel_code, namespace, tpv_name):
    if namespace:
        hotels = [namespace]
    else:
        hotels = get_all_hotels()
    for hotel_code in hotels:

        if part_of_hotel_code and part_of_hotel_code not in hotel_code:
            continue

        payment_rules = list(
            get_using_entity_and_params('PaymentConfiguration', hotel_code=hotel_code))

        configs = list(get_using_entity_and_params('ConfigurationProperty',
                                                   search_params=[('mainKey', '=', 'Use Payment Gateway')],
                                                   return_cursor=True, hotel_code=hotel_code))

        name_config_tpv = list(
            get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code, return_cursor=True,
                                        search_params=[('name', '=', tpv_name)]))

        name_config_range_tpv = list(
            get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code, return_cursor=True,
                                        search_params=[('name', '=', 'RANGE_PERCENT_'+tpv_name)]))

        if not configs or not name_config_tpv:
            continue

        new_config_range_tpv_name = ()
        if name_config_range_tpv:
            new_config_range_tpv_name = change_tpv_name_range_percent(name_config_range_tpv)

        values_tpv_name, new_configs_change = tpv_name_changes(configs, tpv_name)

        values_url, new_name_config_tpv_change = url_changes_and_add_old_url(name_config_tpv, tpv_name)

        payment_rules_dict = changes_payment_rules_equal_tpv_name(
            payment_rules, tpv_name)
        values_tpv_name += ";" + tpv_name

        final_result(values_tpv_name, hotel_code, values_url, payment_rules_dict,
                     new_name_config_tpv_change[0].get("name"), new_config_range_tpv_name)
        entrada = input("Y/N")
        if entrada.lower().strip() == "y":
            datastore_communicator.save_to_datastore("ConfigurationProperty", new_configs_change[0].id,
                                                     new_configs_change[0], hotel_code=hotel_code)
            datastore_communicator.save_to_datastore("IntegrationConfiguration", new_name_config_tpv_change[0].id,
                                                     new_name_config_tpv_change[0], hotel_code=hotel_code)
            for range in new_config_range_tpv_name:
                datastore_communicator.save_to_datastore("IntegrationConfiguration", range.id,
                                                         range, hotel_code=hotel_code)
            for rules in payment_rules_dict.get('new_payment_rules', []):
                datastore_communicator.save_to_datastore("PaymentConfiguration", rules.id, rules, hotel_code=hotel_code)
            print("Guardado")
        else:
            print("No guardado")

def final_result(values_tpv_name, hotel_code, values_url, payment_rules_dict, new_name_config_tpv_change_name, new_config_range_tpv_name):
    old_tpv_name = values_tpv_name.split(";")[0]
    new_tpv_name = values_tpv_name.split(";")[1]
    tpv_name = values_tpv_name.split(";")[2]
    old_url = values_url.split(";")[0]
    new_url = values_url.split(";")[1]
    _old_url = values_url.split(";")[2]

    if new_config_range_tpv_name:
        print("Change tpvs names with range percent")
    if new_name_config_tpv_change_name:
        print(f"New config tpv name: {new_name_config_tpv_change_name}")
    if old_tpv_name:
        print(f"{hotel_code}\n---Antiguo \nValues: {old_tpv_name} \n---Nuevo \nValues: {new_tpv_name}")
    else:
        print(f"No esta configurado el tpv {tpv_name}")
    if old_url:
        print(f"{hotel_code}\n---Antiguo \nurl: {old_url}\n---Nuevo \nurl: {new_url}\n_OLD URL: {_old_url}")
    else:
        print("No tiene urls que camiar")
    if payment_rules_dict.get('old_payment_rules'):
        print(
            f"{hotel_code}\n---Antiguo \nReglas de pago: {str(payment_rules_dict.get('old_payment_rules'))}"
            f"\n---Nuevo \nReglas de pago {str(payment_rules_dict.get('new_payment_rules'))}")
    else:
        print("No tiene reglas de pago")

def change_tpv_name_range_percent(name_config_tpv):
    name_cofig_change = []
    for config in name_config_tpv:
        name = ""
        if config:
            name = config.get("name", "")

        if name and " COBRADOR" not in name:
            name = name + " COBRADOR"

            config["name"] = name
            name_cofig_change.append(config)
    return name_config_tpv
def changes_payment_rules_equal_tpv_name(payment_rules, tpv_name):
    payment_rules_return_in_gateways_change = []
    old_payment_rules = []
    new_payment_rules = []
    rules_index = 0
    if len(payment_rules) > 0:
        for rules in payment_rules:
            payment_rule_in_gateways_change = False
            gateways = rules.get("gateways")
            new_tpv_name = ""
            if gateways:
                old_payment_rules.append(gateways)
                splits_gateways = gateways.split(";")

                new_tpv_name, payment_rule_in_gateways_change = control_splits_gateways(splits_gateways, tpv_name)

                new_payment_rules.append(new_tpv_name)
            rules["gateways"] = new_tpv_name
            if payment_rule_in_gateways_change:
                payment_rules_return_in_gateways_change.append(rules)
            rules_index = rules_index + 1
    payment_rules_dict = {}
    payment_rules_dict['payment_rules_return_in_gateways_change'] = payment_rules_return_in_gateways_change
    payment_rules_dict['old_payment_rules'] = old_payment_rules
    payment_rules_dict['new_payment_rules'] = new_payment_rules

    return payment_rules_dict

def control_splits_gateways(splits_gateways, tpv_name):
    new_tpv_name = ""
    payment_rule_in_gateways_change = False
    for payment_rule_in_gateways in splits_gateways:
        if payment_rule_in_gateways.startswith(tpv_name):
            payment_rule_in_gateways_change = True
            if new_tpv_name == "":
                new_tpv_name = tpv_name + " COBRADOR"
            else:
                new_tpv_name += ";" + tpv_name + " COBRADOR"
        elif new_tpv_name == "":
            new_tpv_name = payment_rule_in_gateways + ""
        else:
            new_tpv_name += ";" + payment_rule_in_gateways
    return new_tpv_name, payment_rule_in_gateways_change


def url_changes_and_add_old_url(name_config_tpv, tpv_name):
    confgurations = []

    if len(name_config_tpv) > 0:
        confgurations = name_config_tpv[0].get("configurations")
    if confgurations:
        exists_old_url = False
        for confg in confgurations:
            split_conf = confg.split("@@")
            if split_conf[0] == "_OLD URL ":
                exists_old_url = True

        confgurations, _old_url, new_url, old_url = control_url_changes(confgurations, tpv_name, exists_old_url)
    name = ""
    if name_config_tpv[0]:
        name = name_config_tpv[0].get("name", "")

    if name and " COBRADOR" not in name:
        name = name + " COBRADOR"

        name_config_tpv[0]["name"] = name
    values_url = old_url + ";" + new_url + ";" + _old_url

    return values_url, name_config_tpv

def control_url_changes(configurations, tpv_name, exists_old_url):
    old_url = ""
    new_url = ""
    _old_url = ""
    conf_index = 0
    for confg in configurations:
        split_conf = confg.split("@@")
        # If para el caso especial
        if split_conf[0] == "notify_url " and tpv_name == "SEQURA":
            old_url = confg
            url = split_conf[1]
            _old_url = "_OLD_URL @@" + url
            new_url, configurations, url = special_url_case(exists_old_url, configurations, _old_url, split_conf)
            new_url = url
            configurations[conf_index] = url
            break
        if split_conf[0] == "merchant_url ":
            old_url = confg
            url = split_conf[1]
            _old_url = "_OLD_URL @@" + url
            if exists_old_url is False:
                configurations.append(_old_url)
            new_url = split_conf[1].split(sep="/", maxsplit=3)
            variables = split_conf[1].split(sep="?", maxsplit=1)
            url = split_conf[0] + "@@" + new_url[0] + "//" + new_url[2] + "/cobrador/proxy/merchant_url"
            if len(variables) > 1:
                url = url + variables[1]
            new_url = url
            configurations[conf_index] = url
            break
        conf_index = conf_index + 1
    return configurations, _old_url, new_url, old_url


def special_url_case(exists_old_url, confgurations, _old_url, split_conf):
    if exists_old_url is False:
        confgurations.append(_old_url)
    new_url = split_conf[1].split(sep="/", maxsplit=3)
    variables = split_conf[1].split(sep="?", maxsplit=1)
    url = split_conf[0] + "@@" + new_url[0] + "//" + new_url[2] + "/cobrador_proxy_merchant_url"
    if len(variables) > 1:
        url = url + variables[1]

    return new_url, confgurations, url


def tpv_name_changes(configs, tpv_name):
    new_value = ""
    old_value = ""
    change_value = ""
    value_index = 0
    if configs:
        configs_values = configs[0].get("value")
        configs_values_split = configs_values.split(";")
        for configs_value in configs_values_split:
            if configs_value == tpv_name:
                old_value = configs_value
                change_value = tpv_name + " COBRADOR"
                if new_value == "":
                    new_value = tpv_name + " COBRADOR"
                else:
                    new_value = new_value + ";" + tpv_name + " COBRADOR"
                    new_value += f";{tpv_name} COBRADOR"
            elif new_value == "":
                new_value = configs_value
            else:
                new_value = new_value + ";" + configs_value
            value_index = value_index + 1
        configs[0]["value"] = new_value
        values_tpv_name = old_value + ";" + change_value

    return values_tpv_name, configs

if __name__ == "__main__":
    # NAMESPACES = "landmar-gigantes"
    # TPV_NAMES = "SERMEPA TOKEN"
    # PART_OF_NAMESPACES = ""
    change_tpvs_name('', 'hotel-nerja-club', 'SERMEPA TOKEN')
    # change_tpvs_name(PART_OF_NAMESPACES, NAMESPACES, TPV_NAMES)