import json
from datetime import datetime

import requests

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.datastore.datastore_utils import entity_id_to_alphanumeric_key

DEFAULT_TAX_DENOMINATOR = 1.10  # 10% taxes



def days_between_dates(date1_str, date2_str):
    date_format = "%Y-%m-%d"
    date1 = datetime.strptime(date1_str, date_format)
    date2 = datetime.strptime(date2_str, date_format)
    delta = date2 - date1
    return abs(delta.days)


def get_tax_denominator(extracted_data):
    if extracted_data.get('global_info').get("tax_percentage"):
        tax_denominator = round((extracted_data.get('global_info').get("tax_percentage") / 100) + 1.0, 2)
    else:
        tax_denominator = DEFAULT_TAX_DENOMINATOR
    return tax_denominator



def get_amount_for_type_and_board(supplements, type_sup, board_key):
    amount = 0.0
    for string_sup in supplements:
        if type_sup in string_sup:
            info_sup = string_sup.split(",")
            for info in info_sup:
                if "discount" in info:
                    amount += float(info.replace("discount:", ""))
                if board_key in info:
                    amount += float(info.replace(board_key + ":", ""))
    return amount

def get_adult_supplement(num_adults, board_key, supplements):
    '''
    "type:Single,discount:6.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    "type:Standar,discount:6.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    "type:3er Adulto,discount:6.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    "type:4to Adulto,discount:6.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    '''

    adultsSupplement = 0

    if (num_adults == 1):
        singleSupplement = get_amount_for_type_and_board(supplements, "Single", board_key)
        adultsSupplement = singleSupplement

    if (num_adults >= 2):
        basePerPerson = get_amount_for_type_and_board(supplements, "Standar", board_key)
        adultsSupplement = basePerPerson *2

    if (num_adults >= 3):
        thirdPersonPrize = get_amount_for_type_and_board(supplements, "3er Adulto", board_key)
        adultsSupplement += thirdPersonPrize

    #If they are more adults we apply the 4th person supplement
    if (num_adults >= 4):
        fourthPersonPrize = get_amount_for_type_and_board(supplements, "4to Adulto", board_key)
        adultsSupplement += fourthPersonPrize *(num_adults-3)

    return adultsSupplement


def get_kid_supplement(num_kids, board_key, supplements):
    '''
    "type:1er ni&ntilde;o,discount:4.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    "type:2do ni&ntilde;o,discount:4.00,ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA:0",
    '''

    kidsSupplement =0

    if (num_kids >= 1):
        singleSupplement = get_amount_for_type_and_board(supplements, "1er ni&ntilde;o", board_key)
        kidsSupplement = singleSupplement

    if (num_kids >= 2):
        basePerPerson = get_amount_for_type_and_board(supplements, "2do ni&ntilde;o", board_key)
        kidsSupplement += basePerPerson *(num_kids - 1)

    return kidsSupplement


def get_rate_periods_of_reservartion(hotel_code, extracted_data):
    SERVER = "http://127.0.0.1:8888"

    #in theory, we can take the rate from the first room (cos rate is global in the reservation)
    rate_id = extracted_data.get("room_stays")[0].get("rate_type_code")
    rate_key = entity_id_to_alphanumeric_key(rate_id, "Rate", hotel_code)

    start_date = extracted_data.get("global_info", {}).get("start")
    end_date = extracted_data.get("global_info", {}).get("end")

    url_get_params = "/integrations?action=get_rate_period&hotel_code=%s&rateKey=%s&startDate=%s&endDate=%s"
    url_get = SERVER + url_get_params % (hotel_code, rate_key, start_date, end_date)
    response = requests.get(url_get)
    if response.status_code == 200:
        print(response.content)
        return json.loads(response.content)

    return None


def get_supplements_for_occupancy(extracted_data, rate_periods):
    supplements_for_occupancy_by_room = {}
    default_index_room = 1
    for room_stays in extracted_data.get("room_stays", []):
        board_key = room_stays.get("board_key")
        room_capacity = room_stays.get("capacity", "")
        capacity_info = room_capacity.split("-")
        num_adults = int(capacity_info[0])
        num_kids = int(capacity_info[1])

        supplements_for_adults = {}
        supplements_for_kids = {}

        total_adults_sup = 0
        total_kids_sup = 0

        for day_key, supplements in rate_periods.items():
            supplements_for_adults[day_key] = get_adult_supplement(num_adults, board_key, supplements)
            supplements_for_kids[day_key] = get_kid_supplement(num_kids, board_key, supplements)

            total_adults_sup += supplements_for_adults[day_key]
            total_kids_sup += supplements_for_kids[day_key]

        supplements_for_occupancy = {"adults": supplements_for_adults,
                                   "kids": supplements_for_kids,
                                   "num_adults": num_adults,
                                   "num_kids": num_kids,
                                   "total_adults_sup": total_adults_sup,
                                   "total_kids_sup": total_kids_sup,
                                   "total_supplements": total_adults_sup + total_kids_sup}

        supplements_for_occupancy_by_room[room_stays.get("index_room", default_index_room)] = supplements_for_occupancy
        default_index_room += 1

    return supplements_for_occupancy_by_room




def trick_totals_in_global_info(extracted_data, supplements_for_occupancy):
    if extracted_data.get('global_info'):
        total_sups_added = 0
        for index_room, sup_room in supplements_for_occupancy.items():
            total_sups_added += sup_room.get("total_supplements", 0)

        if extracted_data.get('global_info').get("amount_after_taxes"):
            tax_denominator = get_tax_denominator(extracted_data)

            new_amount_after_taxes = float(extracted_data['global_info']['amount_before_taxes']) - total_sups_added
            extracted_data['global_info']['amount_after_taxes'] = new_amount_after_taxes
            new_amount_before_taxes = round(new_amount_after_taxes / tax_denominator, 2)
            extracted_data['global_info']['amount_before_taxes'] = new_amount_before_taxes

            new_tax_amount = new_amount_after_taxes - new_amount_before_taxes
            extracted_data['global_info']['tax_amount'] = new_tax_amount


def trick_room_daily_prices(room_stay, sups_of_room, tax_denominator):
    daily_sups_adult = sups_of_room.get("adults")
    daily_sups_kids = sups_of_room.get("kids")

    for daily_price in room_stay.get("daily_rates", []):
        date_key = daily_price.get("effective_date")

        daily_sup_adult = daily_sups_adult.get(date_key)
        daily_sup_kid = daily_sups_kids.get(date_key)

        total_daily_sup = daily_sup_adult + daily_sup_kid

        new_amount = float(daily_price.get("amount")) - total_daily_sup
        daily_price["amount"] = new_amount

        new_amount_before_tax = round(new_amount / tax_denominator, 2)
        new_tax_amount = new_amount - new_amount_before_tax

        daily_price["amount_before_tax"] = new_amount_before_tax
        daily_price["tax_amount"] = new_tax_amount



def trick_room_and_daily_prices(extracted_data, supplements_for_occupancy):
    tax_denominator = get_tax_denominator(extracted_data)
    default_index_room = 1
    for room_stay in extracted_data.get("room_stays", []):
        if room_stay.get("room_amount"):

            room_index = room_stay.get("index_room", default_index_room)
            sups_of_room = supplements_for_occupancy.get(room_index)

            total_sups_room = sups_of_room.get("total_supplements")

            new_room_amount = float(room_stay.get("room_amount")) - total_sups_room
            room_stay["room_amount"] = new_room_amount

            new_room_amount_before_taxes = round(new_room_amount / tax_denominator, 2)
            new_room_taxes = new_room_amount - new_room_amount_before_taxes

            room_stay["room_amount_before_taxes"] = new_room_amount_before_taxes
            room_stay["room_taxes"] = new_room_taxes

            trick_room_daily_prices(room_stay, sups_of_room, tax_denominator)

            default_index_room += 1

def get_identifier_additional_service(occupancy_type, adapter_config):
    service_code = adapter_config.get("%s as services" %occupancy_type)
    return service_code

def build_virtual_additional_service_occupancy(extracted_data, sup_room, occupancy_type, adapter_config):
    additional_service = {}

    start_date = extracted_data.get("global_info").get("start")
    end_date = extracted_data.get("global_info").get("end")

    days = days_between_dates(start_date, end_date)

    if occupancy_type == "adults":
        quantity = sup_room.get("num_adults")
        price = sup_room.get("total_adults_sup")
    elif occupancy_type == "kids":
        quantity = sup_room.get("num_kids")
        price = sup_room.get("total_kids_sup")

    if quantity:
        additional_service['name'] = occupancy_type
        additional_service['quantity'] = quantity
        additional_service['days'] = days

        additional_service['start_date'] = start_date
        additional_service['end_date'] = end_date

        additional_service['price'] = price
        additional_service['amount_after_tax'] = float(price)

        tax_denominator = get_tax_denominator(extracted_data)

        amount_before_tax_service = round(float(price) / tax_denominator, 2)

        additional_service['amount_before_tax'] = "%.2f" % amount_before_tax_service
        additional_service['currency_code'] = "EUR"

        inventory_code = get_identifier_additional_service(occupancy_type, adapter_config)
        additional_service['inventory_code'] = inventory_code

    return additional_service


def add_fake_occupancies_as_services(extracted_data, supplements_for_occupancy, adapter_name, hotel_code):
    # add new supplements and get the service code mapped in xml_channel

    additional_services = extracted_data.get("additional_services", [])

    hotel = {'applicationId': hotel_code}

    adapter_config = {}

    integration_config = get_integration_configuration_of_hotel(hotel, adapter_name)
    if integration_config:
        integration_config = integration_config[0]
        adapter_config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}

    if adapter_config:
        for index_room, sup_room in supplements_for_occupancy.items():

            #build new and fake additional service for adult/kid

            additional_service = build_virtual_additional_service_occupancy(extracted_data, sup_room, "adults", adapter_config)
            if additional_service:
                additional_services.append(additional_service)

            additional_service = build_virtual_additional_service_occupancy(extracted_data, sup_room, "kids", adapter_config)
            if additional_service:
                additional_services.append(additional_service)


    extracted_data.get("additional_services", additional_services)
def trick_json_context(hotel_code, extracted_data, adapter_name):

    # 1. get correct period (OR PERIODS) of the rate.
    rate_periods = get_rate_periods_of_reservartion(hotel_code, extracted_data)
    # 2. get the correct supplements of the regimen
    supplements_for_occupancy = get_supplements_for_occupancy(extracted_data, rate_periods)

    # 3. trick prices and add virtual additional services
    trick_totals_in_global_info(extracted_data, supplements_for_occupancy)
    trick_room_and_daily_prices(extracted_data, supplements_for_occupancy)
    add_fake_occupancies_as_services(extracted_data, supplements_for_occupancy, adapter_name, hotel_code)

    return extracted_data


def update_ocup_as_service_siteminder(request):
    body = request.get_json()
    hotel_code = body.get('hotel_code')
    adapter_name = body.get('adapter_name')

    if not adapter_name:
        adapter_name = "siteminder"

    # TODO return a json to be readed by the adapter
    return trick_json_context(hotel_code, body, adapter_name)

if __name__ == '__main__':
    '''json_to_trik = {'room_stays': [{'room_amount_before_taxes': '28.18', 'index_room': 1, 'rate_internal_name': u'PVP SITEMINDER PARCELA', 'board_key': u'ag9lfmVzdGl2YWwtZ3JvdXByFAsSB1JlZ2ltZW4YgICA7_jH9woMogEQZXN0aXZhbC12ZW5kcmVsbA', 'breakfast': False, 'room_description': u'Parcelas para caravanas y autocaravanas de 70 m\xb2, dise\xf1adas para alojar caravanas de hasta 6 metros de longitud. Cada parcela est\xe1 equipada con conexi\xf3n el\xe9ctrica para tu comodidad, permiti\xe9ndote acceder a las comodidades el\xe9ctricas que necesites durante tu visita.<br><br>\n\nLa capacidad m\xe1xima de cada parcela es de 6 personas, incluyendo tanto a adultos como a ni\xf1os y beb\xe9s, lo que la convierte en un lugar ideal para familias o grupos peque\xf1os.<br><br>\n\nLo que hace que nuestras parcelas sean a\xfan m\xe1s atractivas es su entorno natural. Cada una de ellas est\xe1 rodeada por 5 o m\xe1s \xe1rboles de especies como moreras, tipuanas o pinos blancos, proporcionando sombra y creando un ambiente sereno para tu estancia.<br><br>\n\nLa mayor\xeda de nuestras parcelas disponen de toma de electricidad, pero ninguna dispone de toma de agua y desag\xfce. Sin embargo, se pueden encontrar fuentes de agua potable por todo el camping. Recomendamos llevar un alargo (25m) para poder obtener electricidad en todos los puntos.<br><br>\n\nPara los amantes de la barbacoa, aqu\xed encontrar\xe1s una experiencia \xfanica. Las barbacoas est\xe1n permitidas, pero solo se permite el uso de carb\xf3n vegetal y \xfatiles homologados destinados a este fin. Por supuesto, la posibilidad de utilizar la barbacoa est\xe1 sujeta a las condiciones clim\xe1ticas del momento.<br><br>\n\nQueremos que disfrutes de una estad\xeda segura, por lo que es importante destacar que est\xe1 estrictamente prohibido encender cualquier tipo de fuego en el terreno de la parcela.<br><br>\n\nEn cuanto a las mascotas, son bienvenidas en nuestras instalaciones, con la excepci\xf3n de razas consideradas potencialmente peligrosas seg\xfan el listado de la Generalitat de Catalunya. Al llegar, te pediremos la documentaci\xf3n requerida para garantizar la seguridad de todos nuestros hu\xe9spedes.<br><br>\n\nPor \xfaltimo, la numeraci\xf3n de las parcelas se asigna cuidadosamente por nuestro departamento de reservas seg\xfan la disponibilidad y la organizaci\xf3n interna, asegurando que tu experiencia sea lo m\xe1s placentera posible\n', 'room_amount_payed': '9.30', 'capacity': '2-0-0', 'board_code': '', 'daily_rates': [{'effective_date': '2024-05-04', 'amount_before_tax': '28.18', 'amount': '31.00', 'tax_amount': '2.82', 'expire_date': '2024-05-05'}], 'rate_name': u'Tarifa Parcelas', 'guest_rhp': [1, 2], 'rate_type_code': 5702399944556544, 'rate_plan_code': '5702399944556544_6155339055497216', 'number_of_units': '1', 'room_amount': '31.00', 'room_name': u'Parcela 70 m\xb2', 'guest_count': [{'num_persons': 2, 'qualifying_code': '10'}], 'lunch': False, 'room_taxes': '2.82', 'decimal_places': 2, 'room_type_code': 6421182565842944, 'dinner': False, 'board_type_code': 6155339055497216, 'currency_code': 'EUR'}], 'global_info': {'end': u'2024-05-05', 'res_identifier': u'91320077', 'decimal_places': 2, 'total_guest_count': [{'num_persons': 2, 'qualifying_code': '10'}, {'num_persons': 0, 'qualifying_code': '8'}, {'num_persons': 0, 'qualifying_code': '7'}], 'promocode': None, 'rate_conditions': 'none', 'comments': u' \n PAGADO POR TPV (PAID BY GATEWAY): 9.3\u20ac\nORIGEN RESERVA: U[WEB]\n\n reservation modified by estivalvendrell', 'payed_amount': 9.3, 'start': u'2024-05-04', 'res_id_type': '14', 'comments_only_first_room': [], 'amount_after_taxes': '31.00', 'duration': 'P1D', 'tax_amount': '2.82', 'promotion': {}, 'tax_percentage': 10.000000000000009, 'additional_services': [], 'currency_code': 'EUR', 'amount_before_taxes': '28.18'}, 'creation_time': '2024-04-29T11:20:34+00:00', 'reservation_status': 'Modify', 'modificationTimestamp': u'2024-04-29 13:37:36', 'hotel_name': u'ESTIVAL: Vendrell Platja', 'count': {'num_persons': 2, 'num_children': 0, 'num_babies': 0, 'num_adults': 2}, 'guest': {'phone_formatted_ind': 'true', 'phone_default_ind': 'true', 'address_city': u'Hospitalet de Llobregat ', 'last_name': u'Medina Medina  ', 'province': u'Barcelona ', 'name': u'Hensir ', 'profile_type': '1', 'primary_indicator': 'true', 'email_default_ind': 'true', 'telephone': u'*********', 'birthday': '', 'postal_code': u'08906', 'phone_tech_type': '1', 'country_code': u'ES', 'country': 'SPAIN', 'address_line': u'C/ Hierbabuena 10 ', 'age_qualifying_code': '10', 'address_type': '1', 'doc_id': u'47923554H', 'email': u'<EMAIL>', 'email_type': '1'}, 'modification_time': '2024-04-29T11:20:34', 'hotel_code': u'estival-vendrell', 'source': {'channel_type': '7', 'channel_primary': 'true', 'code': '232900', 'company_name': 'Paraty', 'channel_name': 'Paraty'}, 'result_guest': []}
    hotel_code = "estival-vendrell"
    adapter_name = "siteminder"

    new_json_tricked = trick_json_context(hotel_code, json_to_trik, adapter_name)
    print(new_json_tricked)'''

    response = requests.post("https://prestige-adapter.appspot.com/push_reservation?hotel_code=valle-este&identifier=6746EEA6F", timeout=60)
    pass
