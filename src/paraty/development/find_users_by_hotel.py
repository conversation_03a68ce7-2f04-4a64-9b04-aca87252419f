from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.datastore import datastore_communicator


def get_valid_users(hotel_code_list):
    user_list = []

    all_users = datastore_communicator.get_using_entity_and_params('ParatyUser', hotel_code='admin-hotel',
                                                                   return_cursor=True)
    all_hotels = hotel_manager_utils.get_all_valid_hotels()

    hotel_id_list = [hotel['id'] for hotel in all_hotels if hotel['applicationId'] in hotel_code_list]

    for user in all_users:
        if user.get('enabled') and user.get('accesibleApplications'):
            for hotel_id in user.get('accesibleApplications'):
                if hotel_id in hotel_id_list and user.get('name') not in user_list:
                    user_list.append(user.get('name'))

    return user_list


if __name__ == '__main__':
    found_users = get_valid_users(
        ['ar-almerima', 'ar-arcos', 'ar-corporate', 'ar-golf-almerimar', 'ar-farnesio', 'ar-parquesur']
    )

    for u in found_users:
        print(u)