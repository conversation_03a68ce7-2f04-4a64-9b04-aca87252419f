from paraty.development.find_entity_with_condition import find_entities_with_condition
from paraty_commons_3.common_data.common_data_provider import entity_get_all, get_hotel_advance_config_item
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels



def get_hotel_by_configuration(hotel_code):

    all_filtered_hotels_by_configuration =  []

    hotel_code = hotel_code
    hotelCountryLocationConfig = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Hotel Country Location")])
    hotelCountryLocationTaxIncludedConfig = get_using_entity_and_params("ConfigurationProperty", hotel_code=hotel_code, search_params=[("mainKey","=", "Hotel Country Location Tax Included")])

    if len(hotelCountryLocationConfig) > 0 and len(hotelCountryLocationTaxIncludedConfig) > 0:
        return "OK"
    else:
        return "KO"

def get_all_modified_reservation_by_date(hotel_code):
    result = {}
    reservations = get_using_entity_and_params("Reservation", hotel_code=hotel_code, search_params=[("modificationTimestamp",">=","2022-07-15")])

    for reservation in reservations:
        print(hotel_code + " - %s" % reservation["identifier"] )


if __name__ == '__main__':
    all_valid_hotels = get_all_valid_hotels()
    all_filtered_hotels_by_configuration = []
    for hotel in all_valid_hotels:
        if hotel.get("applicationId"):
            status_hotel = get_hotel_by_configuration(hotel["applicationId"])
            if status_hotel == "OK":
                get_all_modified_reservation_by_date(hotel["applicationId"])



