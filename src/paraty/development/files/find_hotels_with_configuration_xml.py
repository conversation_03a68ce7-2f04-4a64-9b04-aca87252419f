from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def _get_hotels_with_integration_configuration(name):

    all_hotels = get_all_valid_hotels()

    for hotel in all_hotels:
        if hotel.get("applicationId"):
            hotel_code = hotel["applicationId"]
            integration_configuration = get_integration_configuration_of_hotel({'applicationId': hotel_code}, name)
            if integration_configuration:
                print(f'Found: {hotel_code}')


if __name__ == '__main__':
    _get_hotels_with_integration_configuration("billing")