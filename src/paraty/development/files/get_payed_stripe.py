import datetime
import json

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
   get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels
import stripe

from paraty_commons_3.logging.my_gae_logging import logging

TIMESTAMP = "2023-03-01 00:00:00"
TO_TIMESTAMP = "2023-08-20 00:00:00"


def get_cancelable_reservations(hotel_code=None, hotel_name_filter=None, days=1):
   from datetime import datetime, timedelta

   hotel_codes = [hotel for hotel in get_all_hotels() if hotel_name_filter in hotel.lower() and hotel_name_filter] if hotel_name_filter else ([hotel_code] if hotel_code else get_all_hotels())

   pending_reservations = {}

   for hotel_code in hotel_codes:
      pending_reservations[hotel_code] = []
      try:
         pep_paylinks = list(get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code,return_cursor=True, search_params=[('name', '=', 'PEP_PAYLINKS COBRADOR')]))[0]
      except:
         logging.info(f'Hotel {hotel_code} no tiene configurado PEP_PAYLINKS COBRADOR')
         pep_paylinks = {}

      if pep_paylinks:
         expire_hours_link = get_expire_hours_link(pep_paylinks)
         reservations = list(get_using_entity_and_params('Reservation', hotel_code=hotel_code, return_cursor=True, search_params=[('timestamp', '>', (str(datetime.now() - timedelta(days=days))).split(' ')[0])])) or logging.info(f'No hay reservas en {hotel_code} en los últimos {days} días') or []
         for reservation in reservations:
            today = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            status_reservation = json.loads(reservation.get('extraInfo', '')).get('status_reservation')
            reservation_timestamp = get_fixed_timestamp(reservation, expire_hours_link)
            reservation_startDate = get_fixed_startDate(reservation)
            if status_reservation == 'pending' and ((reservation_timestamp < today) or (reservation_startDate < today)) and not reservation.get('cancelled'):
               pending_reservations[hotel_code].append(reservation.get('identifier'))
   return {key: value for key, value in pending_reservations.items() if value}


def check_if_reservation_is_payed(reservation, hotel, cancel_reservations=False):
   hotels = get_all_hotels()
   integration_config = get_integration_configuration_of_hotel(hotels[hotel], "STRIPE COBRADOR")
   if integration_config:
      integration_config = integration_config[0]
      config = {i.split(" @@ ")[0]: i.split(" @@ ")[1] for i in integration_config.get("configurations", [])}
      private_key = config.get("private_key")

      stripe.api_key = private_key
      stripe.api_version = "2020-08-27"

      integration_config_avalon = get_integration_configuration_of_hotel(hotels[hotel], "avalon")

      if integration_config_avalon:
         integration_config_avalon = integration_config_avalon[0]
         integration_config_avalon = dict(integration_config_avalon)

         for key in integration_config_avalon:
            if isinstance(integration_config_avalon[key], list):
               r = {}
               for values in integration_config_avalon[key]:
                  values = values.split(" @@ ")
                  if len(values) == 2:
                     r[values[0]] = values[1]
               integration_config_avalon[key] = r
         integration_config_avalon = integration_config_avalon
         integration_config_avalon = integration_config_avalon.get("configurations")

      reservations = get_reservations_of_hotel(hotels[hotel], TIMESTAMP, TO_TIMESTAMP, reservation_id=reservation, include_end_date=True)
      print("____%s____" % hotel)
      result_data = []

      for reservation in reservations:
         identifier = reservation.get("identifier")
         num_rooms= reservation.get("numRooms")
         if "-" in identifier:
            identifier = identifier.split("-")[-1]
         try:
            extra_info = json.loads(reservation.get("extraInfo", "{}"))
         except:
            extra_info = {}

         customer = stripe.Customer.search(query="name:'%s'" % identifier)
         concepto = "ANTICIPOS"
         extras = False
         tipo_cobro = "VISA"

         if extra_info.get("additional_services_keys"):
            extras = True
         for cus in customer.get("data", []):

            payment_methods = stripe.PaymentMethod.list(customer=cus.stripe_id)
            payments = stripe.PaymentIntent.search(query="customer:'%s'" % cus.stripe_id)


            for payment in payments.get("data", []):
               if payment.status == "succeeded":
                  caja = "RESERVAS " + payment.currency.upper()
                  if payment.currency.upper() == "MXN":
                     caja = "RESERVAS"
                  import_all = (int(payment.get("amount")) / 100)/ num_rooms
                  for i in range(1, num_rooms+1,1):
                     result = {}
                     result["Hotel"] = integration_config_avalon.get("hotel_id")
                     if extra_info.get("external_identifier"):
                        result["Reserva"] = extra_info.get("external_identifier")[i-1].get("Reserva")
                        result["Linea"] = extra_info.get("external_identifier")[i-1].get("Linea")
                     result["FechaIngreso"] = str(datetime.datetime.fromtimestamp(payment.created))
                     result["Importe"] = import_all
                     result["TipoMovimiento"] = "3"
                     result["TipoCobro"] =tipo_cobro
                     result["Caja"] =caja
                     result["Concepto"] = concepto
                     result["ID"] = payment.metadata.get("Paraty Order Id") + "-" + str(i)
                     result["Divisa"] = payment.currency.upper()
                     result["Descripcion"] = "pago por pasarela stripe; " + payment.stripe_id
                     result["AplicarEstancia"] = True
                     result["AplicarExtras"] = extras
                     result["AplicarTasas"] = False
                     result["DocumentoExterno"] = ""
                     result_data.append(result)

                     for element_payment in payment.charges.data:
                        if element_payment.status == "succeeded":
                           date_transactions = str(datetime.datetime.fromtimestamp(element_payment.created))
                           importe = (int(element_payment.get("amount")) / 100) / num_rooms
                           if element_payment.refunded:
                              importe = - importe
                              date_transactions = str(datetime.datetime.fromtimestamp(element_payment.refunds.data[0].created))

                              result = {}
                              result["Hotel"] = integration_config_avalon.get("hotel_id")
                              if extra_info.get("external_identifier"):
                                 result["Reserva"] =extra_info.get("external_identifier")[i-1].get("Reserva")
                                 result["Linea"] = extra_info.get("external_identifier")[i-1].get("Linea")
                              result["FechaIngreso"] = date_transactions
                              result["Importe"] =  importe
                              result["TipoMovimiento"] = "3"
                              result["TipoCobro"] = element_payment.get("payment_method_details").get("card").get("brand").upper()
                              result["Caja"] = caja
                              result["Concepto"] = concepto
                              result["ID"] =  payment.metadata.get("Paraty Order Id") + "-DEV-" + str(i)
                              result["Divisa"] = element_payment.currency.upper()
                              result["Descripcion"] = "devolución por pasarela stripe;" + payment.stripe_id
                              result["AplicarEstancia"] = True
                              result["AplicarExtras"] = extras
                              result["AplicarTasas"] = False
                              result["DocumentoExterno"] = ""
                              result_data.append(result)


         filter_params = [('reservation_identifier', '=', identifier)]
         payments_list_by_cobrador = list(
            datastore_communicator.get_using_entity_and_params('PaymentsReservation', filter_params, hotel_code=hotel))


         for payment_cobrador in payments_list_by_cobrador:
            if payment_cobrador.get("type") == "manual_extra" and payment_cobrador.get("amount") != 0:
               caja = "RESERVAS " + extra_info.get("currency").upper()
               if extra_info.get("currency").upper() == "MXN":
                  caja = "RESERVAS"
               import_all = int(payment_cobrador.get("amount")) / num_rooms

               for i in range(1, num_rooms + 1, 1):
                  result = {}
                  result["Hotel"] = integration_config_avalon.get("hotel_id")
                  result["Reserva"] = extra_info.get("external_identifier")[i - 1].get("Reserva")
                  result["Linea"] = extra_info.get("external_identifier")[i - 1].get("Linea")
                  result["FechaIngreso"] = payment_cobrador.get("timestamp")
                  result["Importe"] = import_all
                  result["TipoMovimiento"] = "gift_bono_email.html3"
                  result["TipoCobro"] = tipo_cobro
                  result["Caja"] = caja
                  result["Concepto"] = concepto
                  result["ID"] = payment_cobrador.get("order") + "-" + str(i)
                  result["Divisa"] = extra_info.get("currency").upper()
                  result["Descripcion"] = "registro manual "
                  result["AplicarEstancia"] = True
                  result["AplicarExtras"] = extras
                  result["AplicarTasas"] = False
                  result["DocumentoExterno"] = ""
                  result_data.append(result)

   if result_data:
      return reservation.get('identifier')
   else:
      pass


def get_fixed_timestamp(reservation, expire_hours_link):
   from datetime import datetime, timedelta
   return str(datetime.strptime(reservation.get('timestamp', 0), "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link)) or reservation.get('timestamp', 0)


def get_fixed_startDate(reservation):
   from datetime import datetime, timedelta
   return str(datetime.strptime(reservation.get('startDate'), '%Y-%m-%d') + timedelta(days=1))


def get_expire_hours_link(pep_paylinks):
   for configs in pep_paylinks.get('configurations', []):
      return float(configs.split(' @@ ')[1] if 'expire_hours_link' in configs else 0)


def get_pending_payed_reservations(cancelable_reservations):
   for hotel_code, reservations in cancelable_reservations.items():
      cancelable_reservations[hotel_code] = []
      for reservation in reservations:
         x = check_if_reservation_is_payed(reservation, hotel_code)
         if x:
            cancelable_reservations[hotel_code].append(x)
   return {hotel_code: id for hotel_code, id in cancelable_reservations.items() if id} or "todo ok"



# cancelable_reservations = get_cancelable_reservations(hotel_code=None, hotel_name_filter='oasis', days=10)


get_pending_payed_reservations(get_cancelable_reservations(hotel_name_filter='oasis', days=3)) # checkea todas las reservas de todos los hoteles con pep paylinks de reservas de hace x dias y si se han pagado


# check_if_reservation_is_payed('ACC661EBA', 'oasishoteles-senscancun') # checkea 1 reserva - identificador, hotel_code