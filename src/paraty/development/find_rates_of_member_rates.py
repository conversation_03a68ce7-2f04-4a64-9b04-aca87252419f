from paraty.development.find_entity_with_condition import find_entities_with_condition
from paraty_commons_3.common_data.common_data_provider import entity_get_all, get_hotel_advance_config_item
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_all_rates_for_hotel(hotel_code):
    rates_of_hotel = {}
    all_rates = get_using_entity_and_params("Rate", hotel_code=hotel_code)

    for rate in all_rates:

        if rate.get("virtualRateOf") != None:
            rates_of_hotel[rate.get("localName").replace('\t',"").lower()] = rate.id

    return rates_of_hotel



def get_all_boards_for_hotel(hotel_code):
    all_boards = get_using_entity_and_params("Regimen", hotel_code=hotel_code)
    board_list = []
    for board in all_boards:
        board_list.append(board.id)
    return board_list



def get_logged_rates(hotel_code):
    all_hotel_rates = get_all_rates_for_hotel(hotel_code)
    all_hotel_boards = get_all_boards_for_hotel(hotel_code)
    login_user_rates = get_hotel_advance_config_item(hotel, "Login user rates")
    if login_user_rates:
        print(hotel_code)
        login_user_rate = login_user_rates[0].get("value").lower().split("@")
        for valid_rate in login_user_rate:
            if valid_rate != "":
                rates_result = {}
                rate = all_hotel_rates.get(valid_rate)
                for board in all_hotel_boards:
                    rates_result[str(rate)+"-"] = board
                    print(str(rate)+"-%s"% str(board))



if __name__ == '__main__':
    required_hotels = ["Landmar Playa la Arena", "Landmar Costa Los Gigantes", "Impressive Punta Cana",
                       "Impressive Premium Punta Cana", "Impressive Playa Granada", "Impressive Zocos Lanzarote",
                       "Grupo Blaumar: Blaumar", "Best Serenade Punta Cana", "Ona hotels: Marinas de Nerja",
                       "Ona hotels: Valle Romano Golf y Resort", "Ona hotels: Mosaic", "Excel: Palm Beach",
                       "Ona hotels: Alanda Club Marbella", "Excel: Hollywood Mirage", "Ona hotels: Ogisaka Garden",
                       "Ona hotels: Cala Pi Club", "Ona hotels: Palamos", "Ona hotels: Aucanada",
                       "Ona hotels: El Marques", "Ona hotels: Garden Lago", "Ona hotels: Los Claveles",
                       "Ona hotels: Jardines Paraisol", "Excel: Beverly Heighs", "Ona hotels: Dorada El Tarter",
                       "Ona hotels: Las Rosas", "Ona hotels: Club Bena Vista", "Ona hotels: Las Brisas",
                       "Excel: Beverly Hills Club", "Ona hotels: Living Barcelona", "Bahia Blanca",
                       "Ona hotels: Village Cala D Or", "Ona hotels: Campanario", "Ona hotels: Suites Salou"]
    new_required_hotel_list = []
    for hotel in required_hotels:
        new_required_hotel_list.append(hotel.lower())

    all_valid_hotels = hotel_manager_utils.get_all_valid_hotels()
    for hotel in all_valid_hotels:
        if hotel.get("name").lower() in new_required_hotel_list:
            hotel_code = hotel.get("applicationId")
            logged_rates = get_logged_rates(hotel_code)
