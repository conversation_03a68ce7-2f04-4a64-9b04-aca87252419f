import logging

from google.cloud import appengine_admin

from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore.datastore_utils import get_location_prefix
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.email_utils import notifyExceptionByEmail
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_all_hotel_metadata, get_hotel_project_and_namespace, get_all_hotels_without_cache, _get_project_id_and_namespace
from paraty_commons_3.redis.redis_communicator import build_redis_client

REDIS_HOST = "************"
REDIS_PORT = 6666
REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"

prefix = {
    'us-central': 's~',
    'europe-west': 'e~',
    'europe-west3': 'h~', # For fuerte-club to work
    'us-east1': 'p~',
    'asia-northeast1': 'b~'
}

my_internal_redis_client = None

def get_redis_client():
    global my_internal_redis_client
    if not my_internal_redis_client:
        my_internal_redis_client = build_redis_client(REDIS_HOST, password=REDIS_PASSWORD, port=REDIS_PORT)
    return my_internal_redis_client



def get_app_location(project_id):
    client = appengine_admin.ApplicationsClient()
    app = client.get_application(name=f"apps/{project_id}")
    return app.location_id


def get_hotel_location_prefix(request):
    hotel_code = request.args.get("hotel_code")
    return _get_hotel_location_prefix(hotel_code)


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], ttl_seconds=3600*24, background_refresh=False)
def _get_hotel_location_prefix(hotel_code):

    redis_client = get_redis_client()
    redis_key = f"location_prefix_{hotel_code}"
    if redis_client:
        redis_value = redis_client.get(redis_key)
        if redis_value:
            return redis_value.decode('utf-8')

    project, namespace = get_hotel_project_and_namespace(hotel_code)

    # i.e. If the project is new
    if not project:
        try:
            current_hotel = get_all_hotels_without_cache().get(hotel_code, {})
            project, namespace = _get_project_id_and_namespace(current_hotel)
        except Exception as e:
            logging.warning("Probably requesting wrong hotel code")
            return 'not_found'

    location = get_app_location(project)

    if not location in prefix:
        notifyExceptionByEmail(f"Location {location} not found, for hotel_code {hotel_code}", "Location not found")
        print(f"Location {location} not found, for hotel_code {hotel_code}")
        return 'not_found'

    if redis_client:
        redis_client.set(redis_key, prefix[location])

    return prefix[location]


def _verify_location_for_hotel(location_prefix_from_metadata, hotel_code):
    location_prefix = _get_hotel_location_prefix(hotel_code)
    location_prefix_2 = get_location_prefix(hotel_code)
    if location_prefix != location_prefix_from_metadata or location_prefix_2 != location_prefix_from_metadata:
        print(f"KO: Hotel {hotel_code} has location prefix {location_prefix} but it is {location_prefix_from_metadata} in metadata")
    else:
        pass
        print(f"OK: Hotel {hotel_code} has location prefix {location_prefix}")


def _verify_location_prefixes():
    all_valid_hotels = get_all_valid_hotels()
    all_metadatas = get_all_hotel_metadata(86400)

    params = []
    for hotel in all_valid_hotels:
        hotel_code = hotel['applicationId']
        location_prefix_from_metadata = all_metadatas[hotel_code]['location_prefix']
        params.append((location_prefix_from_metadata, hotel_code))

    execute_in_parallel(_verify_location_for_hotel, params, max_concurrency=20)


if __name__ == '__main__':
    print(_get_hotel_location_prefix("habitus-gades"))
    # _verify_location_prefixes()
