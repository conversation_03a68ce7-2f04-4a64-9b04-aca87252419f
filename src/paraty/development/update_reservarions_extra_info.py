import datetime
import json
import logging
import math

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels





def add_payed_info_in_extra_info(reservations, hotel_code):
	resertacions_to_save = []
	ids = []
	for reservation in reservations:

		extra_info = json.loads(reservation.get("extraInfo", "{}"))

		payed_amount = float(extra_info.get("payed", 0))
		if extra_info.get("payed_by_cobrador"):
			payed_amount += float(extra_info.get("payed_by_cobrador"))
		if extra_info.get("payed_by_tpv_link"):
			for payment_by_link in extra_info["payed_by_tpv_link"]:
				payed_amount += float(payment_by_link.get("amount"))

		if payed_amount and not extra_info.get("yieldplanet_payed"):


			if payed_amount:
				#add normalization
				extra_info["yieldplanet_payed"] = payed_amount
				reservation["extraInfo"] = json.dumps(extra_info)

				logging.info("FIXING RESERVATION! %s %s ", reservation.get("identifier"), hotel_code)

				ids.append(reservation.key.id)
				resertacions_to_save.append(reservation)

	if resertacions_to_save and save_it_really:
		new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids, resertacions_to_save, hotel_code=hotel_code)



def update_reservations():

	all_hotels = get_all_valid_hotels()


	for hotel in all_hotels:

		hotel_code = hotel['applicationId']
		if chain_name and not chain_name in hotel["name"].lower():
			continue
		if only_hotel_code and not only_hotel_code == hotel_code:
			continue

		from_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
		to_datetime = datetime.datetime.now().strftime("%Y-%m-%d") + " 23:59:59"


		all_reservations = get_reservations_of_hotel(hotel, from_date, to_datetime, include_modified_reservations=False)
		add_payed_info_in_extra_info(all_reservations, hotel_code)




if __name__ == "__main__":

	#string contained in name. examples:
	#chain_name = "park royal"
	#chain_name = "oasis hoteles"

	save_it_really = False

	chain_name = ""
	only_hotel_code = ""


	update_reservations()
	logging.info("FIN")