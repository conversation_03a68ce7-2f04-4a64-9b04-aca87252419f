from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


if __name__ == '__main__':
    all_valid_hotels = get_all_valid_hotels()
    for hotel in all_valid_hotels:
        if hotel.get("applicationId"):
            hotel_code = hotel["applicationId"]

            result_entities = datastore_communicator.get_using_entity_and_params('PaymentConfiguration', search_params=[
            ("type_rule", "=", "early_payment")], hotel_code=hotel_code)
            for rule in result_entities:
                amount = rule.get("amount")

                if amount != "100" or rule.get("type_amount") != "percentage":
                    print("")
                    print(hotel_code + ": %s" % rule)
                    print("")
                else:
                    print("OK")



