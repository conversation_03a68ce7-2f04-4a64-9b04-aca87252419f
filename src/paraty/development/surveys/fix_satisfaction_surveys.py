from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_to_datastore, \
    delete_entity


def main():
    from_hotel_code = 'marmoris-corpo'
    to_hotel_code = 'marmoris-sintra'

    satisfaction_surveys = get_using_entity_and_params('SatisfactionForm', hotel_code=from_hotel_code)

    for survey in satisfaction_surveys:
        booking_id = survey['booking_id']

        reservation = get_using_entity_and_params("Reservation", search_params=[("identifier", "=", booking_id)], hotel_code=to_hotel_code, limit=1)
        if reservation:
            save_to_datastore('SatisfactionForm', None, dict(survey), hotel_code=to_hotel_code)
            delete_entity('SatisfactionForm', survey.id, from_hotel_code)
        else:
            continue


if __name__ == '__main__':
    main()
