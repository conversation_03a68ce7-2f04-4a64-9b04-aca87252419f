[{"adminUser": false, "currentAppId": 4635346020597760, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1687808964163, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 4635346020597760, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUU4CMRCGJxpEkAC+4JPJXkAO4IsCCUKITyQ8+7u7yVaazqYdCtyJSxBP4Kt38A6260po0sn83/z/tIcfajhLtx/wGG5E6eEMrnhF2Wh+Hz8Hb1+XdDGltmZkU6TCdk4tKWzuCtbZrnx6png62+tQ++FeCd2t5ovHZMaS62Rkc4NkrGFSCPW8Wj8govea3FfeFwtTBybsBEkWuiXrOpFGFpCLZHC2fRliIyNsFAt1o9XB4B/cVM4J9uE9oXa1qRbdarRSWiPUtVAnTn3UPsr+X5RtyRaiPE4/OZHdL24QZE86AQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFbCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNLyrIL0osySyD6Rda/6sNqKACALR5qNlrAQAA", "timestamp": 1725963843136, "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6415683650322432, "permissions": "cobrador", "timestamp": 1738692495956, "userName": "vika<PERSON><PERSON>", "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUQ7BQBCGJ6RBNcULr70AB/BCSRqNeJJ4NqpJVze7zXYs7uQS4gRe3cEddGnDJDPJ//3/n8z1BVauoHdAjaMjMT5aYJ6sMLMaz9u9v33UoRaAzSXuA4xIqhBalKg4TyTfn7PJFMw4p2Zxu8VaBINNuBx7C0kx93wVC/RmHEWEBB3N0iEatCtJ/y+7RuH5gqRgksA10RwFVqD9Sc7xUrQJbGNHpXA/1oZxjsVNCRzjaqO1kd1vVapMKiSmq0+iHzm/Afpwj+IIAQAA", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFrCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGMSQ7AlOzFNwzCvJz8vMLy5kqGNgZhBL2SEBVsYNVuacWAl0CUwuYooDWI4PLBeWmZOTCCSzYdKn2Q+ApQUgWvOLCvKLEksyy2D6hdb/agMqqAAAnrU5xz0BAAA="}, {"adminUser": false, "currentAppId": 4635346020597760, "permissions": "cobrador", "timestamp": 1730894953850, "userName": "vika<PERSON><PERSON>", "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUQ7BQBCGJ6RBNcULr70AB/BCSRqNeJJ4NqpJVze7zXYs7uQS4gRe3cEddGnDJDPJ//3/n8z1BVauoHdAjaMjMT5aYJ6sMLMaz9u9v33UoRaAzSXuA4xIqhBalKg4TyTfn7PJFMw4p2Zxu8VaBINNuBx7C0kx93wVC/RmHEWEBB3N0iEatCtJ/y+7RuH5gqRgksA10RwFVqD9Sc7xUrQJbGNHpXA/1oZxjsVNCRzjaqO1kd1vVapMKiSmq0+iHzm/Afpwj+IIAQAA", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFrCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGMSQ7AlOzFNwzCvJz8vMLy5kqGNgZhBL2SEBVsYNVuacWAl0CUwuYooDWI4PLBeWmZOTCCSzYdKn2Q+ApQUgWvOLCvKLEksyy2D6hdb/agMqqAAAnrU5xz0BAAA="}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUQ7BQBCGJ6RBNcULr70AB/BCSRqNeJJ4NqpJVze7zXYs7uQS4gRe3cEddGnDJDPJ//3/n8z1BVauoHdAjaMjMT5aYJ6sMLMaz9u9v33UoRaAzSXuA4xIqhBalKg4TyTfn7PJFMw4p2Zxu8VaBINNuBx7C0kx93wVC/RmHEWEBB3N0iEatCtJ/y+7RuH5gqRgksA10RwFVqD9Sc7xUrQJbGNHpXA/1oZxjsVNCRzjaqO1kd1vVapMKiSmq0+iHzm/Afpwj+IIAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFrCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGMSQ7AlOzFNwzCvJz8vMLy5kqGNgZhBL2SEBVsYNVuacWAl0CUwuYooDWI4PLBeWmZOTCCSzYdKn2Q+ApQUgWvOLCvKLEksyy2D6hdb/agMqqAAAnrU5xz0BAAA=", "timestamp": 1728316624128, "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OUU4CMRCGJxpEkAC+4JPJXkAO4IsCCUKITyQ8+7u7yVaazqYdCtyJSxBP4Kt38A6260po0sn83/z/tIcfajhLtx/wGG5E6eEMrnhF2Wh+Hz8Hb1+XdDGltmZkU6TCdk4tKWzuCtbZrnx6png62+tQ++FeCd2t5ovHZMaS62Rkc4NkrGFSCPW8Wj8govea3FfeFwtTBybsBEkWuiXrOpFGFpCLZHC2fRliIyNsFAt1o9XB4B/cVM4J9uE9oXa1qRbdarRSWiPUtVAnTn3UPsr+X5RtyRaiPE4/OZHdL24QZE86AQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFbCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNLyrIL0osySyD6Rda/6sNqKACALR5qNlrAQAA", "timestamp": 1722248502438, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1705586829481, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1705925291793, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 5897000985296896, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1694622616031, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OUU4CMRCGJxpEkAC+4JPJXkAO4IsCCUKITyQ8+7u7yVaazqYdCtyJSxBP4Kt38A6260po0sn83/z/tIcfajhLtx/wGG5E6eEMrnhF2Wh+Hz8Hb1+XdDGltmZkU6TCdk4tKWzuCtbZrnx6png62+tQ++FeCd2t5ovHZMaS62Rkc4NkrGFSCPW8Wj8govea3FfeFwtTBybsBEkWuiXrOpFGFpCLZHC2fRliIyNsFAt1o9XB4B/cVM4J9uE9oXa1qRbdarRSWiPUtVAnTn3UPsr+X5RtyRaiPE4/OZHdL24QZE86AQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFbCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNLyrIL0osySyD6Rda/6sNqKACALR5qNlrAQAA", "timestamp": 1707746938927, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6415683650322432, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1700743433787, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "timestamp": 1732193160385, "userName": "vika<PERSON><PERSON>", "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUQ7BQBCGJ6RBNcULr70AB/BCSRqNeJJ4NqpJVze7zXYs7uQS4gRe3cEddGnDJDPJ//3/n8z1BVauoHdAjaMjMT5aYJ6sMLMaz9u9v33UoRaAzSXuA4xIqhBalKg4TyTfn7PJFMw4p2Zxu8VaBINNuBx7C0kx93wVC/RmHEWEBB3N0iEatCtJ/y+7RuH5gqRgksA10RwFVqD9Sc7xUrQJbGNHpXA/1oZxjsVNCRzjaqO1kd1vVapMKiSmq0+iHzm/Afpwj+IIAQAA", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFrCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGMSQ7AlOzFNwzCvJz8vMLy5kqGNgZhBL2SEBVsYNVuacWAl0CUwuYooDWI4PLBeWmZOTCCSzYdKn2Q+ApQUgWvOLCvKLEksyy2D6hdb/agMqqAAAnrU5xz0BAAA="}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OUU4CMRCGJxpEkAC+4JPJXkAO4IsCCUKITyQ8+7u7yVaazqYdCtyJSxBP4Kt38A6260po0sn83/z/tIcfajhLtx/wGG5E6eEMrnhF2Wh+Hz8Hb1+XdDGltmZkU6TCdk4tKWzuCtbZrnx6png62+tQ++FeCd2t5ovHZMaS62Rkc4NkrGFSCPW8Wj8govea3FfeFwtTBybsBEkWuiXrOpFGFpCLZHC2fRliIyNsFAt1o9XB4B/cVM4J9uE9oXa1qRbdarRSWiPUtVAnTn3UPsr+X5RtyRaiPE4/OZHdL24QZE86AQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFbCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNLyrIL0osySyD6Rda/6sNqKACALR5qNlrAQAA", "timestamp": 1709303062491, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1700742962539, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 6303191343300608, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAA/02OUU4CMRCGJxpEkAC+4JPJXkAO4IsCCUKITyQ8+7u7yVaazqYdCtyJSxBP4Kt38A6260po0sn83/z/tIcfajhLtx/wGG5E6eEMrnhF2Wh+Hz8Hb1+XdDGltmZkU6TCdk4tKWzuCtbZrnx6png62+tQ++FeCd2t5ovHZMaS62Rkc4NkrGFSCPW8Wj8govea3FfeFwtTBybsBEkWuiXrOpFGFpCLZHC2fRliIyNsFAt1o9XB4B/cVM4J9uE9oXa1qRbdarRSWiPUtVAnTn3UPsr+X5RtyRaiPE4/OZHdL24QZE86AQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAA/1vzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADFbCYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNLyrIL0osySyD6Rda/6sNqKACALR5qNlrAQAA", "timestamp": 1727339916634, "configurationMap": "H4sIAAAAAAAA/1vzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "currentAppId": 5897000985296896, "permissions": "cobrador", "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1694620850631, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}, {"adminUser": false, "permissions": "cobrador", "currentAppId": 4635346020597760, "stringKeysOfValidHotels": "H4sIAAAAAAAAAE2OQU5CMRCGJxqCIkHc4MqkF5ADuFEgQYhxRcLan76XvErTvrRjxTt5CeMJ3HIH7mD7qC826WT+b/5/2s8Ddbyjq1cEjN9Y6fECvnpG3enuv75HLz+ndDKnnrYo5pBs3ZLOuXKlr6wudvX9A6XTfz+LdRhvl+l6vXy6EwvLpRYTVxqIqYaRYLoManuLhDaZ3DTeRweTAzPrGaKI3crqnJCJReQTGf3bvoqxiWFrlGUaJKuHwR+4aJwzfMT3mHrNpiwGzWittEasW6Z+moakQ5LDY9Q6aDEtIau8XiayOYLWU0fIKqD9bUt2vyiKBhleAQAA", "userName": "vika<PERSON><PERSON>", "validHotels": "H4sIAAAAAAAAAFvzloG1uIhBMCuxLFGvtCQzR88jsTjDN7GAlf3WwcNiCReZGZjcGLhy8hNT3BKTS/KLPBk4SzKKUosz8nNSKgrsHRhAgKecA0gKADF7CYN4mKe3lYJHfklqjoJjUWpeooJTTmJeciLQGj6wNUBeup5Pfl66dfeTCWf6le8zMTB6MbCWJeaUplYUMQggFPmV5ialFrWtmSrLPeVBNxMDQ0UBg0D5pRk/GBhKGGTB9rgXJeZBLXPOLy5JVEgBsoLzc4oLGeoYmBlEPnt1gFWLIbkqGKjFMa8kPy8zH6pMLGWHBFgZN1iZc2Il0N0wuYgpDmA5PrBcWGZOTiKQzIZJn2Y/AJYWgGjNL0rMUXBKTUzOgCoQarg/AVVBAVBNSWYZzAKh9b/agAoqACsMLMmMAQAA", "timestamp": 1671095772289, "configurationMap": "H4sIAAAAAAAAAFvzloG1uIhBOCuxLFGvtCQzR8+xqCix0iezuKSi8ZLszOOJc5kZGD0ZWIozq1IrChiAoJwFRFYAAHxdgEM6AAAA"}]