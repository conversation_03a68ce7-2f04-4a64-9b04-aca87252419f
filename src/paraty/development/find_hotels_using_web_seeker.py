from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils


def _hotel_uses_web_seeker(hotel, result):
    my_entities = datastore_communicator.get_using_entity_and_params(entity_name='Wysiwyg', keys_only=True, hotel_code=hotel['applicationId'])
    if len(my_entities) > 0:
        result.append((hotel['applicationId'], hotel['name']))


def _get_hotels_with_web_seeker():

    result = []
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    params = []
    for hotel in all_hotels:
        params.append((hotel, result))

    concurrency_utils.execute_in_parallel(_hotel_uses_web_seeker, params, max_concurrency=20)

    return result


if __name__ == '__main__':

    hotels = _get_hotels_with_web_seeker()
    print(",".join([x[1].lower() for x in hotels]))
