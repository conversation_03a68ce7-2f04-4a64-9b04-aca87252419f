import os
import datetime
from collections import defaultdict

from paraty_commons_3.datastore import datastore_communicator

import csv


def get_posible_login_ok(users_detected, days_ago, secure_countries):
    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=days_ago)
    year_before_today = str(year_before_today)

    headers = ["timestamp", "user", "ip", "status", "ip_geolocation"]

    file_name = f'access_users_possible_bad.csv'
    f = open(file_name, "w")
    csv_writer = csv.writer(f)
    csv_writer.writerow(headers)

    for user in users_detected:

        last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt', [('user', '=', user), (
            'timestamp', '>', year_before_today)], hotel_code="user-seeker:")

        ip_checked = []

        for entrada in last_logging_attempt:

            ip = entrada['ip']
            geo = entrada.get('ip_geolocation', "UNKNOWN")


            if ip not in ip_checked and geo not in secure_countries:

                ip_checked.append(ip)

                ip_attemps = datastore_communicator.get_using_entity_and_params('LoginAttempt',
                                                                                          [('ip', '=', ip)],
                                                                                          hotel_code="user-seeker:")

                for ip_entrada in ip_attemps:
                    status = ip_entrada['status']
                    if status == "login_ok" and ip_entrada.get("timestamp") > "2025-04-16" and ip_entrada.get('ip_geolocation', "UNKNOWN") not in secure_countries:

                        csv_writer.writerow([ip_entrada.get(x) for x in headers])


def user_access_by_user(users, days_ago):
    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=days_ago)
    year_before_today = str(year_before_today)

    headers = ["timestamp", "user", "ip", "status"]

    file_name = f'access_users_puente_flash_rosa.csv'
    f = open(file_name, "w")
    csv_writer = csv.writer(f)
    csv_writer.writerow(headers)



    for user in users:



        last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt', [('user', '=', user), (
        'timestamp', '>', year_before_today)], hotel_code="user-seeker:")

        for entrada in last_logging_attempt:
            timestamp = entrada['timestamp']
            ip = entrada['ip']
            user = entrada['user']
            status = entrada['status']

            csv_writer.writerow([entrada.get(x) for x in headers])


def get_global_report(days_ago):
    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=days_ago)

    year_before_today = str(year_before_today)


    last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt',
                                                                                  [('timestamp', '>',
                                                                                     year_before_today)],
                                                                                  hotel_code="user-seeker:")


    agrupado = defaultdict(set)
    for entrada in last_logging_attempt:
        ip = entrada['ip']
        user = entrada['user']
        agrupado[ip].add(user)


    return {ip: list(users) for ip, users in agrupado.items()}


    return last_logging_attempt

def get_report_by_users(users, days_ago):

    today = datetime.datetime.now()
    year_before_today = today - datetime.timedelta(days=days_ago)

    year_before_today = str(year_before_today)

    all_access = []
    for user in users:

        last_logging_attempt = datastore_communicator.get_using_entity_and_params('LoginAttempt', [('user', '=', user), (
        'timestamp', '>', year_before_today)], hotel_code="user-seeker:")

        all_access += last_logging_attempt


    agrupado = defaultdict(set)
    for entrada in all_access:
        ip = entrada['ip']
        user = entrada['user']
        agrupado[ip].add(user)


    return {ip: list(users) for ip, users in agrupado.items()}


def geolocalizar_ip(ip):
    import requests
    try:
        response = requests.get(f"https://ipinfo.io/{ip}/json")
        if response.status_code == 200:
            data = response.json()
            return {
                'ip': ip,
                'ciudad': data.get('city'),
                'region': data.get('region'),
                'pais': data.get('country'),
                'ubicacion': data.get('loc'),  # lat,long
            }
        else:
            return {'ip': ip, 'error': 'No se pudo obtener info'}
    except Exception as e:
        return {'ip': ip, 'error': str(e)}

if __name__ == '__main__':

    days_ago = 20
    users_detected=['apartamentosronda4', 'carlos_bannister', 'puentereal', 'rosaflash', 'SensationAptm', 'pamplonaeltoro']
    secure_countries_name = ["Spain"]

    get_posible_login_ok(users_detected, days_ago, secure_countries_name)

    secure_countries = ["ES", "MX", "CO", "PT", "AD", "US", "GI", "DO", "IT"]

    users = ['puentereal', 'rosaflash', 'Recep_Flash', 'recepcionflash', 'Reservas_flash1',
             'reservasflashrosamar', 'rosaflash', 'jrecep_rosamar', 'recepcionrosamar', 'Reservas_rosamar',
             'Reservas_rosamar1', 'Reservas_rosamar2', 'Reservas_rosamar3']
    #user_access_by_user(users, days_ago)


    min_distinct_users = 2

    #users = ['puentereal']
    #report = get_report_by_users(users, days_ago)

    #report = get_global_report(days_ago)

    '''for ip, usuarios in report.items():
        if len(usuarios) >= min_distinct_users:
            geo = geolocalizar_ip(ip)

            if geo.get("pais", "") not in secure_countries:

                print(f"{ip}:\t{', '.join(usuarios)}")
                print(geo)
                print()'''
