import json
import os
import datetime
from collections import defaultdict

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator

import csv

#TIPOS DE PAGOS DESDE EL PEP!!!
PAYMENT_TYPE_GIFT_CARD = "gift card"
PAYMENT_TYPE_LINK = "Envío de link al cliente"
PAYMENT_TYPE_PROGRAMATICALLY = "programado"
PAYMENT_TYPE_IN_WEB = "in_web"
PAYMENT_TYPE_EARLY_PAYMENT = "early_payment"
PAYMENT_TYPE_CREATE_TOKEN = "create_token"
PAYMENT_TYPE_FLIGHT = "flight_hotel"
PAYMENT_TYPE_REFUND = "devolution"
PAYMENT_TYPE_REFUND_EXTRA = "extra devolution"
PAYMENT_TYPE_REFUND_AUTOMATIC = "automatic devolution"
PAYMENT_TYPE_REFUND_PENDING = "refund pending"
PAYMENT_TYPE_REFUND_FAILED = "failed_devolution"
PAYMENT_TYPE_MANUAL_EXTRA = "manual_extra" #Pago externo, que se notifica manualmente en el PeP
PAYMENT_TYPE_MANUAL = "manual" #pago real realizado  desde el PeP de forma manual
PAYMENT_TYPE_EXTRA = "extra" #pago real realizado  desde el PeP de forma manual, pero extra a la reserva (para saltarse el limite del total de la reserva)

REAL_PAYMENTS = [PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_REFUND, PAYMENT_TYPE_REFUND_AUTOMATIC, PAYMENT_TYPE_REFUND_EXTRA,
                 PAYMENT_TYPE_PROGRAMATICALLY, PAYMENT_TYPE_MANUAL_EXTRA, PAYMENT_TYPE_MANUAL, PAYMENT_TYPE_EXTRA]
def is_real_payment(payment):

    order = payment.get("order", "")
    if isinstance("order", str):
        order = order.upper()

    if payment and (not payment.get("error")) and ( "ERROR" not in order) and payment.get("type") in REAL_PAYMENTS:
        return True

    return False

def is_numeric(value):
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False


#'invalidCreditCard' '2025-04-23 08:11:16'

if __name__ == '__main__':




    # Reservas NO pagadas 100% con entrada menor a 14 día
    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas",
              "prinsotel-malpas"]
    for hotel_code in hotels:
        print("%s" % hotel_code)
        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[
            ("timestamp", ">=", "2024-01-01")],
                                                                             hotel_code=hotel_code)
        for reservation in result_entities:
            cancelled = reservation.get("cancelled")
            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))

            extra_info = json.loads(reservation.get('extraInfo', ''))
            status_reservation = extra_info.get('status_reservation', "")

            excluded = extra_info.get('exclude_programmatically_payment_cobrador_ids')

            if (not cancelled) and not excluded and status_reservation.lower() not in ["pending"]:

                payed_by_link = 0
                payed = float(extra_info.get("payed", 0))
                payed_by_cobrador = 0
                if extra_info.get("payed_by_cobrador"):
                    payed_by_cobrador = float(extra_info.get("payed_by_cobrador"))
                if extra_info.get("payed_by_tpv_link"):

                    for payment_by_link in extra_info["payed_by_tpv_link"]:
                        if payment_by_link.get("amount"):
                            payed_by_link += float(payment_by_link.get("amount"))

                total_paid = payed + payed_by_cobrador + payed_by_link
                fully_paid = abs(total_paid - price) < 1
                if reservation.get("startDate") > "2025-01-01" and reservation.get("startDate") < "2025-07-17" and not fully_paid:
                    print("%s %s %s start date %s end date  %s Price %s paid %s" % (
                        hotel_code, reservation.get("identifier"),
                        reservation.get("identifier"), reservation.get("startDate"),
                        reservation.get("endDate"), price, total_paid))

    exit()

    #reservas con Boton malo SIN PAGAR:
    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas",
              "prinsotel-malpas"]
    for hotel_code in hotels:
        print("%s" % hotel_code)
        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[
            ("timestamp", ">=", "2024-01-01")],
                                                                             hotel_code=hotel_code)
        for reservation in result_entities:
            cancelled = reservation.get("cancelled")
            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))

            extra_info = json.loads(reservation.get('extraInfo', ''))
            status_reservation = extra_info.get('status_reservation')
            pending = status_reservation == 'pending'

            cancelled_by = extra_info.get('cancelled_by')

            if (not cancelled):

                payed_by_link = 0
                payed = float(extra_info.get("payed", 0))
                payed_by_cobrador = 0
                if extra_info.get("payed_by_cobrador"):
                    payed_by_cobrador + float(extra_info.get("payed_by_cobrador"))
                if extra_info.get("payed_by_tpv_link"):

                    for payment_by_link in extra_info["payed_by_tpv_link"]:
                        if payment_by_link.get("amount"):
                            payed_by_link += float(payment_by_link.get("amount"))

                if reservation.get("endDate") > "2025-05-08" and reservation.get("invalidCreditCard") and (not payed_by_link) and not payed_by_cobrador:
                    print("%s start date %s end date  %s" % (
                    reservation.get("identifier"),  reservation.get("startDate"),
                    reservation.get("endDate")))



    exit()
    #RESERVAS PENDIENTES SIN CANCELAR que tendrían que hberse cancelado

    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas",
              "prinsotel-malpas"]
    for hotel_code in hotels:
        print("%s" % hotel_code)
        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[
            ("timestamp", ">=", "2024-01-01")],
                                                                             hotel_code=hotel_code)
        for reservation in result_entities:
            cancelled = reservation.get("cancelled")
            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))

            extra_info = json.loads(reservation.get('extraInfo', ''))
            status_reservation = extra_info.get('status_reservation')
            pending = status_reservation == 'pending'

            cancelled_by = extra_info.get('cancelled_by')

            if (not cancelled) and pending:

                payed_by_link = 0
                payed = float(extra_info.get("payed", 0))
                payed_by_cobrador = 0
                if extra_info.get("payed_by_cobrador"):
                    payed_by_cobrador + float(extra_info.get("payed_by_cobrador"))
                if extra_info.get("payed_by_tpv_link"):

                    for payment_by_link in extra_info["payed_by_tpv_link"]:
                        payed_by_link += float(payment_by_link.get("amount"))

                if not payed_by_link:
                    print("%s %s %s %s" % (reservation.get("identifier"), reservation.get("timestamp"), reservation.get("startDate"), reservation.get("endDate")))
                    print("Proceso: %s PeP: %s Links: %s Incidents: %s Botón malo: %s" % (payed, payed_by_cobrador, payed_by_link,  reservation.get("incidents"), reservation.get("invalidCreditCard")))
                    print("link enviado: %s", extra_info.get('payment_link_send_date'))





    exit()
    # RESERVAS CANCELADAS O PENDIENTES CON PAGOS REALES

    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas",
              "prinsotel-malpas"]
    for hotel_code in hotels:
        print("%s" % hotel_code)
        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[("timestamp", ">=", "2025-01-01")],
                                                                             hotel_code=hotel_code)
        for reservation in result_entities:
            cancelled = reservation.get("cancelled")
            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))

            extra_info = json.loads(reservation.get('extraInfo', ''))
            status_reservation = extra_info.get('status_reservation')
            pending =  status_reservation == 'pending'

            cancelled_by = extra_info.get('cancelled_by')


            if (cancelled or pending) and not cancelled_by:
                identifier = reservation.get("identifier")
                payments_list_by_cobrador = list(
                    datastore_communicator.get_using_entity_and_params('PaymentsReservation', [
                        ("reservation_identifier", "=", identifier),

                    ],
                                                                       hotel_code=hotel_code))
                captures_ok = []
                possible_ko = []
                manuals = []
                for payment in payments_list_by_cobrador:
                    pay_id = payment.get("order")
                    if "manual" in payment.get("type") or is_real_payment(payment):

                        if price > 10 and reservation.get("endDate", "") > "2025-04-01":

                            print("%s %s %s %s %s %s %s" % (
                            hotel_code, identifier, payment.get("order"),
                            payment.get("timestamp"), price,
                            reservation.get("incidents"), payment.get("amount")))




    exit()
    # PAGOS de reservas CONFIRMADOS VS AUDITS
    total_amount = 0



    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas",
              "prinsotel-malpas"]
    for hotel_code in hotels:
        print("%s" % hotel_code)
        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[], hotel_code=hotel_code)
        for reservation in result_entities:
            cancelled = reservation.get("cancelled")
            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))

            if not cancelled:
                identifier = reservation.get("identifier")
                payments_list_by_cobrador = list(
                    datastore_communicator.get_using_entity_and_params('PaymentsReservation', [
                        ("reservation_identifier", "=", identifier),

                    ],
                                                                       hotel_code=hotel_code))
                captures_ok = []
                possible_ko = []
                manuals = []
                for payment in payments_list_by_cobrador:
                    pay_id = payment.get("order")
                    if "manual" in payment.get("type"):
                        manuals.append(pay_id)
                    elif is_real_payment(payment):

                        if not is_numeric(payment.get("order")):



                            total_amount += float(payment.get("amount"))
                            print("%s %s %s %s %s %s %s" % (
                            hotel_code, identifier, payment.get("payment_order_id"), payment.get("timestamp"), price,
                            reservation.get("incidents"), payment.get("amount")))
                        else:
                            last_logging_attempt = datastore_communicator.get_using_entity_and_params('AuditResponse',
                                                                                                      [(
                                                                                                          'hotel_code',
                                                                                                          '=',
                                                                                                          hotel_code),
                                                                                                        ('payment_order_id', '=',
                                                                                                         pay_id)],
                                                                                                      hotel_code="payment-seeker:")

                            if not last_logging_attempt:
                                last_logging_attempt = datastore_communicator.get_using_entity_and_params(
                                    'AuditResponse',
                                    [('payment_order_id', '=',
                                         pay_id)],
                                    hotel_code=hotel_code)

                            for entrada in last_logging_attempt:
                                response = entrada.get("response", "ERROR")
                                if "error" in response.lower() or "unsuc" in response.lower() or "rejected" in response.lower():
                                    possible_ko.append({pay_id: entrada})
                                else:
                                    if entrada.get("type") == "Capture":

                                        if "CAPTURE_REQUESTED" in response or "PENDING_CAPTURE" in response:
                                            captures_ok.append(pay_id)

                for pos_koS in possible_ko:
                    for pos_ko, payment in pos_koS.items():

                        if pos_ko not in captures_ok:
                            if pos_ko not in manuals:
                                total_amount += float(payment.get("amount"))
                                print("%s %s %s %s %s %s %s" % (
                                    hotel_code, identifier, payment.get("order"),
                                    payment.get("timestamp"), price,
                                    reservation.get("incidents"), payment.get("amount")))

    print(total_amount)





    exit()

    #  AUDITS CON ERRORR VS reservas CONFIRMADA: ARROJA FALSOS POSITIVOS

    total_amount = 0

    posibel_no = 0

    payment_repeated = []

    hotels = ["prinsotel-dorada", "prinsotel-caleta", "prinsotel-alba", "prinsotel-pineda", "prinsotel-villas", "prinsotel-malpas"]
    for hotel_code in hotels:
        last_logging_attempt = datastore_communicator.get_using_entity_and_params('AuditResponse',
                                                                                  [(
                                                                                      'hotel_code', '=',
                                                                                      hotel_code)],
                                                                                  hotel_code="payment-seeker:")

        for entrada in last_logging_attempt:
            if entrada.get("timestamp") > "2025-01-01":
                response = entrada.get("response", "ERROR")
                if "error" in response.lower() or "unsuc" in response.lower() or "rejected" in response.lower():

                    print(response)
                    print("")
                    print("")
                    continue


                    reservation_id = int(entrada.get("payment_order_id"))

                    result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[
                        ('identifier', '=', str(reservation_id))], hotel_code=hotel_code)

                    if not result_entities:
                        reservation_id = reservation_id - 1

                        result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[
                            ('identifier', '=', str(reservation_id))], hotel_code=hotel_code)

                        if not result_entities:
                            reservation_id = reservation_id - 1

                            result_entities = datastore_communicator.get_using_entity_and_params('Reservation',
                                                                                                 search_params=[
                                                                                                     ('identifier', '=',
                                                                                                      str(reservation_id))],
                                                                                                 hotel_code=hotel_code)

                            if not result_entities:
                                reservation_id = reservation_id - 1

                                result_entities = datastore_communicator.get_using_entity_and_params('Reservation',
                                                                                                     search_params=[
                                                                                                         ('identifier',
                                                                                                          '=',
                                                                                                          str(reservation_id))],
                                                                                                     hotel_code=hotel_code)

                    cancelled = "CONFIRMED"
                    if result_entities:
                        reservation =  result_entities[0]
                        cancelled = reservation.get("cancelled")


                    if not cancelled:
                        if entrada.get("payment_order_id") not in payment_repeated:
                            payment_repeated.append(entrada.get("payment_order_id"))
                            price = float(reservation.get("price")) + float(reservation.get("priceSupplements"))
                            total_amount += price
                            print("%s %s %s %s %s %s %s" % (hotel_code, reservation_id, cancelled, entrada.get("payment_order_id"), entrada.get("timestamp"), price, reservation.get("incidents")))

                            second = datastore_communicator.get_using_entity_and_params('AuditResponse',
                                                                                                      [(
                                                                                                           'hotel_code',
                                                                                                           '=',
                                                                                                           hotel_code), ("payment_order_id", "=", entrada.get("payment_order_id"))],
                                                                                                      hotel_code="payment-seeker:")
                            if second:
                                for sec in second:
                                    if "create" in sec.get("response").lower():
                                        #print("CORRECTO")
                                        pass


                        else:

                            #print("posible false posotive %s" % (entrada.get("payment_order_id")))
                            pass

    print(total_amount)

    repetidos = set()
    for elem in payment_repeated:
        repetidos.add(elem)


    print(list(repetidos))
    print(len(list(repetidos)))

