from google.cloud import monitoring_v3
from tabulate import tabulate
from google.protobuf.field_mask_pb2 import FieldMask



from paraty.build.enable_task_service import _get_all_hotel_projects
from paraty.tools.alerts.create_any_error_alert import create_project_alerts

from google.cloud import monitoring_v3
def deactivate_alert_policy(project_id, policy_name):
	client = monitoring_v3.AlertPolicyServiceClient()
	project_name = f"projects/{project_id}"
	
	# Get the existing policy
	alert_policies = client.list_alert_policies(name=project_name)
	for policy in alert_policies:
		if policy.name == policy_name:
			# Deactivate the policy
			policy.enabled = False
			# Update the policy
			update_mask = FieldMask(paths=["enabled"])
			client.update_alert_policy(alert_policy=policy, update_mask=update_mask)
			print(f"Alert policy {policy_name} has been deactivated.")
			return
	
	print(f"Alert policy {policy_name} not found.")


def list_alert_policies(project_id):
	client = monitoring_v3.AlertPolicyServiceClient()
	project_name = f"projects/{project_id}"
	
	alert_policies = client.list_alert_policies(name=project_name)
	
	name_error_notification = ""
	name_error_alert = ""
	error_alert = False
	error_notification = False
	
	for policy in alert_policies:
		if policy.display_name == "Error notification":
			name_error_notification = policy.name
		
		if policy.display_name == "Error Count Alert":
			name_error_alert = policy.name
	
	if name_error_alert:
		policy_name = name_error_alert
		deactivate_alert_policy(project_id, policy_name)
		error_alert = True
	if not name_error_notification:
		create_project_alerts(project_id)
		error_notification = True
	
	return {"project": project_id, "alert": error_alert, "error": error_notification}


def find_hotel_with_alert():
	all_projects = _get_all_hotel_projects()
	list_alert = []
	for project in all_projects:
		try:
		

			list_alert.append(list_alert_policies(project))
		except Exception as e:
			print(f"Hotel sin permiso {project}")
			continue
	
	table_data = []
	for alert in list_alert:
		table_data.append([alert.get('project'), alert.get('alert'), alert.get('error')])
	
	# Print table
	print(tabulate(table_data, headers=["Name Project", "Alert Name", "Error notification"], tablefmt="grid"))


if __name__ == "__main__":
	find_hotel_with_alert()
