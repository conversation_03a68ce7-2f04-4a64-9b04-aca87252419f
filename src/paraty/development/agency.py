import json

from paraty_commons_3 import queue_utils
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.logging.my_gae_logging import logging
from datetime import datetime, timedelta


@timed_cache(hours=72)
def get_configuration(hotel_code, user=None):
    name_config = "AgencyConfiguration"
    if user and user.get("is_company"):
        name_config = "CompanyConfiguration"

    all_configuration_properties = list(
        datastore_communicator.get_using_entity_and_params('WebConfiguration', [('name', '=', name_config)],
                                                           hotel_code=hotel_code))

    if all_configuration_properties:
        current_property = all_configuration_properties[0]
        agency_config = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in current_property.get("configurations")}
        return agency_config

    return {}


@timed_cache(hours=72)
def hotel_has_agency(hotel_code):
    try:
        logging.info("Checking hotel {} ".format(hotel_code))
        agency_config = get_configuration(hotel_code)
        return False if agency_config == {} or agency_config.get("remote login agencies", "") != "" else True
    except Exception as e:
        return False


def _get_remote_only_reservations(remote_hotel, reservations_from):
    reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation', [('timestamp', '>', reservations_from)],
                                                           hotel_code=remote_hotel))

    # if  reservations_from == TS_RESERVATIONS_AGENCY:
    # check also cancellations and modification
    cancel_reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation',
                                                           [('cancellationTimestamp', '>', reservations_from)],
                                                           hotel_code=remote_hotel))
    if cancel_reservations:
        reservations += cancel_reservations

    modify_reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation',
                                                           [('modificationTimestamp', '>', reservations_from)],
                                                           hotel_code=remote_hotel))

    if modify_reservations:
        reservations += modify_reservations

    reservation_id = []
    r = []
    for i in reservations:
        if not i["identifier"] in reservation_id:
            reservation_id.append(i["identifier"])
            i['extraInfo'] = json.loads(i['extraInfo'])
            i['hotel_code'] = remote_hotel
            r.append(i)
    return r


def add_reservation_info():
    hotels = get_all_valid_hotels()

    reservations_from = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    for hotel in hotels:

        CLOUD_FUNCTION = "https://europe-west1-build-tools-2.cloudfunctions.net"
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        hotel_code = hotel['applicationId']

        if not hotel_has_agency(hotel_code):
            logging.info(f"Hotel without agency {hotel_code}")
            continue
        try:
            agency_config = get_configuration(hotel_code)
        except:
            agency_config = {}

        if agency_config == {} or agency_config.get("remote login agencies", "") != "":
            continue

        remote_hotels = agency_config.get("remote hotels", "").replace(";",",").split(",")

        for remote_hotel in remote_hotels:
            try:
                if not remote_hotel:
                    remote_hotel = hotel_code
                logging.info(f"Checking agencies hotel {remote_hotel}")
                # add_reservation_info_hotel(remote_hotel, hotel_code, reservations_from)
                queue_utils.create_task_with_url_get_target("reservation-agency",
                                                            f'{CLOUD_FUNCTION}/paraty_add_reservation_agency_task?remote_hotel={remote_hotel}&hotel_code={hotel_code}&reservations_from={reservations_from}',
                                                            f"paraty_add_reservation_agency_task_{remote_hotel}_{timestamp}")
            except Exception as e:
                logging.info(f"Error in add_reservation_info queueing hotel: {remote_hotel}")

    logging.info("OK paraty_add_reservation_agency")
    return "OK"


def add_reservation_info_hotel(remote_hotel, hotel_code, reservations_from):
    logging.info("Executing task add_reservation_agency for hotel %s with date from %s" %(remote_hotel, reservations_from))
    try:
        agencies = get_using_entity_and_params('Agencies', hotel_code=hotel_code)
        if not agencies:
            logging.info(f"Hotel {hotel_code} without agencies")
            return f"OK add_reservation_info_hotel{remote_hotel}"

        total_reservations = _get_remote_only_reservations(remote_hotel, reservations_from)

        for agency in agencies:
            agency_id = int(agency.key.id)
            agency_hash = agency.get("agency_hash")

            agency_reservations = [x for x in total_reservations if x.get("extraInfo").get("agency_id") == str(agency_id) or x.get("extraInfo").get("agency_hash") == str(agency_hash)]

            for reservation in agency_reservations:
                try:
                    query = [('IdAgency', '=', agency_id), ('IdBooking', '=', reservation.id)]
                    my_entity = list(get_using_entity_and_params('AgenciesBooking', query,
                                                                 hotel_code=hotel_code))

                    if not my_entity:
                        my_entity = {'IdAgency': agency_id, 'IdBooking': reservation.key.id}

                    else:
                        my_entity = list(my_entity)[0]

                    if not reservation.get("cancelled"):
                        my_entity['Cancelled'] = False
                    else:
                        my_entity['Cancelled'] = True

                    my_entity['Identifier'] = reservation.get("identifier")
                    my_entity['HotelCode'] = reservation.get("hotel_code")
                    my_entity['TotalNeto'] = float(reservation.get("price", "0")) + float(
                        reservation.get("priceSupplements", "0"))

                    agency_pvp_price = float(reservation.get("price", "0")) + float(
                        reservation.get("priceSupplements", "0"))
                    commission = 0
                    extra_info = reservation.get("extraInfo", "{}")
                    if extra_info:
                        agency_pvp_price = extra_info.get("agency_pvp_price", "")
                        commission = extra_info.get("agency_commision", "")

                    if not my_entity.get('timestamp'):
                        my_entity['timestamp'] = datetime.now()
                    my_entity['Comission'] = commission
                    my_entity['TotalPVP'] = agency_pvp_price
                    if hasattr(my_entity, "id"):
                        datastore_communicator.save_to_datastore("AgenciesBooking", int(my_entity.id), my_entity,
                                                                 hotel_code=hotel_code)
                    else:
                        datastore_communicator.save_to_datastore('AgenciesBooking', None, my_entity,
                                                                 hotel_code=hotel_code)
                    logging.info("Agency reservation %s added in %s" % (reservation.get("identifier"), hotel_code))
                except Exception as e:
                    continue
        logging.info(f"Ok Add reservation agency {hotel_code}")
        return "OK"
    except Exception as e:
        logging.info(f"KO Add reservation agency{hotel_code}")
        return "KO"
