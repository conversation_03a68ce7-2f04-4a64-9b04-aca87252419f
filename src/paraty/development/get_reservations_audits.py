import datetime
import json
import logging

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel, get_integration_configuration_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels





__author__ = 'nmarin'

class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)





def check_all_reservations_from_adapter(adapter_name):

	timestamp = '2023-02-05 07:00:00'
	to_datetime = '2023-02-14 08:35:00'

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")



	for hotel in all_hotels:
		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):



			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False)


			for reservation in reservations:
				identifier_to_check = "0" + reservation.get("identifier")
				result_entities = datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent', search_params=[('request_id', '=', identifier_to_check)], hotel_code="~siteminder-adapter")

				if len(result_entities) >1:
					print("DUPLICATED: " + hotel['applicationId'] + " "+ identifier_to_check)





if __name__ == "__main__":


	check_all_reservations_from_adapter("siteminder")
	logging.info("FIN")