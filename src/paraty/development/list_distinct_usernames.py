import json
from paraty_commons_3.datastore import datastore_communicator
from collections import Counter

def get_all_distinct_usernames():
    """
    Extrae todos los userName distintos de la base de datos AdminUserSession
    """
    print("Obteniendo todos los registros de AdminUserSession...")
    
    # Obtener todos los registros de AdminUserSession
    all_admin_sessions = datastore_communicator.get_using_entity_and_params(
        'AdminUserSession', 
        hotel_code="admin-hotel"
    )
    
    print(f"Total de registros encontrados: {len(all_admin_sessions)}")
    
    # Extraer todos los userNames
    usernames = []
    username_counts = Counter()
    
    for session in all_admin_sessions:
        username = session.get('userName')
        if username:
            usernames.append(username)
            username_counts[username] += 1
    
    # Obtener userNames únicos
    distinct_usernames = list(set(usernames))
    distinct_usernames.sort()  # Ordenar alfabéticamente
    
    print(f"\nTotal de userNames únicos encontrados: {len(distinct_usernames)}")
    print("\n=== LISTA DE USERNAMES DISTINTOS ===")
    
    for i, username in enumerate(distinct_usernames, 1):
        count = username_counts[username]
        print(f"{i:3d}. {username} (aparece {count} veces)")
    
    # Guardar en archivo JSON
    output_data = {
        "total_records": len(all_admin_sessions),
        "total_distinct_usernames": len(distinct_usernames),
        "usernames_with_counts": dict(username_counts),
        "distinct_usernames_list": distinct_usernames
    }
    
    with open("distinct_usernames_report.json", "w", encoding="utf-8") as archivo:
        json.dump(output_data, archivo, ensure_ascii=False, indent=4)
    
    print(f"\nReporte guardado en: distinct_usernames_report.json")
    
    # Guardar lista simple en archivo de texto
    with open("distinct_usernames_list.txt", "w", encoding="utf-8") as archivo:
        for username in distinct_usernames:
            archivo.write(f"{username}\n")
    
    print(f"Lista simple guardada en: distinct_usernames_list.txt")
    
    return distinct_usernames, username_counts

def get_usernames_with_filters(min_sessions=None, max_sessions=None):
    """
    Obtiene userNames con filtros opcionales por número de sesiones
    """
    distinct_usernames, username_counts = get_all_distinct_usernames()
    
    if min_sessions is not None or max_sessions is not None:
        print(f"\n=== FILTROS APLICADOS ===")
        if min_sessions is not None:
            print(f"Mínimo de sesiones: {min_sessions}")
        if max_sessions is not None:
            print(f"Máximo de sesiones: {max_sessions}")
        
        filtered_usernames = []
        for username, count in username_counts.items():
            if min_sessions is not None and count < min_sessions:
                continue
            if max_sessions is not None and count > max_sessions:
                continue
            filtered_usernames.append(username)
        
        filtered_usernames.sort()
        print(f"\nUserNames que cumplen los filtros ({len(filtered_usernames)}):")
        for username in filtered_usernames:
            count = username_counts[username]
            print(f"  - {username} ({count} sesiones)")
        
        return filtered_usernames
    
    return distinct_usernames

if __name__ == '__main__':
    print("=== EXTRACTOR DE USERNAMES DISTINTOS ===\n")
    
    try:
        # Obtener todos los userNames distintos
        distinct_usernames, username_counts = get_all_distinct_usernames()
        
        # Mostrar algunos estadísticos adicionales
        print(f"\n=== ESTADÍSTICAS ===")
        print(f"UserName con más sesiones: {max(username_counts, key=username_counts.get)} ({max(username_counts.values())} sesiones)")
        print(f"UserName con menos sesiones: {min(username_counts, key=username_counts.get)} ({min(username_counts.values())} sesiones)")
        print(f"Promedio de sesiones por usuario: {sum(username_counts.values()) / len(username_counts):.2f}")
        
        # Ejemplos de filtros (comentados por defecto)
        # print("\n=== EJEMPLOS DE FILTROS ===")
        # get_usernames_with_filters(min_sessions=5)  # Usuarios con al menos 5 sesiones
        # get_usernames_with_filters(max_sessions=1)  # Usuarios con solo 1 sesión
        
    except Exception as e:
        print(f"Error al obtener los userNames: {e}")
        import traceback
        traceback.print_exc()
