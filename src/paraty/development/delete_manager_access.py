import base64
import json
import os
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from datetime import datetime
import csv

def get_manager_access(filter_function, params, user_name):
    #search_params=[('currentAppId', '=', 4918418288738304)]
    search_params = [('userName', '=', user_name)]
    #, search_params = search_params
    all_hotel_acess =  datastore_communicator.get_using_entity_and_params('AdminUserSession', search_params = search_params, hotel_code="admin-hotel")
    list_of_keys_to_delete = []
    deleted_acess = []
    for access_manager in all_hotel_acess:
        if filter_function(access_manager, params):
            access = {}
            user = access_manager['userName']
            timestamp = str(access_manager["timestamp"])[:-3]
            access[user] = datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')
            print(access)
            list_of_keys_to_delete.append(access_manager.key)
            deleted_acess.append(dict(access_manager))


    # Convertir bytes a base64
    for dic in deleted_acess:
        for clave, valor in dic.items():
            if isinstance(valor, bytes):
                dic[clave] = base64.b64encode(valor).decode("utf-8")

    with open("masivedeletes/User_agents_access%s.txt" % user_name, "w", encoding="utf-8") as archivo:
        json.dump(deleted_acess, archivo, ensure_ascii=False, indent=4)

    datastore_communicator.delete_entity_multi(list_of_keys_to_delete, hotel_code="admin_hotel")

    print("ok")




def access_ok_by_dates(access_manager, params):
    user_name = params[0]

    date_from = "2025-07-01 00:00:00"
    timestamp = str(access_manager["timestamp"])[:-3]
    timestamp_human = datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')

    user_ok = access_manager.get('userName') == user_name

    date_ok =timestamp_human and (timestamp_human >= date_from)


    return user_ok



if __name__ == '__main__':
    #carlos_bannister, valdas, rosaflash, maspalomas, puentereal, pamplonaeltoro, SensationAptm, munich-city-elaya, gransolapartamentos, amerello-chateau
    user_name = "josemanuel"

    '''all_hotel_acess = datastore_communicator.get_using_entity_and_params('AdminUserSession',
                                                                         hotel_code="admin-hotel")

    # Extraer todos los userName distintos en una lista
    distinct_usernames = []
    seen_usernames = set()

    for session in all_hotel_acess:
        username = session.get('userName')
        if username and username not in seen_usernames:
            distinct_usernames.append(username)
            seen_usernames.add(username)

    # Ordenar la lista alfabéticamente
    #distinct_usernames.sort()

    # Ahora tienes todos los userName distintos en la lista 'distinct_usernames'
    # Puedes usar esta lista como necesites'''

    #distinct_usernames = ['josemanuel', 'sa-hotels', 'infanteantequera']
    distinct_usernames = ['smanzanares']



    for user_name in distinct_usernames:
        params= [user_name]
        function_filter = access_ok_by_dates

        get_manager_access(function_filter, params, user_name)





