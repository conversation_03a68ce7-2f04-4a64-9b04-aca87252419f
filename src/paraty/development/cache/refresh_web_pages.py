from concurrent.futures import ThreadPoolExecutor

import requests

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

all_hotels = get_all_hotels()

def perform_request(hotel_code):
    print(f'Performing request for hotel {hotel_code}')
    requests.get(f'https://europe-west1-hotel-tools.cloudfunctions.net/notify-web-change?hotel_code={hotel_code}', timeout=3)

with ThreadPoolExecutor(max_workers=10) as executor:
    for hotel_code in all_hotels:
        executor.submit(perform_request, hotel_code)
