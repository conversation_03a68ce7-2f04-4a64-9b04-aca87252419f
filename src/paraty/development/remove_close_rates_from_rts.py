from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def clean_deleted_rates(date, rate_key_to_delete, hotel_codes):

    all_hotels = get_all_valid_hotels()

    for hotel in all_hotels:
        if hotel.get("applicationId") in hotel_codes:


            all_rts = datastore_communicator.get_using_entity_and_params("RoomTypeStatus",
                                                                         search_params=[("date", ">=", date)],
                                                                         hotel_code=hotel["applicationId"])

            for rts in all_rts:
                closedRateBoard = rts.get("closedRateBoard", "")
                if not closedRateBoard:
                    continue
                initial_len = len(closedRateBoard)
                rates = {}
                closed = closedRateBoard.split(";")
                for close in closed:
                    rateBoard = close.split("_@_")
                    if rateBoard[0] not in rates and rateBoard[0] != "":
                        rates[rateBoard[0]] = 0
                    if rateBoard[0] in rates:
                        rates[rateBoard[0]] += 1


                for close in closed:
                    rateBoard = close.split("_@_")
                    if rateBoard[0] in rate_key_to_delete:
                        closedRateBoard = closedRateBoard.replace(close, "")

                closedRateBoard = closedRateBoard.replace(";;", ";")

                print("Original: %s", rts["closedRateBoard"])
                rts["closedRateBoard"] = closedRateBoard
                print("final: %s", rts["closedRateBoard"])
                print(f"from{initial_len} to {len(closedRateBoard)}")

            try:
                datastore_communicator.save_entity_multi(all_rts, hotel["applicationId"])
                pass
            except Exception as e:
                print(e)

if __name__ == "__main__":

    date = "2025-05-01"
    rate_key_to_delete = ["agxzfm5hdS1ob3RlbHNyEQsSBFJhdGUYgICQ4NWtngsMogEKbmF1LXNhbGVtYQ"]
    hotel_codes = ["nau-salema"]
    clean_deleted_rates(date, rate_key_to_delete, hotel_codes)
