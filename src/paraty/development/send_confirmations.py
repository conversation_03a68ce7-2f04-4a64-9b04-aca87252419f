from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
import requests

reservations_by_hotel = {
  "best-ballena": [
    "44016274", "96139331", "36505651", "13139333", "24554237", "19322364", "32695657", "16793601", "77147237",
    "22675317", "37204630", "89519451", "33071309", "81101276", "41107313", "48349161", "95028576", "46882394",
    "81688849", "19785790", "83596065", "38968015", "31239442", "26472911", "46505434", "71364510", "11964263",
    "70696313", "99342753", "54193132", "97449459", "22708980", "32475729", "64862683", "70207201", "18567785",
    "46805078", "10567330", "99978647", "76563064", "77207616", "57740580", "58862627", "23256494"
  ],
  "best-benalmadena": [
    "46776153", "81716970", "85916730", "8DAFFC31C", "39213460", "92718761", "29911914", "27770214", "75505669", "19314736"
  ],
  "best-cambrils": [
    "68436668", "39243469", "68268575", "48588506", "63021442"
  ],
  "best-capsalou": [
    "52212979", "16821200", "55859052", "67275468", "96117340"
  ],
  "best-davinciroyal": [
    "69953033", "56032169", "19361412", "29461496", "66991706", "24912973", "73312246", "80472148", "33424949", "95022642"
  ],
  "best-delta": [
    "29957861", "61013377", "16725526", "30101426"
  ],
  "best-frontmaritim": [
    "R78234004", "88026824"
  ],
  "best-indalo": [
    "46518982", "73741330", "17853808", "87262908", "79252523", "63628430", "18676034", "38628221", "96251682", "22700904",
    "68140254", "54015830", "47384847", "72247846", "99967339", "82159915", "97350038", "43234794", "92422849", "10987181",
    "28271304"
  ],
  "best-jacaranda": [
    "83933674", "15574224", "95396455", "67649517", "59673757", "36166646", "21042922"
  ],
  "best-lloret-splash": [
    "93182717", "20761669"
  ],
  "best-losangeles": [
    "36353831", "37435846", "51578967", "42386886", "61813609", "79436654"
  ],
  "best-maritim": [
    "48557815", "72512317", "94221609", "33635836", "43171869", "20659208", "76017642", "17605605", "32530385", "88671714",
    "19113035", "12690428", "97550036", "21808289", "66467741", "88124455", "63950471", "90384826", "50241713", "53333130",
    "92215376", "47525856", "20102349", "31945573", "12058511", "52851086", "11341600", "39131004", "56655694"
  ],
  "best-mojacar": [
    "65792158", "21020356", "15139825", "83757166", "15736024", "49572317", "69618481", "49518208", "32666214", "70053201",
    "66801231", "43513341", "13477066", "29473658", "84566994", "50537881", "25992925", "97326781"
  ],
  "best-negresco": [
    "76207076", "70096921", "56703079", "74371801", "96761048", "54285481", "70941353", "30547517", "70433994", "97602782",
    "93533881", "98727908", "88471750", "80481417", "70922629", "94308106", "75870901", "56075393"
  ],
  "best-oasistropical": [
    "19546116", "82784414-1", "47521302"
  ],
  "best-puebloindalo": [
    "93548197", "71925858", "87005558", "20536113", "52560718", "53557506", "85875812", "26439317", "89025523", "47447062",
    "41416338", "68809468", "80967693", "13821754", "37935424"
  ],
  "best-punta-dorada": [
    "31080210", "55726023"
  ],
  "best-roquetas": [
    "60706318", "46711020", "47287579", "77958775", "16628273", "96794169", "61689390", "40566045", "66309167", "87393550",
    "82092856"
  ],
  "best-sabinal": [
    "87894298", "76956804", "85718627", "55726653", "85743604", "90864106", "57420982", "95967166", "84675017"
  ],
  "best-sandiego": [
    "25158700", "13906128", "41915898", "11561963"
  ],
  "best-sanfrancisco": [
    "73046885", "64198333"
  ],
  "best-semiramis": [
    "89255555", "28996784", "61257752", "58608054", "73221933", "26923529", "36652577", "59070581", "28736499"
  ],
  "best-siroco": [
    "94197114", "68942711", "92806401", "54919064", "65849992", "46643284", "67323255", "44577524", "84518720"
  ],
  "best-soldor": [
    "52028454", "43988826", "31480776", "47818416", "65437055", "55953269", "39046441"
  ],
  "best-tenerife": [
    "CF3BE543E", "51392770", "75480418"
  ],
  "best-terramarina": [
    "74652356", "58333015"
  ],
  "best-triton": [
    "88957948", "74911654", "44364297", "77848033", "33332561", "19890601", "89891491", "30751689", "49195920", "71065449",
    "75895015", "60562350", "13686127"
  ]
}

def send_emails_confirmation(hotel_code, reservation_id, type, modification=False, email=None):
    hotel_info = get_hotel_by_application_id(hotel_code)
    url = get_inner_url(hotel_info)

    email_sender = ""
    if email:
        email_sender = "&email=" + str(email).replace(";", "%3B")

    send_confirmation_post = "%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=true" % (
        url, reservation_id, modification, type, email_sender)

    result = requests.post(send_confirmation_post)
    return result

if __name__ == "__main__":
    for hotel_code, rervations in reservations_by_hotel.items():

        for reservation_id in rervations:
            reservation = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('identifier', '=', reservation_id)], hotel_code=hotel_code)
            if reservation and not reservation[0].get("cancelled"):
                email = reservation[0].get("email")
                send_emails_confirmation(hotel_code, reservation_id, type="customer", email=email)
            else:
                print("ERRORRRR %s %s", hotel_code, reservation_id)