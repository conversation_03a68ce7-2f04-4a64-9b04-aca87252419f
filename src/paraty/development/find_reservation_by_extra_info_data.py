import json
import os

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from datetime import datetime
import csv


def find_confirmed_or_modified_for_hotel_in_period(hotel, min_date, max_date):
    all_reservations = common_data_provider.get_reservations_of_hotel(hotel, min_date, max_date, include_modified_reservations=True)
    return all_reservations


def has_credit_card_to_none_change(extra_info):
    # Check if there's a changes object in the extra_info
    changes = extra_info.get('modifications', {}).get('changes', {})
    
    # Check if there's a creditCard change where the new_value is None
    if 'creditCard' in changes:
        credit_card_change = changes['creditCard']
        if credit_card_change.get('new_value') is None and credit_card_change.get('old_value'):
            return True
    
    return False


if __name__ == '__main__':
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    r_list = []

    min_date = '2024-06-01 00:00:00'
    max_date = '2025-09-22 23:59:59'

    for hotel in all_hotels:
        if 'prinsotel' in hotel['applicationId']:
            hotel_printed = False
            try:
                reservation_list = common_data_provider.get_reservations_of_hotel(hotel, min_date, max_date, include_modified_reservations=True)


                for reservation in reservation_list:
                    extra_info = json.loads(reservation['extraInfo'])

                    if has_credit_card_to_none_change(extra_info):
                        if not hotel_printed:
                            print()
                            print(hotel['applicationId'])
                            hotel_printed = True

                        # Optionally print the specific credit card change
                        changes = extra_info.get('modifications', {}).get('changes', {})
                        if 'creditCard' in changes:
                            credit_card_change =changes['creditCard']

                            if "@@50" in credit_card_change.get('old_value'):

                                if float(extra_info.get("payed", "0")) != 50.0:

                                    print(
                                        f"Reservation ID: {reservation.get('identifier')} {reservation.get('timestamp')} {reservation.get('startDate')}")

                                    print(f"  - Old value: {credit_card_change.get('old_value')}")
                                    print(f"  - New value: {credit_card_change.get('new_value')}")
                                    if 'date' in changes:
                                        print(f"  - Change date: {changes['date']}")
            except:
                pass