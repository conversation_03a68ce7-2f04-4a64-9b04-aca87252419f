import logging
import re
import zlib
import datetime
import html
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id, id_to_entity_key

import json

__author__ = 'nmarin & gruiz'

ENTITY_SEARCH_REGEX = re.compile('"entity"="([^"]*)"')
KEY_SEARCH_REGEX = re.compile('"key"="([^"]*)"')
ROOM_STATUS_KEY_SEARCH_REGEX = re.compile('"key":"([^"]*)"')

# SEPARATOR = "; "
# RATE = 'com.paraty.common.services.shared.model.rates.Rate'
# MULTIRATE = 'com.paraty.common.services.shared.model.MultiRate'
# PROMOTION = 'com.paraty.common.services.shared.model.rates.Promotion'
# CONFIGURATION = 'com.paraty.common.services.shared.model.ConfigurationProperty'
# WEBSECTION = 'com.paraty.common.services.shared.model.WebSection'
# RESTRICTION_DAY_STATUS = 'com.paraty.common.services.shared.model.RestrictionDayStatus'
# PROMOTION_DAY_STATUS = 'com.paraty.common.services.shared.model.PromotionDayStatus'
# REDIRECTION = 'com.paraty.common.services.shared.model.Redirection'
# RESTRICTION = 'com.paraty.common.services.shared.model.Restriction'
# RATE_PERIOD = 'com.paraty.common.services.shared.model.rates.RatePeriod'
# ROOM_TYPE_STATUS = 'RoomTypeStatus'
# FINAL_PRICE_DAY = 'FinalPriceDay'

ENTITY_MODEL_TRANSLATION = {
    'RestrictionDayStatus': 'com.paraty.common.services.shared.model.RestrictionDayStatus',
    'RoomTypeStatus': 'com.paraty.common.services.shared.model.RoomTypeStatus',
}

ENTITY_TRANSLATIONS = {
    'Rate': 'Tarifas',
    'MultiRate': 'Multitarifas',
    'Promotion': 'Ofertas',
    'ConfigurationProperty': 'Configuraciones Avanzadas',
    'WebSection': 'Secciones',
    'RestrictionDayStatus': 'Restricciones en el calendario',
    'PromotionDayStatus': 'Cierres de ofertas desde calendarios',
    'Redirection': 'Redirecciones',
    'Restriction': 'Restricciones',
    'RatePeriod': 'Periodos de tarifa',
    'com.paraty.common.services.shared.model.RoomTypeStatus': 'Cupos y cierres en calendario',
    'com.paraty.common.services.shared.model.FinalPriceDay': 'Precios finales y restrecciones en calendarios',
    'IntegrationConfiguration': 'Integraciones',
    'Picture': 'Imágenes',
    'RoomType': 'Habitaciones',
    'ParatyUser': 'Usuarios',
    'YieldModification': 'Precio de la habitación desde el calendario [Nombre temporal]',
    'Regimen': 'Regímenes',
    'RateCondition': 'Condiciones de la tarifa',
    'Promocode': 'Promocodes',
    'News': 'Noticas',
    'WebConfiguration': 'Configuración Web',
    'Reservation': 'Reservas',
    'Supplement': 'Servicios Adicionales',
    'PriceIncrease': 'Suplementos y Paquetes'
}

ALL_HOTELS = hotel_manager_utils.get_all_hotels()
# logging.info(f"Hoteles: {ALL_HOTELS}")
all_keys = None

not_configured_entities = []


def execute(request, local=False):
    logging.info(f"REQUEST: {request}")
    final_logs_by_hotel = {}
    hotel_code_and_id_dict = {}
    search_params = []
    logs_by_hotel = {}

    if not local:
        data = request.get_json(force=True)
    else:
        data = request

    hotel_code = data.get("hotel_code", "")
    if data.get('multi_hotel'):
        logging.info("Multi hotel")
        for hotel in ALL_HOTELS.keys():
            if hotel_code in hotel:
                hotel_code_and_id_dict[hotel] = str(ALL_HOTELS[hotel]['id'])
    else:
        logging.info(f"Un hotel: {hotel_code}")
        hotel_code_and_id_dict[hotel_code] = str(ALL_HOTELS[hotel_code]['id'])

    num_days = data.get('num_days', '')
    entities_name = data.get('entities_name', '')
    user = data.get('user', '')
    search_by = data.get('search_by', '')

    if entities_name is None:
        entities_name = []

    if num_days:
        timestamp = datetime.datetime.now() - datetime.timedelta(days=num_days)
        formatted_timestamp = datetime.datetime.strftime(timestamp, "%Y-%m-%d")
        search_params.append(("timestamp", ">=", formatted_timestamp))

    if not hotel_code:
        for hotel in ALL_HOTELS.keys():
            hotel_code_and_id_dict[hotel] = ALL_HOTELS[hotel]['id']
            logs_by_hotel[hotel] = []

        all_logs = list(datastore_communicator.get_using_entity_and_params('UsageLogEntry', search_params,
                                                                           hotel_code='admin-hotel',
                                                                           return_cursor=True))

        for log in all_logs:
            for key, value in hotel_code_and_id_dict.items():
                if log.get('applicationId') == str(value):
                    logs_by_hotel[key].append(log)

        for hotel_code, logs in logs_by_hotel.items():
            if logs:
                final_logs_by_hotel[hotel_code] = check_logs(all_logs=logs, hotel_code=hotel_code,
                                                             entities_name=entities_name, user=user)

        if local:
            for hotel_code in final_logs_by_hotel.keys():
                print('*' * 30 + f' {hotel_code} ' + '*' * 30)
                print_logs(final_logs_by_hotel[hotel_code])

    else:
        for hotel_code, hotel_id in hotel_code_and_id_dict.items():
            search_params.append(('applicationId', '=', hotel_id))
            all_logs = list(datastore_communicator.get_using_entity_and_params('UsageLogEntry', search_params,
                                                                               hotel_code='admin-hotel',
                                                                               return_cursor=True))
            final_logs_by_hotel[ALL_HOTELS.get(hotel_code)['name']] = check_logs(all_logs=all_logs,
                                                                                 hotel_code=hotel_code,
                                                                                 entities_name=entities_name, user=user)

            search_params = search_params[:-1]
        # print(json.dumps(final_logs_by_hotel, indent=3))

        if local:
            for hotel_code in final_logs_by_hotel.keys():
                print('*' * 30 + f' {hotel_code} ' + '*' * 30)
                print_logs(final_logs_by_hotel[hotel_code])

    return json.dumps(final_logs_by_hotel)


def check_logs(all_logs, hotel_code, entities_name=None, user=None, search_by=None):
    valid_logs = all_logs
    valid_logs.sort(key=lambda x: x['timestamp'])

    if user:
        valid_logs = apply_filter(valid_logs, user=user)

    if entities_name:
        valid_logs = apply_filter(valid_logs, entities_name=entities_name)

    global all_keys
    all_keys = get_all_entities(hotel_code)

    final_logs = []
    for log in valid_logs:
        entity = check_extra_info(log, all_keys)
        final_logs.append(get_log_data(log, entity))

    return final_logs


def apply_filter(logs, user=None, entities_name=None, hotel_id=None):
    filtered_logs = []
    if user:
        for l in logs:
            if l.get('username') == user:
                filtered_logs.append(l)

    if entities_name:
        for l in logs:
            if l.get('entityType') in entities_name:
                filtered_logs.append(l)

    if hotel_id:
        for l in logs:
            if l.get('hotelCode') == hotel_id:
                filtered_logs.append(l)

    return filtered_logs if len(filtered_logs) > 0 else logs


def check_extra_info(log, all_keys):
    extra_info = str(zlib.decompress(log['extraInfo3'], zlib.MAX_WBITS | 16))
    try:
        if '"key":' in extra_info:
            key = str(ROOM_STATUS_KEY_SEARCH_REGEX.search(extra_info).group(1))
        else:
            key = str(KEY_SEARCH_REGEX.search(extra_info).group(1))

        final_key = all_keys.get(log.get('entityType'), {}).get(key, log)
        if final_key == log:
            final_key = all_keys.get(ENTITY_MODEL_TRANSLATION.get(log.get('entityType')), {}).get(key, log)

        return final_key

    except Exception:
        # print(f"ERROR: {log}")
        return log


def get_room_type_status(entity):
    room_entity = all_keys.get('RoomType').get(entity.get('roomKey'))
    room_name = room_entity.get('name', '')
    date = datetime.datetime.strftime(datetime.datetime.strptime(entity.get('date'), '%Y-%m-%d'), "%d-%m-%Y")
    room_state = entity.get('open')

    return f"Habitación: {room_name} Fecha: {date} Estado de venta ACTUAL: {'Abierta' if room_state else 'Cerrada'}"


def get_yield_modification(entity):
    room_name = all_keys.get('RoomType').get(entity.get('roomKey')).get('name', '')
    rate = all_keys.get('Rate').get(entity.get('rateKey')).get('name', '')
    base_price = entity.get('basePrice', '')
    date = datetime.datetime.strftime(datetime.datetime.strptime(entity.get('date'), '%Y-%m-%d'), "%d-%m-%Y")

    return f"Habitación: {room_name} Tarifa: {rate} Precio Base[Actual]: {base_price} Fecha: {date}"


def get_rate(entity):
    rate_name = entity.get('name')
    identifier = entity.get('localName')

    return f"{f'Identificador: {identifier} ' if identifier else ''}Nombre: {rate_name}"


def get_multirate(entity):
    multirate_identifier = entity.get('localName', '')
    multirate_name = entity.get('name')
    multirate_status = entity.get('enabled')

    return f"{f'Identificador: {multirate_identifier} ' if multirate_identifier else ''}" \
           f"Nombre: {multirate_name} Estado[ACTUAL]: {'Activa' if multirate_status else 'Desactivada'}"


def get_promotion(entity):
    promotion_name = entity.get('name')
    promotion_indentifier = entity.get('identifier', '')
    promotion_status = entity.get('enabled')

    return f"Identificador: {promotion_indentifier} Nombre: {promotion_name} Estado ACTUAL: {'Activa' if promotion_status else 'Desactivada'}"


def get_configuration_property(entity):
    property_value = entity.get('value')
    property_mainkey = entity.get('mainKey')

    if property_mainkey == 'Password Tarjetas':
        property_value = '********'

    return f"Nombre: {property_mainkey}{f' Valor ACTUAL: {property_value}' if property_value else ''}"


def get_web_section(entity):
    section_name = entity.get('name')
    section_status = entity.get('enabled')
    section_type = entity.get('sectionType')
    section_private = entity.get('adminOnly')

    return f"Nombre: {section_name} Estado ACTUAL: {'Activa' if section_status else 'Desactivada'} " \
           f"Tipo de sección: {section_type} Privada: {'SI' if section_private else 'NO'}"


def get_restriction_day_status(entity):
    room_name = all_keys.get('RoomType').get(entity.get('room')).get('name', '')
    calendar_date = datetime.datetime.strftime(datetime.datetime.strptime(entity.get('date'), '%Y-%m-%d'),
                                               "%d-%m-%Y")
    release = entity.get('release')
    min_days = entity.get('minDays')
    max_days = entity.get('maxDays')

    return f"Habitación: {room_name} Fecha en el calendario: {calendar_date} Release: {release} " \
           f"Estancia mínima (días): {min_days} Estancia mínima (días): {max_days}"


def get_promotion_day_status(entity):
    promotion_name = all_keys.get('Promotion').get(entity.get('promotionKey'), {}).get('name')
    date = entity.get('date')
    # No le veo utilidad a sacar el estad, ya que es bool
    # status = entity.get('enabled')
    amount = entity.get('amount')

    return f"Oferta: {promotion_name} Fecha: {date} {f'Cupo: {amount}' if amount != -1 else ''}"


def get_redirection(entity):
    redirection_capacity_target = entity.get('capacityTarget')
    redirection_status = entity.get('enabled')
    redirection_capacity_source = entity.get('capacitySource')

    return f"{f'Capacidad solicitada: {redirection_capacity_source}' if redirection_capacity_source else ''}" \
           f"{f' Capacidad Destino: {redirection_capacity_target}' if redirection_capacity_target else ''}" \
           f" Estado ACTUAL: {'Activa' if redirection_status else 'Desactivada'}"


def get_restriction(entity):
    restriction_status = entity.get('enabled')
    restriction_start_day = datetime.datetime.strftime(
        datetime.datetime.strptime(entity.get('startDate'), '%Y-%m-%d'),
        "%d-%m-%Y")
    restriction_end_day = datetime.datetime.strftime(
        datetime.datetime.strptime(entity.get('endDate'), '%Y-%m-%d'),
        "%d-%m-%Y")

    room_list = []
    if entity.get('roomType2'):
        room_keys = entity.get('roomType2').split(';')
        for key in room_keys:
            if key:
                room_name = all_keys.get('RoomType').get(key).get('name')
                room_list.append(room_name)

    min_days = entity.get('minDays')
    max_days = entity.get('maxDays')
    string_text = ''
    if restriction_status:
        string_text += "Estado: Activa |"
    else:
        string_text += 'Estado: Desactivada |'

    string_text += f"Fecha de inicio: {restriction_start_day} |"
    string_text += f"Fecha de fin: {restriction_end_day} |"

    if room_list:
        string_text += f'Habitación: {", ".join(room_list)}'

    return string_text[:-1]


def get_rate_period(entity):
    rate_name = all_keys.get('Rate').get(entity.get('rateKey')).get('name')
    period_start = entity.get('start')
    period_end = entity.get('end')
    base_price_json = entity.get('basePricesJson')
    base_price_list = []
    supplements_json = entity.get('supplementsJson2')
    supplements_list = []

    for x in base_price_json:
        x = x.split(',')
        room_name = all_keys.get('RoomType').get(x[0].split(':')[1]).get('name', '')
        base_price = x[1].split(':')[1]
        pax = x[2].split(':')[1]
        base_price_list.append([room_name, base_price, pax])

    base_price_block = 'Bloque Precio Base >> '
    for x in base_price_list:
        base_price_block += f'Habitación: {x[0]} Precio: {x[1]} Por persona {f"Si" if x[2] else "No"}'
        base_price_block += ' | '

    for supplement_str in supplements_json:
        supplement_str = supplement_str.split(',')
        type = html.unescape(supplement_str[0].split(':')[1])
        discount = supplement_str[1].split(':')[1]
        regimen_list = []

        for regimen in supplement_str[2:]:
            regimen_str = regimen.split(':')
            if regimen_str[0] == 'RoomType' or regimen_str[0] == 'enabled':
                break
            regimen_name = all_keys.get('Regimen').get(regimen_str[0]).get('name', '')
            price = regimen_str[1] if regimen_str[1] else "n/a"
            regimen_list.append([regimen_name, price])

        # Datos extra para mostrar en form io
        supplements_list.append([type, discount, regimen_list])

    return f"Inicio del periodo: {period_start} Fin del periodo: {period_end} Tarifa: {rate_name}"


def get_picture(entity):
    # En el manager se crea un log por cada bloque de imagenes ya configurado
    picture_key = entity.get('mainKey')
    picture_block = 'Sección en el manager: '

    if picture_key == 'frontPictures':
        picture_block += 'Portada |'
    elif picture_key == 'hotelGalleryPictures':
        picture_block += 'Galería de fotos del hotel |'
    elif picture_key == 'logoPicture':
        picture_block += 'Logotipo |'
    else:
        web_section = all_keys.get('WebSection').get(picture_key, {}).get('name', '')
        if web_section:
            picture_block += 'Secciones | Nombre: ' + web_section + ' |'
        else:
            room = all_keys.get('RoomType').get(picture_key, {}).get('name', '')
            if room:
                picture_block += 'Habitaciones | Nombre: ' + room + ' |'
            else:
                picture_block += "[TEMPORAL] Origen no encontrado o se ha eliminado |"

    name = entity.get('name', '')
    status = entity.get('enabled')
    image_url = entity.get('servingUrl')
    # Con saltos de linea no queda bien :p
    text_string = picture_block
    if name:
        text_string += f"Nombre de la imagen: {name} |"

    if status:
        text_string += "Estado de la imagen: Activa"
    else:
        text_string += "Estado de la imagen: Desactivada |"

    if image_url:
        text_string += f"URL de la imagen: {image_url} |"

    if not name and not status and not image_url:
        text_string += "| Bloque de imágenes sin configurar |"

    return text_string[:-1]


def get_room_type(entity):
    room_name = entity.get('name')
    amount = entity.get('amount')
    capacities = " ".join(entity.get('capacities', []))
    visible_web = entity.get('visibleInWeb')

    return f"Nombre: {room_name} Cupo: {amount} Capacidad: {capacities} " \
           f"Visible en la web: {'Si' if visible_web else 'No'}"


def get_rate_condition(entity):
    # no hay rate key en esta entidad
    condition_name = entity.get('name')
    condition_description = entity.get('description')

    return f"Condición: {condition_name} Descripción: {condition_description}"


def get_promocode(entity):
    promocode_id = entity.get('promocodeId')
    reusable = entity.get('reusable')
    status = entity.get('enabled')
    amount = entity.get('amount')

    return f"ID: {promocode_id} Estado: {status}  Un solo uso {'No' if reusable else 'Si'}   Cantidad: {amount}"


def get_integration_configuration(entity):
    integration_name = entity.get('name')
    # download_booking = entity.get('downloadBooking')
    # retrieve_prices = entity.get('retrievePrices')
    return f"Nombre: {integration_name}"


def get_news(entity):
    name = entity.get('name')
    author = entity.get('author')

    return f"Nombre de la noticia: {name} {f'Autor de la noticia: {author}' if author else ''}"


def get_web_configuration(entity):
    name = entity.get('name')

    return f"Nombre: {name}"


def get_supplement(entity):
    supplement_name = entity.get('name', '')
    # supplement_price = entity.get('price')

    return f"Nombre: {supplement_name}"


def get_price_increase(entity):
    name = entity.get('name')
    return f"Nombre: {name}"


def get_reservation(entity):
    return f"Identificador: {entity.get('identifier')}"


def get_reference_details(entity):
    if entity.key.kind == 'RoomTypeStatus':
        return get_room_type_status(entity)

    elif entity.key.kind == 'YieldModification':
        return get_yield_modification(entity)

    elif entity.key.kind == 'Rate':
        return get_rate(entity)

    elif entity.key.kind == 'MultiRate':
        return get_multirate(entity)

    elif entity.key.kind == 'Promotion':
        return get_promotion(entity)

    elif entity.key.kind == 'ConfigurationProperty':
        return get_configuration_property(entity)

    elif entity.key.kind == 'WebSection':
        return get_web_section(entity)

    elif entity.key.kind == 'RestrictionDayStatus':
        return get_restriction_day_status(entity)

    elif entity.key.kind == 'PromotionDayStatus':
        return get_promotion_day_status(entity)

    elif entity.key.kind == 'Redirection':
        return get_redirection(entity)

    elif entity.key.kind == 'Restriction':
        return get_restriction(entity)

    elif entity.key.kind == 'RatePeriod':
        return get_rate_period(entity)

    elif entity.key.kind == 'Picture':
        return get_picture(entity)

    elif entity.key.kind == 'RoomType':
        return get_room_type(entity)

    elif entity.key.kind == 'RateCondition':
        return get_rate_condition(entity)

    elif entity.key.kind == 'Promocode':
        return get_promotion(entity)

    elif entity.key.kind == 'IntegrationConfiguration':
        return get_integration_configuration(entity)

    elif entity.key.kind == 'News':
        return get_news(entity)

    elif entity.key.kind == 'WebConfiguration':
        return get_web_configuration(entity)

    elif entity.key.kind == 'Reservation':
        return get_reservation(entity)

    elif entity.key.kind == 'Supplement':
        return get_supplement(entity)

    elif entity.key.kind == 'PriceIncrease':
        return get_price_increase(entity)

    else:
        # print(entity)
        not_configured_entities.append(entity)
        return '[TEMPORAL]Sin informacion o se ha eliminado'


def action_simplified(log_action, value):
    if log_action.startswith('ADD'):
        return f'Ha añadido un nuevo elemento en {value}'

    elif log_action.startswith('UPDATE'):
        return f'Ha realizado una modificación en {value}'

    elif log_action.startswith('REMOVE'):
        return f'Ha eliminado un elemento en {value}'


def print_logs(logs):
    for log in logs:
        if log:
            print(f"Usuario: {log.get('user', '')}")
            print(f'Acción: {log.get("action")}')
            print(f"Referencia ====> {log.get('reference')}")
            print(f"Fecha y hora: {log.get('timestamp')}\n")


def get_log_data(log, entity):
    for k, v in ENTITY_TRANSLATIONS.items():
        if k.lower() == log.get('entityType').lower():
            user = log.get('username')
            action = action_simplified(log.get('action'), v)
            reference = get_reference_details(entity)
            log_timestamp = datetime.datetime.strptime(log.get('timestamp'), '%Y-%m-%d %H:%M:%S') + datetime.timedelta(
                hours=2)
            formatted_timestamp = datetime.datetime.strftime(log_timestamp, "%d-%m-%Y %H:%M:%S")

            return {
                'user': user if not user.isdecimal() else 'Manager AUTOMÁTICO',
                'action': action,
                'reference': reference,
                'timestamp': formatted_timestamp
            }

    # print(f'ENTIDAD SIN CONFIGURAR>>>>>>>>>> {log}')
    return None


def get_all_entities(hotel_code):
    convert_id = lambda entity: {id_to_entity_key(hotel_code, x.key): x for x in
                                 datastore_communicator.get_using_entity_and_params(entity, hotel_code=hotel_code,
                                                                                    return_cursor=True)}

    all_configs = convert_id("ConfigurationProperty")
    all_promotions = convert_id("Promotion")
    all_rate = convert_id("Rate")
    all_multirate = convert_id("MultiRate")
    all_websection = convert_id("WebSection")
    all_integrationconfiguration = convert_id("IntegrationConfiguration")
    all_webconfiguration = convert_id("WebConfiguration")
    all_picture = convert_id("Picture")
    all_room = convert_id("RoomType")
    all_redirection = convert_id("Redirection")
    all_reservation = convert_id("Reservation")
    all_ratecondition = convert_id("RateCondition")
    all_priceincrease = convert_id("PriceIncrease")
    all_roomtypestatus = convert_id("RoomTypeStatus")
    all_restrictionstatus = convert_id("RestrictionDayStatus")
    all_final_price_day = convert_id("FinalPriceDay")
    all_restricctions = convert_id("Restriction")
    all_yield_modification = convert_id('YieldModification')
    rate_period = convert_id('RatePeriod')
    regimen = convert_id('Regimen')
    protion_days = convert_id('PromotionDayStatus')
    all_promocodes = convert_id('Promocode')
    all_news = convert_id('News')
    all_supplements = convert_id('Supplement')

    all_keys = {
        "ConfigurationProperty": all_configs,
        "Promotion": all_promotions,
        "Rate": all_rate,
        "MultiRate": all_multirate,
        "WebSection": all_websection,
        "IntegrationConfiguration": all_integrationconfiguration,
        "Picture": all_picture,
        "WebConfiguration": all_webconfiguration,
        "RoomType": all_room,
        "Redirection": all_redirection,
        "Reservation": all_reservation,
        "RateCondition": all_ratecondition,
        "PriceIncrease": all_priceincrease,
        "Restriction": all_restricctions,
        "com.paraty.common.services.shared.model.RoomTypeStatus": all_roomtypestatus,
        "com.paraty.common.services.shared.model.RestrictionDayStatus": all_restrictionstatus,
        "com.paraty.common.services.shared.model.FinalPriceDay": all_final_price_day,
        'YieldModification': all_yield_modification,
        "RatePeriod": rate_period,
        'Regimen': regimen,
        'PromotionDayStatus': protion_days,
        'Promocode': all_promocodes,
        'News': all_news,
        'Supplement': all_supplements,
    }

    return all_keys


if __name__ == '__main__':
    execute(local=True, request={
        "entities_name": ["com.paraty.common.services.shared.model.RoomTypeStatus"],
        "hotel_code": "casual-musica-valencia",
        "num_days": 40,
        "multi_hotel": False,
    })

    # Ejemplo de busqueda en varios hoteles de AP
    # Se activa el mutl_hotel y se pone la parte del hotel code en común
    # execute(local=True, multi_hotel=True, request={
    #     "entities_name": ["IntegrationConfiguration"],
    #     "hotel_code": "ap-",
    #     "num_days": 60
    # })
