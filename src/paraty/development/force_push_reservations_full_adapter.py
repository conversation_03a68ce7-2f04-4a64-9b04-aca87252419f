import json
import sys

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

sys.path.append('..')
import datetime
import requests

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
	get_reservations_of_hotel, get_hotel_advance_config_item

__author__ = 'nmarin'

class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)


def get_cancelled_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	for hotel in all_hotels:

		'''if not hotel.get("applicationId", "") == "best-maritim":
			continue'''

		url_push = ""


		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			#print("%s adapter found in %s", (adapter_name, hotel["name"]))

			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break


			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):

					for reservation in reservations:

						extra_info = json.loads(reservation.get("extraInfo", "{}"))
						if not extra_info.get("external_identifier"):
							#print("reservstion NOT already sent " + extra_info.get("external_identifier"))
							continue


							print("reservation not cancelled " + extra_info.get("external_identifier"))
							continue

						print(reservation.get("identifier"))
						url_push_reservation = url_push + "&identifier=" +  reservation["identifier"]
						print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								url = hotel.get("url")
								rest_client.update(url + "/rest", 'Reservation', reservation)


							#print "pushing: %s" % url_push_reservation
							#print "pushing: %s" % reservation["identifier"]
							#print



							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								print(response.content)
							else:
								print('ERROR reservation could not be pushed')
								print("")




def get_valid_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	x = {
		'arenacenter': ['5B906A78B'],
		'noa-boutique': ['56A52562D'],
		'ar-parquesur': ['38434690'],
		'aranzazu-carlton': ['54987181'],
		'guitart-gold': ['93264786'],
		'sirena-3': ['34585827'],
		'hotel-dynastic': ['17155969'],
		'prinsotel-dorada': ['54035557'],
		'casual-ilbira-granada': ['5967CB3D8'],
		'summum-joan-miro': ['26232624'],
		'psantiago-iii': ['BE-39506879'],
		'ap-sinerama': ['DE15E40B4'],
		'smy-eulalia-algarve': ['71904859-1'],
		'port-vista': ['43961133'],
		'hotel-parasol-garden': ['59354068'],
		'xq-vistamar': ['B07C8389A'],
		'q10-ampolla': ['29321013'],
		'reino-anamaria': ['CBF38B710', 'C00DDA55F']
	}


	for hotel_code, reservations in x.items():

		'''if not hotel.get("applicationId", "") == "oasishoteles-tulum":
			continue'''

		url_push = ""

		'''if not hotel.get("applicationId", "") == "best-maritim":
			continue'''


		if only_specific_timezone:

			has_specific_timezone =  get_hotel_advance_config_item(hotel, "Specific timezone")
			if not has_specific_timezone:
				continue

		'''try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):'''
		if True:

			#print("%s adapter found in %s", (adapter_name, hotel["name"]))

			#reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			'''for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break'''

			url_push = "https://siteminder-adapter.appspot.com/push_reservation?hotel_code=%s" % (hotel_code)
			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				#print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):
					num_iter =1
					for reservation_id in reservations:
						hotel = {"applicationId": hotel_code}
						reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, reservation_id=reservation_id, include_end_date=False,
																 include_cancelled_reservations=True)
						reservation = reservations[0]

						'''
						extra_info = json.loads(reservation.get("extraInfo", "{}"))

						if reservation.get('comments', '') and '@@@TEST@@@' in reservation.get('comments', '').upper():
							#print("reservstion already sent " + extra_info.get("external_identifier"))
							continue

						if reservation.get('cancelled'):
							#print("Discarting %s because IS CANCELLED", reservation["identifier"])
							continue
							#pass

						if reservation.get("modificationTimestamp") and reservation.get("modificationTimestamp") > "2024-02-10 12:00":
							print("MANUAL FIX: " + hotel["name"] + ": " + reservation.get("identifier"))
							continue'''

							#pass

						'''if reservation.get('startDate') < "2023-08-09":
							#print("Discarting %s because star date is: %s", reservation["identifier"], reservation["startDate"])
							continue'''



						'''if "external_identifier" in extra_info and not extra_info.get("external_identifier"):
							pass
						else:
							continue
						'''


						'''paid = float(extra_info.get("payed", "0.0"))
						several_rooms = reservation.get("numRooms")
						if not paid or several_rooms < 2:
							continue'''

						#if not reservation.get("identifier") == "1D67C1697":
						#	continue

						print("AUTOMATIC FIX: " + hotel_code + ": " + reservation_id)
						url_push_reservation = url_push + "&identifier=" + reservation_id
						#print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								datastore_communicator.save_entity(reservation, hotel_code=hotel_code)


							#print "pushing: %s" % url_push_reservation
							#print "pushing: %s" % reservation["identifier"]
							#print



							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								print("OK SITEMINDER")
								num_iter += 1

							else:
								print('ERROR reservation could not be pushed '+ reservation["identifier"])
						else:
							print(str(num_iter) + ": PREVIEW " + reservation["timestamp"] + " " + hotel.get("applicationId", "") + " " + reservation["identifier"])
							num_iter += 1

if __name__ == "__main__":

	# if len(sys.argv) < 2:
	# 	print
	# 	print "Usage: python force_push_reservations_full_adapter.py adapter_name"
	# 	print
	# 	sys.exit(1)
	#
	# adapter_name = sys.argv[1]

	post_it_really = True
	modify_reservation = True
	adapter_name = "siteminder"

	only_specific_timezone = False

	timestamp = '2025-02-10 08:40:00'
	to_datetime = '2025-02-10 11:10:00'
	print("ALL HAS FINISHED")




	get_valid_reservations_from_adapter(adapter_name, timestamp, to_datetime)


