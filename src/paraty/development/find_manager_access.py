import os
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from datetime import datetime
import csv

def get_manager_access(application_id, filter_function, params):
    search_params=[('currentAppId', '=', application_id)]
    all_hotel_acess =  datastore_communicator.get_using_entity_and_params('AdminUserSession', search_params=search_params, hotel_code="admin-hotel")


    for access_manager in all_hotel_acess:
        if filter_function(access_manager, params):
            access = {}
            user = access_manager['userName']
            timestamp = str(access_manager["timestamp"])[:-3]
            access[user] = datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')
            print(access)

    print("ok")




def access_ok_by_dates(access_manager, params):

    date_from = params[0]
    date_to = params[1]

    return access_manager.get('timestamp') and (access_manager.get('timestamp') >= date_from and access_manager.get('timestamp') <= date_to)



if __name__ == '__main__':

    application_id = 4862294785261568



    #add here other function filter if needed
    date_from = int(datetime(2022, 5, 9, 0, 0, 0).strftime('%s') + "000")
    date_to = int(datetime(2022, 5, 13, 23, 59, 59).strftime('%s') + "999")
    params= [date_from, date_to]
    function_filter = access_ok_by_dates


    get_manager_access(application_id, function_filter, params)





