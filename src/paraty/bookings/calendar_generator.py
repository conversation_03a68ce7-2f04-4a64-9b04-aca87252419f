import base64
import logging
from datetime import datetime
from urllib.parse import quote

from paraty_commons_3.email_utils import sendEmail

REQUIRED_FIELDS = [
    'identifier',
    'customer_name',
    'check_in',
    'check_out',
    'hotel_name',
    'hotel_code',
    'hotel_address'
]


def validate_calendar_data(data: dict):
    """
    Validates that all required fields are present in the data dictionary.
    Raises a ValueError if any field is missing.
    """
    missing_fields = [field for field in REQUIRED_FIELDS if field not in data or not data[field]]
    if missing_fields:
        logging.exception(f"[CALENDAR GENERATOR] Missing required fields in calendar data: {missing_fields}")
        raise Exception(f"The following required fields are missing: {', '.join(missing_fields)}")


def generate_calendar_files(calendar_data: dict):
    """
    Generates an .ics file and a Google Calendar URL from a data dictionary.
    """
    try:
        logging.info(f"[CALENDAR GENERATOR] Generating calendar files with data: {calendar_data}")
        validate_calendar_data(calendar_data)

        return _generate_ics_and_google_calendar(
            uid=calendar_data['identifier'],
            customer_name=calendar_data['customer_name'],
            check_in=calendar_data['check_in'],
            check_out=calendar_data['check_out'],
            hotel_name=calendar_data['hotel_name'],
            hotel_address=calendar_data['hotel_address']
        )
    except Exception as e:
        logging.exception(f"[CALENDAR GENERATOR] Error generating calendar files: {e}")
        raise Exception(f"Error generating calendar files: {e}")


def _generate_ics_and_google_calendar(uid, customer_name, check_in, check_out, hotel_name, hotel_address):
    dt_start = datetime.strptime(check_in, "%Y-%m-%d").strftime("%Y%m%dT150000Z")
    dt_end = datetime.strptime(check_out, "%Y-%m-%d").strftime("%Y%m%dT110000Z")
    ics_content = f"""BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//{hotel_name}//Booking System//EN
BEGIN:VEVENT
UID:{uid}@{hotel_name.lower().replace(' ', '')}.com
DTSTAMP:{datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')}
DTSTART:{dt_start}
DTEND:{dt_end}
SUMMARY:Booking at {hotel_name}
DESCRIPTION:Booking for {customer_name}. Reservation No: {uid}
LOCATION:{hotel_address}
END:VEVENT
END:VCALENDAR"""

    logging.info(f"[CALENDAR GENERATOR] ICS content to base64")
    ics_base64 = base64.b64encode(ics_content.encode('utf-8')).decode('utf-8')

    title = quote(f"Booking at {hotel_name}")
    dates = f"{dt_start}/{dt_end}"
    details = quote(f"Booking for {customer_name}. Reservation No: {uid}")
    location = quote(hotel_address)
    google_calendar_url = (
        f"https://www.google.com/calendar/render?action=TEMPLATE"
        f"&text={title}&dates={dates}&details={details}&location={location}"
    )
    return {
        "ics_base64": ics_base64,
        "google_calendar_url": google_calendar_url
    }
