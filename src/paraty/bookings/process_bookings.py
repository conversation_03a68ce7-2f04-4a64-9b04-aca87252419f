import datetime
import logging
import json

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.common_data import common_data_provider

AMARE_HOTELS = ["amare-", "fuerte-", "olee-"]
nowDate = datetime.datetime.now().strftime("%Y-%m-%d")
yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")


def process_antiota_bookings():

    all_hotels = get_all_valid_hotels()
    valid_hotels = []
    for hotel in all_hotels:
        if any(hotel["applicationId"].startswith(prefix) for prefix in AMARE_HOTELS):
            valid_hotels.append(hotel)

    for hotel in valid_hotels:
        reservations = common_data_provider.get_reservations_of_hotel(hotel, yesterday, nowDate)
        for reservation in reservations:
            if "antiota_promotion" in reservation["extraInfo"]:
                if reservation["promocode"] != "OFERTAOTA":
                    logging.info("UPDATING BOOKING %s in HOTEL: %s" % (reservation["identifier"], hotel["applicationId"]))
                    reservation["promocode"] = "OFERTAOTA"
                    datastore_communicator.save_entity(reservation, hotel["applicationId"])

    return "OK"
def reservation_has_antiota_promotion(reservation):
    try:
        promotions_list = json.loads(reservation["extraInfo"]).get("shopping_cart_human_read").get("rooms")[0].get("promotions_list")
        for promotion in promotions_list:
            if "antiota_promotion" in promotion["name_class"]:
                return True
        return False
    except:
        return False


if __name__ == "__main__":
    process_bookings()
