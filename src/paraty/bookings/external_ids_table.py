import json
import logging

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.common_data import common_data_provider

import datetime

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

def fill_external_ids_table():

    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    hotels = get_all_valid_hotels()
    for hotel in hotels:
        try:
            show_external = get_hotel_advance_config_value(hotel["applicationId"], 'Show reservations external ids')
            if show_external:
                external_ids = []
                bookings = common_data_provider.get_reservations_of_hotel(hotel, yesterday.strftime("%Y-%m-%d"), yesterday.strftime("%Y-%m-%d"), include_end_date=True, include_modified_reservations=True, include_cancelled_reservations=True)

                for booking in bookings:
                    extra_info = json.loads(booking.get("extraInfo", "{}"))
                    if extra_info.get("external_identifier_for_manager"):
                        external_ids.append({
                            "booking_id": booking.get("identifier", ""),
                            "external_id": extra_info.get("external_identifier_for_manager")
                        })
                ids = [""] * len(external_ids)
                datastore_communicator.save_multiple_entities("ExternalIdentifiers", ids, external_ids, hotel["applicationId"])
        except Exception as e:
            logging.warning("Error with external ids in hotel %s: %s", hotel["applicationId"], e)

    return "OK"

if __name__ == "__main__":
    fill_external_ids_table()