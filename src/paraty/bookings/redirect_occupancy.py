import logging

from flask import jsonify

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


def parse_occupancy(occupancy):
    return list(map(int, occupancy.split("-")))


def find_and_sort_combinations(available_rooms, search, num_max_rooms=False):
    if search == "1-0-0" and "1-0-0" not in available_rooms:
        return [["2-0-0"]]

    all_combinations = []
    exact_combinations = find_combinations(available_rooms, search)
    sort_combinations(exact_combinations)
    all_combinations.extend(exact_combinations)

    parsed_occupancy = parse_occupancy(search)
    adults, children, infants = parsed_occupancy
    total_children_infants = children + infants
    seen = {search}

    # Genera combinaciones modificando niños a adultos y viceversa
    for i in range(1, children + 1):
        modified_search = f"{adults + i}-{children - i}-{infants}"
        modified_combinations = find_combinations(available_rooms, modified_search)
        sort_combinations(modified_combinations)
        add_unique_combinations(all_combinations, modified_combinations, seen, total_children_infants)

    # Genera combinaciones donde todos los niños se tratan como adultos
    final_search = f"{adults + children}-0-{infants}"
    final_combinations = find_combinations(available_rooms, final_search)
    sort_combinations(final_combinations)
    add_unique_combinations(all_combinations, final_combinations, seen, total_children_infants)

    all_combinations = filter_initial_occupation(all_combinations, search)

    if num_max_rooms and all_combinations:
        all_combinations = extract_combinations_with_max_rooms(all_combinations, int(num_max_rooms))
        logging.info(f"[Redirect Occupancy] extract_combinations_with_max_rooms {all_combinations}")

    logging.info(f"[Redirect Occupancy] Combinations {all_combinations}")

    return jsonify(all_combinations)


def extract_combinations_with_max_rooms(combinations, max_rooms):
    return [comb for comb in combinations if len(comb) <= max_rooms]


def find_combinations(available_rooms, search):
    desired_occupancy = parse_occupancy(search)
    result = []
    seen = set()
    search_combinations_recursively(available_rooms, desired_occupancy, [], result, seen, 0)
    return result


def search_combinations_recursively(available_rooms, remaining_occupancy, current_combination, result, seen, start_index):
    logging.info(f"[Redirect Occupancy] search_combinations_recursively: {available_rooms}, {remaining_occupancy}, {current_combination}, {result}, {seen}, {start_index}")
    if len(current_combination) > 3:
        return
    if remaining_occupancy == [0, 0, 0]:
        sorted_comb = sorted(current_combination)
        comb_key = ",".join(sorted_comb)
        if comb_key not in seen:
            result.append(sorted_comb)
            seen.add(comb_key)
        return

    for i in range(start_index, len(available_rooms)):
        room = parse_occupancy(available_rooms[i])
        if all(room[j] <= remaining_occupancy[j] for j in range(3)):
            new_comb = current_combination + [available_rooms[i]]
            new_remaining = [remaining_occupancy[j] - room[j] for j in range(3)]
            search_combinations_recursively(available_rooms, new_remaining, new_comb, result, seen, i)


def sort_combinations(combinations):
    combinations.sort(key=lambda c: (len(c), -max(parse_occupancy(room)[0] for room in c)))


def add_unique_combinations(all_combinations, new_combinations, seen, original_children_infants):
    for comb in new_combinations:
        sorted_comb = sorted(comb)
        comb_key = ",".join(sorted_comb)
        if comb_key not in seen:
            all_combinations.append(sorted_comb)
            seen.add(comb_key)


def filter_initial_occupation(combinations, initial_occupation):
    initial_occupancy = parse_occupancy(initial_occupation)
    return [comb for comb in combinations if
            not any(is_same_occupancy(parse_occupancy(room), initial_occupancy) for room in comb)]


def is_same_occupancy(occupancy1, occupancy2):
    return occupancy1 == occupancy2


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='RoomType', ttl_seconds=3600*24)
def get_available_rooms_occupancy(hotel_code):
    hotel_rooms = get_using_entity_and_params("RoomType", hotel_code=hotel_code)
    if not hotel_rooms:
        logging.error(f"[Redirect Occupancy] No rooms found for hotel_code: {hotel_code}")
        raise Exception(f"No rooms found for hotel_code: {hotel_code}")

    logging.info(f"[Redirect Occupancy] Getting rooms capacities for hotel_code: {hotel_code}")
    rooms_capacities = sorted(
        {capacity for room in hotel_rooms for capacity in room.get("capacities", [])}
    )

    return rooms_capacities


def get_new_combination(hotel_code, occupancy, num_max_rooms=False):
    try:
        available_rooms = get_available_rooms_occupancy(hotel_code)
        return find_and_sort_combinations(available_rooms, occupancy, num_max_rooms)
    except Exception as e:
        logging.error(f"Error getting new occupancy, hotel_code: {hotel_code}, occupancy: {occupancy}")
        return {}


if __name__ == "__main__":
    result = get_new_combination("hotel-puentereal", "7-2-0")
    print(result)
