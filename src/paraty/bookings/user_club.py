import datetime
import logging
import json

import requests

from flask import request, make_response

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params

LOYALTY_ENDPOINT = 'https://loyalty-seeker.appspot.com'


def user_club_points(request):
    try:
        hotel_code = request.args.get("hotel_code")
        body = extract_params()
        identifier = request.args.get('identifier')
        if not identifier:
            identifier = body.get('identifier')


        reservations = get_using_entity_and_params("Reservation", search_params=[["identifier","=",identifier]], hotel_code=hotel_code)

        if not reservations:
            logging.info(f"not found reservations in hotel_code:{hotel_code} identifier:{identifier}")

        for reservation in reservations:
            if reservation.get("cancelled"):
                logging.info(f"Deleting transaction point for cancelled reservation {reservation.get('identifier')}")
                extra_info = json.loads(reservation.get("extraInfo"))
                namespace = extra_info.get("user_club_info").get("session_namespace")
                booking_id = reservation.get("identifier")
                transaction = get_using_entity_and_params("transactionsClub", search_params=[["booking_id","=",booking_id]], hotel_code="loyalty-seeker:"+namespace)
                if transaction:
                    data = {"action": "delete_transaction",
                            "transaction_key": str(transaction[0].id),
                            "namespace": namespace}
                    logging.info(f"Deleting transaction {transaction[0]}")
                    requests.post(LOYALTY_ENDPOINT + "/transactions/", json=data)
        return True
    except:
        logging.error(f"Fail to remove transaction point of {hotel_code}")

def extract_params():
    return request.json if request.is_json else json.loads(request.data.decode('utf-8')) if \
        len(request.data) > 0 else {}
