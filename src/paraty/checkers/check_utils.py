id_regex_for_checks={
    "AT": {
        "passport": "^[A-z]\\d{7}$",
        "id_number": ["^[0-9]{12}$", "^[A-z0-9+/]{22}[A-z0-9+/=][A-z0-9+/=]$"],
        "zip_code": "^[0-9]{4}$"
    },
    "BE": {
        "passport": "^[A-z]{2}\\d{6}$",
        "id_number": ["^[0-9]{2}\\.?[0-9]{2}\\.?[0-9]{2}-[0-9]{3}\\.?[0-9]{2}$"],
        "zip_code": "^[1-9][0-9]{3}$"
    },
    "BG": {
        "passport": "^\\d{9}$",
        "id_number": ["^[0-9]{2}[0,1,2,4][0-9][0-9]{2}[0-9]{4}$"],
        "zip_code": "^[0-9]{4}$"
    },
    "CH": {
        "passport": "^[A-z]\\d{7}$",
        "id_number": ["^([Aa][Dd][Mm]|[Cc][Hh][Ee])\\d{9}$"],
        "zip_code": "^[0-9]{4}$"
    },
    "CN": {
        "passport": "^([Gg]|[Ee])\\d{8}$",
        "id_number": ["^\\d{17}[\\dX]$"],
        "zip_code": "^[0-9]{6}$"
    },
    "DE": {
        "passport": "^([CFGHJKLMNPRTVWXYZ0-9]|[cfghjklmnprtvwxyz0-9]){9}$",
        "id_number": ["^[0-9]{2}[0,1][0-9][0-9]{2}-[A-z]-[0-9]{5}$"],
        "zip_code": "^[0-9]{5}$"
    },
    "ES": {
        "passport": "^[A-z0-9]{2}([A-z0-9]?)\\d{6}$",
        "id_number": ["^[0-9XMLKYxmlky][0-9]{7}[A-z]$", "([XYZ]|[xyz])\\d{7}[A-z]$"],
        "id_functions": [lambda s: s[-1] == 'TRWAGMYFPDXBNJZSQVHLCKE'[int(s[:-1]) % 23],
                         lambda s: s[-1] == 'TRWAGMYFPDXBNJZSQVHLCKE'[int(s[:-1].replace('X', '0', 1).replace('Y', '1', 1).replace('Z', '2', 1)) % 23]
                        ],
        "zip_code": "^(0[1-9]|[1-4][0-9]|5[0-2])[0-9]{3}$"
    },
    "FI": {
        "passport": "^[A-z]{2}\\d{7}$",
        "id_number": ["^[0-9]{2}\\.?[0,1][0-9]\\.?[0-9]{2}[-+Aa][0-9]{3}[A-z]$"],
        "zip_code": "^[0-9]{5}$"
    },
    "FR": {
        "passport": "^\\d{2}[A-z]{2}\\d{5}$",
        "id_number": ["^[0-9]{12}$", "^[A-Z0-9]{9}[0-9]$"],
        "zip_code": "^[0-9]{5}$"
    },
    "GB": {
        "passport": "^\\d{9}$",
        "id_number": ["^([A-CEGHJ-PR-TW-Z]|[a-ceghj-pr-tw-z])([A-CEGHJ-NPR-TW-Z]|[a-ceghj-npr-tw-z]){1}[0-9]{6}[A-Da-d\\s]$", "^[0-9]{10}$"],
        "zip_code": "^([A-Za-z][A-Ha-hJ-Yj-y]?\\d{1,2}[A-Za-z]?|[A-Za-z]{2}\\d{1,2}) ?\\d[A-Za-z]{2}$"
    },
    "GR": {
        "passport": "^[A-z]{2}\\d{7}$",
        "id_number": ["^[A-z][ -]?[0-9]{6}$"],
        "zip_code": "^[0-9]{3} ?[0-9]{2}$"
    },
    "HU": {
        "passport": "^[A-z]{2}(\\d{6}|\\d{7})$",
        "id_number": ["^[1-8] ?[0-9]{2}[0,1][0-9][0-9]{2} ?[0-9]{4}$"],
        "zip_code": "^[0-9]{4}$"
    },
    "IE": {
        "passport": "^[A-z0-9]{2}\\d{7}$",
        "id_number": ["^[0-9]{7}[A-z]W?$"],
        "zip_code": "^[A-Za-z0-9]{3,7}$"
    },
    "IT": {
        "passport": "^[A-z]{6}\\d{2}[A-z]\\d{2}[A-z]\\d{3}[A-z]$",
        "id_number": ["^[A-z0-9]{6,20}$"],
        "zip_code": "^[0-9]{5}$"
    },
    "NL": {
        "passport": "^[A-z]{2}[A-z0-9]{6}\\d$",
        "id_number": ["^[0-9]{9}$"],
        "zip_code": "^[1-9][0-9]{3} ?[A-Za-z]{2}$"
    },
    "PO": {
        "passport": "^[A-z]{2}\\d{7}$",
        "id_number": ["^[A-z]{3}\\d{7}$"],
        "zip_code": "^[0-9]{2}-[0-9]{3}$"
    },
    "PT": {
        "passport": "^[A-z]\\d{6}$",
        "id_number": ["^[0-9]{8}$"],
        "zip_code": "^[0-9]{4}-[0-9]{3}$"
    },
    "RO": {
        "passport": "^\\d{8,9}$",
        "id_number": ["^[1-8][0-9]{2}[0,1][0-9][0-9]{4}$"],
        "zip_code": "^[0-9]{6}$"
    },
    "SE": {
        "passport": "^\\d{8}$",
        "id_number": ["^[0-9]{2}[0-1][0-9][0-9]{2}[-+][0-9]{4}$"],
        "zip_code": "^[0-9]{3} ?[0-9]{2}$"
    },
    "SL": {
        "passport": "^[Pp][A-z]\\d{7}$",
        "id_number": ["^[0-9]{13}$"],
        "zip_code": "^[0-9]{4}$"
    },
    "US": {
        "passport": "^\\d{9}$",
        "id_number": ["^\\d{9}$"],
        "zip_code": "^[0-9]{5}(-[0-9]{4})?$"
    }
}