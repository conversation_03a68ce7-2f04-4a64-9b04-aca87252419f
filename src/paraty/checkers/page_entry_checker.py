import concurrent
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor

from tqdm import tqdm

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_internal_url


def delete_invalid_page_entries_handler():
    with ThreadPoolExecutor(max_workers=20) as executor:
        # Submit all tasks and get future objects
        all_hotels = get_all_valid_hotels()

        all_hotels = [x for x in all_hotels]

        futures = [executor.submit(delete_invalid_page_entries_v2, x['applicationId']) for x in all_hotels]

        # Use tqdm to create a progress bar
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
            pass  # We don't need to do anything here, tqdm updates automatically


def delete_invalid_page_entries_v2(hotel_code):    
    print(f"Checking hotel {hotel_code}")

    page_entries = datastore_communicator.get_using_entity_and_params("PageEntry3", projections=["path"], hotel_code=hotel_code, return_cursor=True)
    entities_to_delete = []
    base_url = get_internal_url(hotel_code)
    
    # Crear una lista de tuplas (base_url, path) para procesar
    urls_to_check = [(base_url, entry.get("path", "")) for entry in page_entries]
    
    # Procesar en paralelo las verificaciones de URL
    with ThreadPoolExecutor(max_workers=10) as executor:
        duplicate_results = [has_duplicate_path_segments(path) for _, path in urls_to_check]
        url_results = list(executor.map(lambda x: return_code_error(*x), urls_to_check))
    
    # Combinar resultados
    for entry, is_duplicate, has_error in zip(page_entries, duplicate_results, url_results):
        if is_duplicate or has_error:
            entities_to_delete.append(entry)

    if not entities_to_delete:
        return

    print(f"Deleting {len(entities_to_delete)} invalid page entries")
    entity_keys_to_delete = [x.key for x in entities_to_delete]
    datastore_communicator.delete_entity_multi(entity_keys_to_delete, hotel_code)

def has_duplicate_path_segments(path):
    print(f"Checking {path}")
    segments = [seg for seg in path.split('/') if seg]
    for i in range(len(segments)-1):
        if segments[i] == segments[i+1]:
            return True
    return False

def return_code_error(base_url, path):
    import requests
    from urllib.parse import urljoin
    
    full_url = urljoin(base_url, path)
    
    try:
        print(f"Calling {full_url}")
        response = requests.get(full_url, timeout=10, allow_redirects=True)
        return response.status_code != 200
    except requests.RequestException:
        return True

if __name__ == '__main__':

    with ThreadPoolExecutor(max_workers=20) as executor:
        # Submit all tasks and get future objects
        all_hotels = get_all_valid_hotels()

        all_hotels = [x for x in all_hotels]

        futures = [executor.submit(delete_invalid_page_entries_v2, x['applicationId']) for x in all_hotels]

        # Use tqdm to create a progress bar
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
            pass  # We don't need to do anything here, tqdm updates automatically
