import datetime
import json
import logging

from paraty_commons_3 import date_utils
from paraty_commons_3.bigquery import bigquery_communicator
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

MIN_USER_ENTRIES_PER_DAY = 10

def _get_user_entry_query():

    min_date = datetime.datetime.now() - datetime.timedelta(days=7)
    min_date_str = date_utils.date_to_string(min_date)

    return f'''
        SELECT
          count(*) as num_entries,
          hotel_code
        FROM
          `analysis-seeker.bi_dataset.USER_ENTRY_P`
        WHERE
          DATE(timestamp) > '{min_date_str}'
        GROUP BY
          hotel_code
        order by num_entries desc
        '''


@timed_cache(hours=1)
def get_hotels_to_check():

    candidates = get_all_valid_hotels()

    invalid_names = ['test', 'demo']

    # Remove hotels with invalid names
    candidates = list(filter(lambda x: not any([y in x['applicationId'].lower() for y in invalid_names]), candidates))


    user_entries_in_last_days = bigquery_communicator.execute_query(_get_user_entry_query())
    user_entries_per_hotel = {x['hotel_code']: x['num_entries'] for x in user_entries_in_last_days}

    final_candidates = []
    for candidate in candidates:
        if user_entries_per_hotel.get(candidate['applicationId'], 0) > MIN_USER_ENTRIES_PER_DAY:
            final_candidates.append(candidate)
        else:
            logging.info("Hotel %s has %s entries in the last 7 days" % (candidate['applicationId'], user_entries_per_hotel.get(candidate['applicationId'], 0)))

    logging.info("Found %s candidates" % len(final_candidates))

    return  json.dumps([x['applicationId'] for x in final_candidates])

if __name__ == '__main__':
    print(get_hotels_to_check())