import re


def check_html_handler(request):
    status = 'ok'
    messages = []

    target_html = request.values.get('html')
    is_broken = has_broken_tags(target_html)
    if is_broken:
        status = 'error'
        messages.append('Broken tags found')

    return {'status': status, 'messages': messages}


def has_broken_tags(html):
    tags_without_close = ['img', '<br', '<hr', 'meta', 'link', 'input']
    tags_to_delete = []
    tags_opened = re.findall('<(?!/).+?>', html)
    tags_closed = re.findall(r'</.+?>', html)
    tags_breakline_closed = re.findall(r'</br.+?>', html)

    for occurrence in tags_opened:
        for tag in tags_without_close:
            if tag in occurrence:
                tags_to_delete.append(occurrence)

    return not (len(tags_opened) - len(tags_to_delete)) - (len(tags_closed) - len(tags_breakline_closed)) == 0