from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def clean_deleted_rates(date):

    all_hotels = get_all_valid_hotels()

    for hotel in all_hotels:
        if "htop" in hotel.get("applicationId"):
            all_rates = common_data_provider.get_rates_of_hotel(hotel)
            all_rates_map = {x['key']: x for x in all_rates}

            all_rts = datastore_communicator.get_using_entity_and_params("RoomTypeStatus",
                                                                         search_params=[("date", ">=", date)],
                                                                         hotel_code=hotel["applicationId"])

            for rts in all_rts:
                closedRateBoard = rts.get("closedRateBoard", "")
                if not closedRateBoard:
                    continue
                initial_len = len(closedRateBoard)
                rates = {}
                closed = closedRateBoard.split(";")
                for close in closed:
                    rateBoard = close.split("_@_")
                    if rateBoard[0] not in rates and rateBoard[0] != "":
                        rates[rateBoard[0]] = 0
                    if rateBoard[0] in rates:
                        rates[rateBoard[0]] += 1

                rate_key_to_delete = []
                for rateKey in rates.keys():
                    if not all_rates_map.get(rateKey):
                        rate_key_to_delete.append(rateKey)

                for close in closed:
                    rateBoard = close.split("_@_")
                    if rateBoard[0] in rate_key_to_delete:
                        closedRateBoard = closedRateBoard.replace(close, "")

                closedRateBoard = closedRateBoard.replace(";;", ";")
                rts["closedRateBoard"] = closedRateBoard

                print(f"from{initial_len} to {len(closedRateBoard)}")

            try:
                datastore_communicator.save_entity_multi(all_rts, hotel["applicationId"])
            except Exception as e:
                print(e)

if __name__ == "__main__":

    date = "2024-12-23"
    clean_deleted_rates(date)
