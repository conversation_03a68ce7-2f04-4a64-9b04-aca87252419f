import re
from paraty.checkers.check_utils import id_regex_for_checks


def validate_identifier(country_id, id_number):
    if country_id in id_regex_for_checks:
        regexes = id_regex_for_checks[country_id]
        if re.match(regexes["passport"], id_number):
            return True

        for regex in regexes["id_number"]:
            if re.match(regex, id_number):
                if "id_functions" in regexes:
                    result = False
                    for lambda_function in regexes["id_functions"]:
                        try:
                            is_valid = lambda_function(id_number)
                            if is_valid:
                                result = True
                                break
                        except:  # if validation function fails, the validation result must remain false
                            pass
                    return result
                return True
        return False

    return True
