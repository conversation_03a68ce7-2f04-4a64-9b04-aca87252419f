import datetime
import logging
import re
import threading
from copy import copy

from postmarker.core import PostmarkClient

from paraty.development.cancel_pending_reservations_not_paid import cancelBooking
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

EMAILS_REGEX = ['.*dataseeker.*', '.*paraty.*', '.*ring2travel.*']

MAX_THREADS = 10
SEMAPHORE = threading.Semaphore(value=MAX_THREADS)
ALL_THREADS = []


def check_hotel_bookings(hotel_code, hotel_info, bookings_found):
    try:
        SEMAPHORE.acquire()
        date = datetime.datetime.now() - datetime.timedelta(days=30)
        date = datetime.datetime.strftime(date, "%Y-%m-%d")

        search_params = [('timestamp', '>=', date)]
        all_bookings = datastore_communicator.get_using_entity_and_params('Reservation', search_params, hotel_code=hotel_code, return_cursor=True)
        for booking in all_bookings:
            if not booking['cancelled']:
                if list(filter(lambda x: re.match(x, booking['email']), EMAILS_REGEX)):
                    bookings_found.setdefault(hotel_info['name'], []).append({
                        'email': booking['email'],
                        'identifier': booking['identifier'],
                        'hotel_code': hotel_code
                    })

    except Exception as e:
        logging.error(e)

    SEMAPHORE.release()


def check_active_bookings_handler(request):
    bookings_found = {}
    all_hotels = get_all_hotels()


    for hotel_code, hotel_info in all_hotels.items():
        if 'demo' in hotel_code or 'test' in hotel_code or 'omnibees' in hotel_code:
            continue

        thread_element = threading.Thread(target=check_hotel_bookings, args=(hotel_code, hotel_info, bookings_found))
        ALL_THREADS.append(thread_element)
        thread_element.start()

    for thread_element in ALL_THREADS:
        thread_element.join()


    base_email = """
    	<h1>@@hotel_name@@</h1>
    	========================<br>
    	<br>
    	@@results@@
    	<br><br><br><br>
    	"""

    final_result = """
        Para evitar la cancelación automática de la reserva, es necesario incluir la nota "NO CANCELAR" en los comentarios de la reserva.<br>
    """
    emails_to_send = []
    for hotel_name, booking_list in bookings_found.items():
        base_email_copy = copy(base_email)
        base_email_copy = base_email_copy.replace("@@hotel_name@@", hotel_name)

        results_builded = ''
        for booking_info in booking_list:
            emails_to_send.append(booking_info['email'])
            cancellation_response = cancel_booking(booking_info['identifier'], booking_info['hotel_code'])
            results_builded += f"{booking_info['email']} - {booking_info['identifier']}{' - Reserva cancelada' if cancellation_response == 200 else ''}<br>"
            results_builded += '<br>'

        base_email_copy = base_email_copy.replace("@@results@@", results_builded)
        final_result += base_email_copy

    emails_to_send = set(emails_to_send)

    emails_on_copy = ('<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>'
                      )

    # Used external library because the actual one is not working, at least locally
    postmark = PostmarkClient(server_token='************************************')
    postmark.emails.send(
        From='Paraty Robot <<EMAIL>>',
        To=','.join(emails_to_send),
        Cc=','.join(emails_on_copy),
        Subject='Reservas activas [GRAVE]',
        HtmlBody=final_result
    )

    # sendEmail('<EMAIL>', 'Reservas activas [GRAVE]', '', final_result)

    return bookings_found


def cancel_booking(identifier, hotel_code):
    reservations = list(get_using_entity_and_params("Reservation", hotel_code=hotel_code, return_cursor=True,
                                                    search_params=[("identifier", "=", identifier)]))
    if not reservations:
        return False

    reservation = reservations[0]
    comments = reservation.get('comments')
    email = reservation.get('email')

    if (comments and "NO CANCELAR" in comments.upper()) or ("ring2travel." in email):
        return False

    return cancelBooking(id_to_entity_key(hotel_code, reservation.key), hotel_code)


if __name__ == '__main__':
    check_active_bookings_handler(None)