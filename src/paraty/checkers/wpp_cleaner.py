from collections import defaultdict

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def clean_duplicated_languages_wpp():
    counter = 0
    hotels = get_all_valid_hotels()
    for hotel in hotels:
        if "amaika" in hotel["applicationId"]:
            wpp = datastore_communicator.get_using_entity_and_params("WebPageProperty", hotel_code=hotel["applicationId"])

            grouped_entities = defaultdict(list)

            for entity in wpp:
                key = (entity["entityKey"], entity["mainKey"], entity["languageKey"])
                grouped_entities[key].append(entity)

            # Identificar y eliminar duplicados
            for key, group in grouped_entities.items():
                if len(group) > 1:
                    # Mantener una entidad (por ejemplo, la primera) y eliminar las demás
                    entities_to_delete = group[1:]  # Todas menos la primera
                    list_of_keys = [x.key for x in entities_to_delete]
                    counter += len(entities_to_delete)
                    #datastore_communicator.delete_entity_multi(list_of_keys, hotel["applicationId"])
                    pass
    print(counter)
if __name__ == "__main__":
    clean_duplicated_languages_wpp()