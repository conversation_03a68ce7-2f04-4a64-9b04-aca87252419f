from paraty_commons_3.datastore import datastore_communicator


def get_web_update_configuration(request):

	current_entity = "WebConfiguration"
	hotel_code = request.values.get("hotel_code")
	name = request.values.get("configuration")

	search_params = []
	if name:
		search_params = [('name', '=', name)]

	properties = datastore_communicator.get_using_entity_and_params(current_entity, search_params, hotel_code=hotel_code)

	result = {"result": properties}

	return result