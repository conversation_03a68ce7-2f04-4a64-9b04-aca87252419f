import subprocess

from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def _get_all_hotel_projects():
	all_hotels = hotel_manager_utils.get_all_hotels()

	result = set()

	for hotel in all_hotels.values():
		project, namespace = datastore_utils.get_project_and_namespace_from_hotel(hotel)
		if project:
			result.add(project)

	return result


def update_all_projects():
	all_projects = _get_all_hotel_projects()
	for project in all_projects:
		update_service(project)


def update_service(project_name):
	try:
		execute_command('gcloud services enable cloudtasks.googleapis.com --project %s' % project_name)
	except Exception as e:
		print("Error at: %s" % project_name)
		print(e)


if __name__ == '__main__':
	update_all_projects()