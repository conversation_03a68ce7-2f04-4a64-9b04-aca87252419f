import subprocess

from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils




def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def _get_all_hotel_projects():
	all_hotels = hotel_manager_utils.get_all_hotels()

	result = set()

	for hotel in all_hotels.values():
		project, namespace = datastore_utils.get_project_and_namespace_from_hotel(hotel)
		if project:
			result.add(project)

	return result


def update_all_cron_jobs():
	all_projects = _get_all_hotel_projects()

	for project in all_projects:

		if 'admin-hotel' in project:
			continue

		if 'adapter' in project or 'seeker' in project:
			continue

		# if not 'test' in project:
		# 	continue
		try:
			update_cron_job(project)
		except:
			print("Error updating project %s" % project)


def update_cron_job(project_name):

	# Modify this url before running this script
	QUEUE_YAML_PATH = '/Users/<USER>/projects/hotel-webs/source/queue.yaml'

	execute_command('gcloud app deploy %s --project %s --quiet' % (QUEUE_YAML_PATH, project_name))

if __name__ == '__main__':
	update_all_cron_jobs()