import subprocess

from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils

CRON_JOB_YAML_PATH = '~/projects/hotel-webs/source/cron.yaml'


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def _get_all_hotel_projects():
	all_hotels = hotel_manager_utils.get_all_hotels()

	result = set()

	for hotel in all_hotels.values():
		project, namespace = datastore_utils.get_project_and_namespace_from_hotel(hotel)
		if project:
			result.add(project)

	return result


def update_all_cron_jobs():
	all_projects = _get_all_hotel_projects()
	for project in all_projects:
		update_cron_job(project)


def update_cron_job(project_name):
	execute_command('gcloud app deploy cron.yaml --project %s' % project_name)

if __name__ == '__main__':
	update_all_cron_jobs()