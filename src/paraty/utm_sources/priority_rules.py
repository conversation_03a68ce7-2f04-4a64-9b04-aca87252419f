"""
Reglas de prioridad para la categorización de UTM sources.
"""

from paraty.utm_sources.constants import *

def get_priority_rules(term_prefix="Paraty Metas "):
    """
    Devuelve la lista de reglas de prioridad para la categorización de UTM sources.
    
    Args:
        term_prefix: Prefijo para las etiquetas de término. Por defecto "Paraty Metas ".
        
    Returns:
        list: Lista de reglas ordenadas por prioridad.
    """
    priority_rules = [
        {
            "conditions": {
                "utm_medium": MEDIUM_ORGANIC,
                "utm_source_in": SOURCE_CALLCENTER,
                "utm_content": CONTENT_RING
            },
            "label": "Ring2Travel"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_ORGANIC,
                "utm_source_in": SOURCE_CALLCENTER,
                "utm_content": CONTENT_CALL
            },
            "label": "Call Center"
        },{
            "conditions": {
                "utm_medium_in": SOURCE_CALLCENTER,
                "utm_source_in": SOURCE_CALLCENTER,
            },
            "label": "Call Center"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_CPA,
                "utm_source": SOURCE_GOOGLE,
                "utm_campaign": CAMPAIGN_HPA,
                "utm_term_contains": TERM_PARATY_METASEARCH
            },
            "label": lambda e: f"GHA CPA - Paraty Metas ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_CPC,
                "utm_source": SOURCE_GOOGLE,
                "utm_campaign": CAMPAIGN_HPA,
                "utm_term_contains": TERM_PARATY_METASEARCH
            },
            "label": lambda e: f"GHA CPC - Paraty Metas ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_CPC,
                "utm_source": SOURCE_GOOGLE,
                "utm_campaign": CAMPAIGN_PPA,
                "utm_term_contains": TERM_PARATY_METASEARCH
            },
            "label": lambda e: f"GHA CPC PPA - Paraty Metas ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_GOOGLE,
                "utm_medium": MEDIUM_CPC,
                "utm_campaign_contains": CAMPAIGN_FBL
            },
            "special_check": lambda entry, reservation, extra_info: "ds_click_id" in extra_info,
            "label": lambda e: f"GHA - Derbysoft ({e.get('utm_source', '')}/{e.get('utm_medium', '')})",
            "special_case": {
                "hotel_code": "tafer-garza-blanca",
                "label_contains": "GHA - Derbysoft",
                "override_label": "GHA CPA - Paraty Metas (google/cpa)"
            }
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_GOOGLE,
                "utm_medium": MEDIUM_CPC,
                "utm_campaign_contains": CAMPAIGN_PPA
            },
            "label": lambda e: f"Hotel Ads Destination - Derbysoft ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_GOOGLE,
                "utm_medium": MEDIUM_CPC,
                "utm_campaign_contains": CAMPAIGN_HPALA
            },
            "label": lambda e: f"Google Search - DerbySoft ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_GOOGLE,
                "utm_medium": MEDIUM_CPC,
                "utm_campaign_contains": CAMPAIGN_HPAMAPS
            },
            "label": lambda e: f"Google Maps - Derbysoft ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_TRIVAGO,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": CAMPAIGN_TRIVAGO
            },
            "label": lambda e: f"Derbysoft Trivago ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_TRIVAGO,
                "utm_campaign_contains": CAMPAIGN_TRIVAGOSL
            },
            "label": lambda e: f"Derbysoft Trivago SL ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_TRIPADVISOR,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": CAMPAIGN_TRIPADVISOR
            },
            "label": lambda e: f"Derbysoft Tripadvisor ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_TRIPADVISOR,
                "utm_medium": MEDIUM_CPC,
                "utm_campaign_contains": CAMPAIGN_TRIPADVISORSP
            },
            "label": lambda e: f"Derbysoft Tripadvisor SP ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_BING,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": CAMPAIGN_BING_LOCAL
            },
            "label": lambda e: f"Derbysoft Bing Local ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_BING,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": CAMPAIGN_BING_MAPS
            },
            "label": lambda e: f"Derbysoft Bing Maps ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_KAYAK,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": SOURCE_KAYAK
            },
            "label": lambda e: f"Derbysoft Kayak ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_WEGO,
                "utm_medium_in": [MEDIUM_CPA, MEDIUM_CPC],
                "utm_campaign_contains": SOURCE_WEGO
            },
            "label": lambda e: f"Derbysoft Wego ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_term": TERM_DERBYSOFT,
                "utm_source": SOURCE_GOOGLE,
                "utm_medium": MEDIUM_FBL,
                "utm_campaign_contains": CAMPAIGN_FBL
            },
            "label": lambda e: f"Hotel Ads FBL - Derbysoft ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_PPA,
                "utm_source": SOURCE_GOOGLEHPA,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"GHA CPA - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_PPA,
                "utm_source": SOURCE_GOOGLEHPA,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"GHA CPC - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_LOCAL_UNIVERSAL,
                "utm_source": SOURCE_GOOGLEHPA,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Google Search - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_MAP_RESULTS,
                "utm_source": SOURCE_GOOGLEHPA,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Google Maps - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_TRIVAGO,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Trivago CPA - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_TRIVAGO,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Trivago CPC - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_SL_DEVICES,
                "utm_source": SOURCE_TRIVAGO,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Trivago SL CPA - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_SL_DEVICES,
                "utm_source": SOURCE_TRIVAGO,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Trivago SL CPC - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "special_check": lambda entry, reservation, extra_info: "trivago" in extra_info.get("original_referer", ""),
            "label": lambda e: f"Trivago"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_TRIPADVISOR,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Tripadvisor CPA - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_TRIPADVISOR,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Tripadvisor CPC - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_LOCAL_UNIVERSAL,
                "utm_source": SOURCE_BING,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Bing Local - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_MAP_RESULTS,
                "utm_source": SOURCE_BING,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Bing Maps - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "priority": 5.3,
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_KAYAK,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Kayak - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_SKYSCANNER,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Skyscanner - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_DEVICES,
                "utm_source": SOURCE_WEGO,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"Wego - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_FBL,
                "utm_source": SOURCE_GOOGLEHPA,
                "utm_campaign": CAMPAIGN_METAIO
            },
            "label": lambda e: f"GHA FBL - Wihp ({e.get('utm_source')}/{e.get('utm_medium')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_DISPLAY,
                "utm_source_in": SOURCE_DISPLAYS
            },
            "label": lambda e: e.get("utm_source").capitalize() + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_source": "Affilired"
            },
            "label": lambda e: "Affilired" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_DISPLAY
            },
            "label": "Display"
        },
        {
            "conditions": {
                "utm_source": SOURCE_ASKSUITE
            },
            "label": "Asksuite"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_PAID_SOCIAL,
                "utm_source_in": SOURCE_SOCIAL
            },
            "label": lambda e: f"Social Ads - {e.get('utm_source', '')} ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "special_check": lambda entry, reservation, extra_info: "fbclid" in extra_info.get("original_referer", ""),
            "label": lambda e: f"Social Ads - Meta"
        },
        {
            "special_check": lambda entry, reservation, extra_info: "epik=" in extra_info.get("original_referer", ""),
            "label": lambda e: f"Social Ads - Pinterest"
        },
        {
            "special_check": lambda entry, reservation, extra_info: "ttclid" in extra_info.get("original_referer", ""),
            "label": lambda e: f"Social Ads - TikTok"
        },
        {
            "conditions": {
                "utm_medium_in": [MEDIUM_CPC, MEDIUM_CPA],
                "utm_source": SOURCE_GOOGLE
            },
            "label": lambda e: "Google Ads" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "special_check": lambda entry, analytics_campaign_history: "gclid" in analytics_campaign_history,
            "label": lambda e: "Google Ads"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_SOCIAL,
                "utm_source_in": SOURCE_SOCIAL
            },
            "label": lambda e: e.get("utm_source").capitalize()
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_PAID_SOCIAL,
                "utm_source": SOURCE_TRIPADVISOR,
            },
            "label": lambda e: "Tripadvisor - Offer" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_SOCIAL,
                "utm_source": SOURCE_TRIPADVISOR,
            },
            "label": lambda e: "Tripadvisor - Offer" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium_in": MEDIUM_REFERRALS,
            },
            "label": lambda e: f"{e.get('utm_medium', '').replace('-', ' ').capitalize()}"
        },
        {
            "conditions": {
                "utm_source": SOURCE_GOOGLE,
                "utm_campaign": CAMPAIGN_FBL,
                "utm_medium": MEDIUM_ORGANIC
            },
            "label": lambda e: f"GHA FBL - Paraty Metas ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_EMAIL,
                "utm_source_in": [SOURCE_NEWSLETTER, SOURCE_CAMPAIGN]
            },
            "label": lambda e: e.get("utm_source") + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_ORGANIC,
                "utm_source": SOURCE_GOOGLE,
                "utm_campaign": "google-my-business"
            },
            "label": lambda e: "GMB" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_ORGANIC,
                "utm_source": "Apple",
                "utm_campaign": "apple-business-connect"
            },
            "label": lambda e: "Apple" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_ORGANIC,
                "utm_source": SOURCE_BING,
                "utm_campaign": "bing-places"
            },
            "label": lambda e: "Bing Places" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_EMAIL,
                "utm_source": SOURCE_RESCUE_SEEKER
            },
            "label": lambda e: "Rescue Email" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "conditions": {
                "utm_medium": MEDIUM_EMAIL,
                "utm_source": SOURCE_TRANSACTIONAL
            },
            "label": lambda e: "Email Transaccional" + f" ({e.get('utm_source', '')}/{e.get('utm_medium', '')})"
        },
        {
            "label": "Organic"
        }
    ]

    return priority_rules 