"""
Funciones de utilidad para el procesamiento de reglas UTM.
"""

import logging
import json

def matches_conditions(entry, conditions, history_str=""):
    """
    Verifica si una entrada cumple con todas las condiciones especificadas.
    Las comparaciones son case-insensitive.
    
    Args:
        entry: Diccionario con los datos de la entrada
        conditions: Diccionario con las condiciones a verificar
        history_str: Cadena completa de history para buscar en caso necesario
        
    Returns:
        bool: True si cumple todas las condiciones, False en caso contrario
    """
    # Si no hay condiciones, se considera que cumple (para casos especiales)
    if not conditions:
        return True
        
    for key, value in conditions.items():
        if key == "utm_term_contains":
            # Verificar si utm_term contiene el valor especificado (case-insensitive)
            if value.lower() not in entry.get("utm_term", "").lower():
                return False
        elif key == "utm_campaign_contains":
            # Verificar si utm_campaign contiene el valor especificado (case-insensitive)
            if value.lower() not in entry.get("utm_campaign", "").lower():
                return False
        elif key == "utm_source_in":
            # Verificar si utm_source está en una lista de valores (case-insensitive)
            entry_source = entry.get("utm_source", "").lower()
            if not any(v.lower() == entry_source for v in value):
                return False
        elif key == "utm_medium_in":
            # Verificar si utm_medium está en una lista de valores (case-insensitive)
            entry_medium = entry.get("utm_medium", "").lower()
            if not any(v.lower() == entry_medium for v in value):
                return False
        elif key in entry:
            # Verificar igualdad exacta para otros casos (case-insensitive)
            entry_value = entry[key]
            if isinstance(value, str) and isinstance(entry_value, str):
                if entry_value.lower() != value.lower():
                    return False
            else:
                if entry_value != value:
                    return False
        else:
            # Si la clave no existe en la entrada, la condición no se cumple
            return False
    
    return True


def process_campaign_entries(campaign_entries, priority_rules, analytics_campaigns_history, reservation, extra_info, hotel_code):
    """
    Procesa las entradas de campaña según las reglas de prioridad.
    
    Args:
        campaign_entries: Lista de diccionarios con información de campaña
        priority_rules: Lista de reglas de prioridad
        analytics_campaigns_history: Historia completa de campañas
        reservation: Diccionario con información de reserva
        extra_info: Diccionario con información extra
        hotel_code: Código del hotel
        
    Returns:
        str: Etiqueta generada según las reglas de prioridad, o None si no se encontró etiqueta
    """
    # Aplicamos las reglas en orden de prioridad para cada entrada
    for rule in priority_rules:
        for entry in campaign_entries:
            # Primero verificamos si hay un special_check
            if "special_check" in rule:
                special_check = rule["special_check"]
                special_check_passed = False
                
                try:
                    # Verificamos el número de parámetros que acepta la función
                    param_count = special_check.__code__.co_argcount
                    
                    if param_count == 1:
                        special_check_passed = special_check(entry)
                    elif param_count == 2:
                        special_check_passed = special_check(entry, analytics_campaigns_history)
                    elif param_count == 3:
                        special_check_passed = special_check(entry, reservation, extra_info)
                    
                    # Si la verificación especial falla, continuamos con la siguiente entrada
                    if not special_check_passed:
                        continue
                except Exception as e:
                    logging.warning(f"Error en special_check: {e}")
                    continue
            
            # Verificamos las condiciones regulares
            if matches_conditions(entry, rule.get("conditions", None), analytics_campaigns_history):
                # Obtenemos la etiqueta según la regla
                if callable(rule["label"]):
                    try:
                        # Verificamos el número de parámetros que acepta la función
                        param_count = rule["label"].__code__.co_argcount
                        
                        if param_count == 1:
                            label = rule["label"](entry)
                        elif param_count == 2:
                            label = rule["label"](entry, analytics_campaigns_history)
                    except Exception as e:
                        logging.warning(f"Error al generar etiqueta: {e}")
                        label = "Error al generar etiqueta"
                else:
                    label = rule["label"]
                
                # Verificamos si hay un caso especial para esta regla
                if "special_case" in rule:
                    special = rule["special_case"]
                    if hotel_code == special["hotel_code"] and special["label_contains"] in label:
                        label = special["override_label"]
                
                return label
    
    # Verificaciones globales para el historial completo (sin dependencia de entradas específicas)
    # Esto verifica reglas como "gclid en cualquier parte del historial"
    for rule in priority_rules:
        if "global_check" in rule:
            global_check = rule["global_check"]
            global_check_passed = False
            
            try:
                # Verificamos el número de parámetros que acepta la función
                param_count = global_check.__code__.co_argcount
                
                if param_count == 1:
                    global_check_passed = global_check(analytics_campaigns_history)
                elif param_count == 2:
                    global_check_passed = global_check(analytics_campaigns_history, reservation)
                elif param_count == 3:
                    global_check_passed = global_check(analytics_campaigns_history, reservation, extra_info)
                
                if global_check_passed:
                    if callable(rule["label"]):
                        param_count = rule["label"].__code__.co_argcount
                        if param_count == 0:
                            label = rule["label"]()
                        elif param_count == 1:
                            label = rule["label"](None)  # Pasamos None como entrada
                        elif param_count == 2:
                            label = rule["label"](None, analytics_campaigns_history)
                    else:
                        label = rule["label"]
                    
                    return label
            except Exception as e:
                logging.warning(f"Error en global_check: {e}")
                continue
    
    return None


def parse_campaign_history(analytics_campaigns_history):
    """
    Analiza y convierte el historial de campañas en una lista de entradas.
    
    Args:
        analytics_campaigns_history: Cadena con el historial de campañas
        
    Returns:
        list: Lista de diccionarios con información de campaña
    """
    campaign_entries = []
    if not analytics_campaigns_history:
        return campaign_entries

    for utms in analytics_campaigns_history.split(";"):
        if utms:  # Solo procesar entradas no vacías
            try:
                origins = json.loads(utms)
                campaign_entries.append(origins)
            except:
                logging.warning(f"Invalid JSON in analytics_campaigns_history: {utms}")
    
    return campaign_entries 