import datetime
import json
import logging

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_hotel_by_numeric_id, \
    get_all_valid_hotels

from paraty.utm_sources.constants import *
from paraty.utm_sources.priority_rules import get_priority_rules
from paraty.utm_sources.utils import matches_conditions, process_campaign_entries, parse_campaign_history


def get_utm_sources(request):
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type, X-Auth-Token',
        'Access-Control-Max-Age': '3600'
    }

    identifiers = request.values.get('identifiers')
    hotel_code = request.values.get('hotel_code')

    save_result_in_extra_info = request.values.get('save_result_in_extra_info')

    # call sending ONE hotel and several Identifiers by GET
    if hotel_code and identifiers:
        result = get_label_sources(hotel_code, identifiers, save_result_in_extra_info)
        return result, 200, headers

    # call sending HOtels and identifiers in JSON by POST
    result = get_labels_several_hotels(request, save_result_in_extra_info)
    return result, 200, headers


def get_labels_several_hotels(request, save_result_in_extra_info=False):
    identifiers_by_hotels = request.get_json(force=True)
    logging.info("get_utm_sources JSON RQ received!: %s", identifiers_by_hotels)

    results = []
    params = [(hotel_code, identifiers, results, save_result_in_extra_info) for hotel_code, identifiers in
              identifiers_by_hotels.items()]
    concurrency_utils.execute_in_parallel(get_label_sources, params)

    utm_results = {}
    for partial_res in results:
        utm_results.update(partial_res)

    return json.dumps(utm_results)


def get_label_sources(hotel_code, identifiers, all_results, save_result_in_extra_info=False):
    from_date = (datetime.datetime.now() - datetime.timedelta(days=15)).strftime("%Y-%m-%d")
    to_datetime = datetime.datetime.now().strftime("%Y-%m-%d") + " 23:59:59"
    utm_results = {}
    if identifiers and hotel_code:

        if hotel_code.isnumeric():
            hotel = get_hotel_by_numeric_id(hotel_code)

        else:
            hotel = get_hotel_by_application_id(hotel_code)

        identifiers_list = identifiers.split(",")

        if len(identifiers_list) == 1 and not identifiers_list[0] == "ALL":
            all_reservations = get_reservations_of_hotel(hotel, from_date, to_datetime,
                                                         reservation_id=identifiers_list[0],
                                                         include_modified_reservations=False)
            save_result_in_extra_info = True
        else:
            all_reservations = get_reservations_of_hotel(hotel, from_date, to_datetime,
                                                         include_modified_reservations=False)

        resertacions_to_save = []
        ids = []

        new_label_built = False

        for reservation in all_reservations:
            if (reservation.get("identifier") in identifiers) or identifiers == "ALL":

                extra_info = json.loads(reservation['extraInfo'])
                if False:
                    logging.info("hotel already precharged!")
                    utm_results[reservation.get("identifier")] = extra_info["utm_label_generated"]

                else:
                    new_label_built = True
                    utm_source = get_utm_label_from_extra_info(reservation, hotel_code)
                    utm_results[reservation.get("identifier")] = utm_source

                    logging.info("saving %s with utm label: %s", reservation.get("identifier"), utm_source)
                    extra_info["utm_label_generated"] = utm_source
                    reservation['extraInfo'] = json.dumps(extra_info)

                    # datastore_utils.id_to_entity_key(hotel_code, reservation.key)
                    ids.append(reservation.key.id)
                    resertacions_to_save.append(reservation)

        if new_label_built and save_result_in_extra_info:
            try:
                logging.info("New ids generated %s in hotel %s" % (ids, hotel_code))
                new_ids_gerated = datastore_communicator.save_multiple_entities("Reservation", ids,
                                                                                resertacions_to_save,
                                                                                hotel_code=hotel["applicationId"])
            except Exception as e:
                logging.info("ERROR EN HOTEl: %s", hotel_code)
                logging.info(e)

    all_results.append(utm_results)
    return json.dumps(utm_results)


def get_utm_label_from_extra_info(reservation, hotel_code):
    extra_info = json.loads(reservation['extraInfo'])

    utm_medium = extra_info.get("utm_medium", "")
    utm_source = extra_info.get("utm_source", "")
    utm_campaign = extra_info.get("utm_campaign", "")
    utm_term = extra_info.get("utm_term", "")
    utm_content = extra_info.get("utm_content", "")
    original_referer = extra_info.get("original_referer", "")
    ds_click_id = extra_info.get("ds_click_id", "")
    analytics_campaigns_history = extra_info.get("analytics_campaigns_history", "")
    
    current_entry = {
        "utm_medium": utm_medium,
        "utm_source": utm_source,
        "utm_campaign": utm_campaign,
        "utm_term": utm_term,
        "utm_content": utm_content
    }

    term_prefix = "Paraty Metas "

    if reservation.get("agent"):
        return f"Callcenter({reservation.get('agent')})"

    # Obtener las reglas de prioridad
    priority_rules = get_priority_rules(term_prefix)
    
    try:
        # Procesamos analytics_campaigns_history con la estructura de reglas de prioridad
        if analytics_campaigns_history:
            # Convertimos el historial en una lista de diccionarios para facilitar el procesamiento
            campaign_entries = parse_campaign_history(analytics_campaigns_history)
            
            # Añadir una nueva entrada con los UTM actuales
            campaign_entries.append(current_entry)
            
            # Procesamos las entradas según las reglas de prioridad
            label = process_campaign_entries(campaign_entries, priority_rules, analytics_campaigns_history, reservation, extra_info, hotel_code)
            if label:
                return label
    except:
        logging.warning(f"Error reading utm history for reservation {reservation.get('identifier')}")

    # Si no hay historial, procesamos los UTM actuales
    # Procesamos la entrada actual con las reglas de prioridad
    for rule in priority_rules:
        if "special_check" in rule:
            special_check = rule["special_check"]
            special_check_passed = False

            try:
                # Verificamos el número de parámetros que acepta la función
                param_count = special_check.__code__.co_argcount

                if param_count == 1:
                    special_check_passed = special_check(current_entry)
                elif param_count == 2:
                    special_check_passed = special_check(current_entry, analytics_campaigns_history)
                elif param_count == 3:
                    special_check_passed = special_check(current_entry, reservation, extra_info)

                # Si la verificación especial falla, continuamos con la siguiente entrada
                if not special_check_passed:
                    continue
            except Exception as e:
                logging.warning(f"Error en special_check: {e}")
                continue

        if matches_conditions(current_entry, rule.get("conditions", None)):
            if callable(rule["label"]):
                try:
                    label = rule["label"](current_entry)
                except Exception as e:
                    logging.warning(f"Error al generar etiqueta: {e}")
                    label = "Error al generar etiqueta"
            else:
                label = rule["label"]
            
            # Verificamos si hay un caso especial para esta regla
            if "special_case" in rule:
                special = rule["special_case"]
                if hotel_code == special["hotel_code"] and special["label_contains"] in label:
                    label = special["override_label"]
            
            return label

    # Si no se encontró ninguna regla que coincida, usamos el método antiguo
    return get_label_old_way(reservation)


def get_label_old_way(reservation):
    # (this code was taken from manager in java)
    real_canal = ""
    extra_info = json.loads(reservation['extraInfo'])

    source_txt = reservation.get("source", "")

    if source_txt is None:
        source_txt = ""

    if extra_info.get("fre_booking_link"):
        real_canal = "FBL"

    if extra_info.get("utm_campaign") and source_txt:
        utm_campaign = extra_info.get("utm_campaign").replace('"', "")
        if utm_campaign.lower() == "MetaI/O".lower():
            real_canal = "Paraty Metas " + source_txt

    if not real_canal:

        real_canal = "Organic"

        if source_txt == "Callcenter" and reservation.get("agent"):
            source_txt += "(" + reservation.get("agent") + ")"
            real_canal = source_txt

    return real_canal


def _test_get_labels_several_hotels(identifiers_by_hotels):
    # identifiers_by_hotels = request.get_json(force=True)
    logging.info("get_utm_sources JSON RQ received!: %s", identifiers_by_hotels)

    save_result_in_extra_info = False

    results = []
    params = [(hotel_code, identifiers, results, save_result_in_extra_info) for hotel_code, identifiers in
              identifiers_by_hotels.items()]
    concurrency_utils.execute_in_parallel(get_label_sources, params)

    utm_results = {}
    for partial_res in results:
        utm_results.update(partial_res)

    return json.dumps(utm_results)


def _excetute_presave_utm_labels(hotel_code, identifiers="ALL"):
    results = []
    res1 = get_label_sources(hotel_code, identifiers, results, save_result_in_extra_info=True)
    print(res1)


if __name__ == "__main__":

    results = []

    # hotel_code = "omnibees3"
    # hotel_code = "4792983878434816"
    # identifiers = "6634D9ABE"
    # res1 = get_label_sources(hotel_code, identifiers, results, save_result_in_extra_info=True)
    # print(res1)

    # # best-ballena, best-sandiego
    # hotel_filter = lambda x: ('parkroyal' in x['applicationId']) or ('tafer' in x['applicationId'])
    # all_hotels = get_all_valid_hotels()
    # valid_hotels = list(filter(hotel_filter, all_hotels))
    #
    # all_reservations = {}
    # for hotel in valid_hotels:
    _excetute_presave_utm_labels("best-roquetas", "RDB8B8A7DB")

    print("OK")
# identifiers_by_hotels = {"best-ballena":"R4D8BD6614"}
# res2 = _test_get_labels_several_hotels(identifiers_by_hotels)
# print(res2)
