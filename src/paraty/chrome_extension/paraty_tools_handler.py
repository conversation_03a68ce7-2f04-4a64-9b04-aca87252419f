from flask import request

from paraty.chrome_extension.paraty_tools_utils import build_request_response, check_user_credentials, \
    get_all_hotels_chrome_extension


def handle_paraty_tools_chrome_extension_handler():
    target_action = request.values.get('action')

    available_actions = {
        'check_user': check_user_credentials,
        'get_all_hotels': get_all_hotels_chrome_extension
    }

    if target_action in available_actions:
        return available_actions[target_action]()

    return build_request_response(False, "Not implemented yet")
