import json

from flask import request

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels


def build_request_response(status, message=None, data=None):
    return json.dumps({
        'status': bool(status),
        'message': message,
        'data': data
    })


def check_user_credentials():
    username = request.values.get('username')
    password = request.values.get('password')

    if username and password:
        return build_request_response(True, "User credentials are valid")


def get_all_hotels_chrome_extension():
    all_hotels = get_all_hotels()
    return build_request_response(True, data=all_hotels)
