import gspread
import pygsheets
from google.oauth2.service_account import Credentials
import pandas as pd
import ast
import os
import logging

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty.gdrive.constants_excels import *


current_dir = os.path.dirname(os.path.abspath(__file__))
CREDENTIALS_JSON = os.path.join(current_dir, "client_secrets.json")

gc = gspread.service_account(filename=CREDENTIALS_JSON)

hotels = get_all_valid_hotels()
def _compare_values_with_new_rows(values, rows):

    """
    This function overwrites excel columns that are handwritten in new rows that are going to be inserted.
    This way everytime excel updates data, we don't lose these columns (description, validation, incompatible with)
    """

    dict_list2 = {sublist[0]: sublist[1:4] for sublist in values}

    for sublist1 in rows:
        if sublist1[0] in dict_list2:
            sublist1[1:4] = dict_list2[sublist1[0]]

def _get_first_element(list):
    return int(eval(list)[0])


def fill_all_hotels_adapters_excel():
    spreadsheet_adapter = gc.open_by_key(XML_ADAPTERS_EXCEL_ID)
    worksheet_adapter = spreadsheet_adapter.worksheet("Sheet1")
    
    spreadsheet_tpv = gc.open_by_key(XML_TPVS_EXCEL_ID)
    worksheet_tpv = spreadsheet_tpv.worksheet("Sheet1")
    
    rows_adapter = []
    rows_tpvs = []
    
    for hotel in hotels:
        try:
            logging.info("Getting xml configs for hotel %s" % hotel["applicationId"])
            integrations = get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel["applicationId"])
            
            for integration in integrations:
                if integration.get("name").startswith("_") or integration.get("name").upper().startswith("COPIA") or integration.get("name").upper().startswith(
                        "HOTEL MANAGER"):
                    continue
                if integration.get("downloadBooking"):
                    rows_adapter.append([hotel.get("name"), integration.get("name", "")])
                configurations = integration.get("configurations")
                for config in configurations:
                    if "merchant_url" in config:
                        rows_tpvs.append([hotel.get("name"), integration.get("name", "")])
        
        
        
        except Exception as e:
            logging.info(e)
    
    rows_adapter = sorted(rows_adapter, key=lambda x: x[0])
    
    worksheet_adapter.delete_rows(3, 10000)
    worksheet_adapter.update(f"2:2", [["", ""]])
    worksheet_adapter.insert_rows(rows_adapter, 2)
    
    rows_tpvs = sorted(rows_tpvs, key=lambda x: x[0])
    
    worksheet_tpv.delete_rows(3, 10000)
    worksheet_tpv.update(f"2:2", [["", ""]])
    worksheet_tpv.insert_rows(rows_tpvs, 2)
    return "OK"


def fill_advanced_configs_excel():
    spreadsheet = gc.open_by_key(ADVANCED_CONFIG_EXCEL_ID)

    worksheet = spreadsheet.worksheet("Sheet1")

    rows = [] #every time we update excel we rebuild all rows, so we update hotel list count, in addition we only write once for quota limit
    values = worksheet.get_values()

    for hotel in hotels:
        try:
            logging.info("Getting advanced configs for hotel %s" % hotel["applicationId"])
            configs = get_using_entity_and_params('ConfigurationProperty', hotel_code=hotel["applicationId"])

            for config in configs:

                if any(config.get("mainKey", "").startswith(option) or config.get("mainKey", "").endswith(option) for option in NAMES_FILTERS):
                    continue

                #check if config has already been inserted
                row_index = next((i for i, row in enumerate(rows) if row[0] == config.get("mainKey", "")), None)

                if row_index is not None:
                    hotels_list = ast.literal_eval(rows[row_index][4])
                    if hotel["name"] not in hotels_list:
                        hotels_list[0] += 1 # we increase hotel count everytime
                        if len(hotels_list) == 6 and not "..." in hotels_list:
                            hotels_list.append("...")
                        elif len(hotels_list) < 6: # but we only append hotel until 5 examples
                            hotels_list.append(hotel["name"])
                        rows[row_index][4] = str(hotels_list)
                else: #if config has not been inserted yet, we append it, with handwritten columns empty
                    rows.append([config.get("mainKey", ""), "", "", "", str([1, hotel['name']])])
        except Exception as e:
            logging.info(e)

    _compare_values_with_new_rows(values, rows)

    rows = sorted(rows, key=lambda x: _get_first_element(x[4]), reverse=True) # sort config by hotel count

    worksheet.delete_rows(3, 10000) # delete all rows except headers and second one, if we delete them all we cant insert later
    worksheet.update(f"2:2", [["", "", "", "", ""]]) #empty second row
    worksheet.insert_rows(rows, 2)
    return "OK"


def fill_web_configs_excel():
    spreadsheet = gc.open_by_key(WEB_CONFIG_EXCEL_ID)

    worksheet = spreadsheet.worksheet("Sheet1")

    rows = []
    values = worksheet.get_values()

    for hotel in hotels:
        try:
            logging.info("Getting web configs for hotel %s" % hotel["applicationId"])
            configs = get_using_entity_and_params('WebConfiguration', hotel_code=hotel["applicationId"])

            for config in configs:

                if any(config.get("name", "").startswith(option) or config.get("name", "").endswith(option) for option in NAMES_FILTERS):
                    continue

                row_index = next((i for i, row in enumerate(rows) if row[0] == config.get("name", "")), None)

                if row_index is not None:
                    hotels_list = ast.literal_eval(rows[row_index][4])
                    if hotel["name"] not in hotels_list:
                        hotels_list[0] += 1
                        if len(hotels_list) == 6 and not "..." in hotels_list:
                            hotels_list.append("...")
                        elif len(hotels_list) < 6:
                            hotels_list.append(hotel["name"])
                        rows[row_index][4] = str(hotels_list)
                else:
                    rows.append([config.get("name", ""), "", "", "", str([1, hotel['name']])])
        except Exception as e:
            logging.info(e)

    _compare_values_with_new_rows(values, rows)

    rows = sorted(rows, key=lambda x: _get_first_element(x[4]), reverse=True)

    worksheet.delete_rows(3, 10000)
    worksheet.update(f"2:2", [["", "", "", "", ""]])
    worksheet.insert_rows(rows, 2)

    return "OK"

def fill_xml_configs_excel():
    spreadsheet = gc.open_by_key(XML_CONFIG_EXCEL_ID)

    worksheet = spreadsheet.worksheet("Sheet1")

    rows = []
    values = worksheet.get_values()

    for hotel in hotels:
        try:
            logging.info("Getting xml configs for hotel %s" % hotel["applicationId"])
            configs = get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel["applicationId"])

            for config in configs:

                if any(config.get("name", "").startswith(option) or config.get("name", "").endswith(option) for option in NAMES_FILTERS):
                    continue

                row_index = next((i for i, row in enumerate(rows) if row[0] == config.get("name", "")), None)

                if row_index is not None:
                    hotels_list = ast.literal_eval(rows[row_index][4])
                    if hotel["name"] not in hotels_list:
                        hotels_list[0] += 1
                        if len(hotels_list) == 6 and not "..." in hotels_list:
                            hotels_list.append("...")
                        elif len(hotels_list) < 6:
                            hotels_list.append(hotel["name"])
                        rows[row_index][4] = str(hotels_list)
                else:
                    rows.append([config.get("name", ""), "", "", "", str([1, hotel['name']])])
        except Exception as e:
            logging.info(e)

    _compare_values_with_new_rows(values, rows)

    rows = sorted(rows, key=lambda x: _get_first_element(x[4]), reverse=True)

    worksheet.delete_rows(3, 10000)
    worksheet.update(f"2:2", [["", "", "", "", ""]])
    worksheet.insert_rows(rows, 2)

    return "OK"


def fill_users_configs_excel():
    spreadsheet = gc.open_by_key(USERS_CONFIG_EXCEL_ID)

    worksheet = spreadsheet.worksheet("Sheet1")

    rows = []
    values = worksheet.get_values()

    users = get_using_entity_and_params('ParatyUser', hotel_code="admin-hotel:")

    try:
        for user in users:
            configs = user.get('configurationMap', [])

            if configs:
                for config in configs:
                    config_name = config.split(" @@ ")[0]

                    if any(config_name.startswith(option) or config_name.endswith(option) for option in NAMES_FILTERS):
                        continue

                    row_index = next((i for i, row in enumerate(rows) if row[0] == config_name), None)

                    if row_index is not None:
                        users_list = ast.literal_eval(rows[row_index][4])
                        if user.get("name", "") not in users_list:
                            users_list[0] += 1
                            if len(users_list) == 6 and not "..." in users_list:
                                users_list.append("...")
                            elif len(users_list) < 6:
                                users_list.append(user["name"])
                            rows[row_index][4] = str(users_list)
                    else:
                        rows.append([config_name, "", "", "", str([1, user['name']])])
    except Exception as e:
        logging.warning(e)

    _compare_values_with_new_rows(values, rows)

    rows = sorted(rows, key=lambda x: _get_first_element(x[4]), reverse=True)

    worksheet.delete_rows(3, 10000)
    worksheet.update(f"2:2", [["", "", "", "", ""]])
    worksheet.insert_rows(rows, 2)

    return "OK"

def fill_users_permissions_excel():
    spreadsheet = gc.open_by_key(USERS_PERMISSIONS_EXCEL_ID)

    worksheet = spreadsheet.worksheet("Sheet1")

    rows = []
    values = worksheet.get_values()

    users = get_using_entity_and_params('ParatyUser', hotel_code="admin-hotel:")

    try:
        for user in users:
            permissions = user.get('permission', "")

            if permissions:
                permissions = permissions.split(";")
                for permission in permissions:

                    row_index = next((i for i, row in enumerate(rows) if row[0] == permission), None)

                    if row_index is not None:
                        users_list = ast.literal_eval(rows[row_index][4])
                        if user.get("name", "") not in users_list:
                            users_list[0] += 1
                            if len(users_list) == 6 and not "..." in users_list:
                                users_list.append("...")
                            elif len(users_list) < 6:
                                users_list.append(user["name"])
                            rows[row_index][4] = str(users_list)
                    else:
                        rows.append([permission, "", "", "", str([1, user['name']])])
    except Exception as e:
        logging.warning(e)

    _compare_values_with_new_rows(values, rows)

    rows = sorted(rows, key=lambda x: _get_first_element(x[4]), reverse=True)

    worksheet.delete_rows(3, 10000)
    worksheet.update(f"2:2", [["", "", "", "", ""]])
    worksheet.insert_rows(rows, 2)

    return "OK"


def get_first_word(name):
    words = name.split()
    if words:
        return words[0]
    return ""


def is_in_tpvs_list(tpv_name):
    for tpv in TPVS_LIST:
        if tpv.lower() == tpv_name.lower():
            return True
    return False


if __name__ == "__main__":
    fill_all_hotels_tpvs_excel()
