import logging

from paraty_commons_3 import queue_utils
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache


# Note that we suppose that integrations don't need this information to be exact
# All allow at least 2 tasks for the same hotel (one might be running)
# and since each task has a 60 seconds delay, it is completely safe to cache this for 10 seconds
# 5 seconds is probably enough to avoid running into quota limits

@timed_cache(seconds=5)
def get_list_tasks_cached(project, location, queue_name):
    result = queue_utils.list_tasks(queue_name, project=project, location=location)
    return ",".join([x.name for x in result])


def list_tasks_in_queue(request):

    queue_name = request.values.get('queue_name')
    project = request.values.get('project')
    location = request.values.get('location')

    logging.info(f"Listing tasks in queue: {queue_name} at {location} in {project}")

    return get_list_tasks_cached(project, location, queue_name)

