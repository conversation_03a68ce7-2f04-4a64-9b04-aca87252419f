import json
import requests

from paraty.utilities.tax.tax_constants import CONFIG_SEPARATOR, COUNTRY_LOCATION_CUSTOM_TAX, \
    ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT, ACCOMODATION_TAX, NOT_INCLUDE_ACCOMODATION_TAX, HOTEL_COUNTRY_LOCATION
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from collections import OrderedDict

import logging
import datetime
from dateutil.parser import parse
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item, get_pictures_for_entity, \
    get_web_page_properties_by_entity_key, \
    get_hotel_advance_config_value, get_hotel_web_config_item, get_web_section, get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


def parse_currency_response(content, currency_to):
    response_json = json.loads(content)
    currency_date = response_json.get('time_last_updated', '')

    if currency_date:
        currency_date = datetime.datetime.fromtimestamp(currency_date).strftime("%d/%m/%Y %H:%M:%S")
        logging.info("Date currency: %s, %s", response_json.get('time_last_updated'), currency_date)

    rates = response_json.get('rates', '')

    if rates:
        rate = rates.get(currency_to, '')
        if rate:
            return 1 / float(rate)

    return 1


@managers_cache(hotel_code_provider=lambda f, a, k:'', ttl_seconds=6 * 3600, background_refresh=False)
def get_currency_from_paraty_adapter(currency_from, currency_to):
    try:

        logging.info("Currency conversion from paratytech-adapter")

        currency_from = currency_from.upper()
        url = f'https://paratytech-adapter.appspot.com/currency/?currency_from={currency_from}'
        response = requests.get(url, timeout=2)

        if response and response.content:
            return parse_currency_response(response.content, currency_to)

        return 1

    except Exception as e:
        logging.warning("Cannot get exchange rate from exchangerate: %s", e)
        return 1


def _get_multicurrency_rates(hotel, rate):
    integration_configuration = get_integration_configuration_of_hotel(hotel, "MULTICURRENCY RATES")
    if integration_configuration:
        integration_configuration = integration_configuration[0]
        configurations = {x.split(CONFIG_SEPARATOR)[0]: x.split(CONFIG_SEPARATOR)[1] for x in
                          integration_configuration.get('rateMap', {'item': []})}
        if rate in configurations:
            return configurations[rate]
    return ""


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='ConfigurationProperty,IntegrationConfiguration', ttl_seconds=6 * 3600, background_refresh=False)
def get_property_currency(property_id, rate):
    try:
        logging.info(f"[Accommodation tax] Getting property currency for {property_id}")
        hotel = get_hotel_by_application_id(property_id)

        default_currency = _get_multicurrency_rates(hotel, rate)
        if default_currency:
            return default_currency

        default_currency = get_hotel_advance_config_value(hotel['applicationId'], 'Base Price Currency')
        if default_currency:
            return default_currency

    except Exception as e:
        logging.warning("Exception trying to retrieve the default currency: %s", e)
        return 'EUR'

    return 'EUR'


def get_country_location_custom_tax(hotel):
    country_location_custom_tax = get_hotel_advance_config_value(hotel['applicationId'], COUNTRY_LOCATION_CUSTOM_TAX)
    if country_location_custom_tax:
        country_location_custom_tax = country_location_custom_tax
        country_location_custom_tax = country_location_custom_tax.replace("%", "")
        country_location_custom_tax = float(float(country_location_custom_tax) + 100) / 100
    return country_location_custom_tax


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='ConfigurationProperty,IntegrationConfiguration,WebConfiguration', ttl_seconds=24 * 3600, background_refresh=False)
def _get_params_calculate_accommodation_tax(hotel_code, tourism_tax_optional):
    accommodation_tax_property = get_hotel_advance_config_value(hotel_code,
                                                                ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT)
    accommodation_tax_extended_config_result = get_hotel_web_config_item(hotel_code, ACCOMODATION_TAX)
    accommodation_not_included = get_hotel_advance_config_value(hotel_code, NOT_INCLUDE_ACCOMODATION_TAX)

    if not accommodation_not_included and not accommodation_tax_extended_config_result and not accommodation_tax_property:
        accommodation_tax_property = tourism_tax_optional

    return accommodation_not_included, accommodation_tax_extended_config_result, accommodation_tax_property


def _get_accommodation_tax(actual_price, accommodation_tax_property, accommodation_not_included):
    if not accommodation_tax_property or not actual_price:
        return None
    if len(accommodation_tax_property) == 0:
        return None

    accommodation_tax_property = accommodation_tax_property
    if ";" in accommodation_tax_property:
        accommodation_tax_value = accommodation_tax_property.split(";")[0]

    else:
        accommodation_tax_value = accommodation_tax_property

    if "%" in accommodation_tax_value:
        accommodation_tax_percentage = float(accommodation_tax_value.replace("%", ""))

        if accommodation_not_included:
            accommodation_tax_value = actual_price * accommodation_tax_percentage / 100
        else:
            accommodation_tax_percentage = (accommodation_tax_percentage / 100) + 1
            accommodation_tax_value = actual_price - (actual_price / accommodation_tax_percentage)

        accommodation_tax_value = round(accommodation_tax_value, 2)

    return accommodation_tax_value


def _get_period_tax_for_date(date, night_number, periods, default_tax):
    """Search for date in tax periods and apply the biggest tax. If not found - default tax will be applied"""
    tax = -1
    date_dt = date.date()
    for period in periods:
        if period['start_date'] <= date_dt <= period['end_date'] and period['tax'] > tax:
            if period['start_night'] <= night_number <= period['end_night']:
                tax = period['tax']

    return default_tax if tax == -1 else tax


# @timed_cache(days=7)
def retrieve_grouped_tax_by_period(hotel, periods_section, start_date, nights, increment):
    logging.info(f"[Accommodation tax] Retrieving grouped tax by period. Start date: {start_date}, Nights: {nights}, Increment: {increment}")
    is_percentage = False
    base_price = 0
    search_dates = get_search_dates_list(start_date, nights)

    raw_periods = get_pictures_from_section_name(hotel, periods_section)
    logging.info(f"[Accommodation tax] Raw periods: {raw_periods}")
    periods = []
    if not raw_periods:
        return {}
    for period in raw_periods:
        properties = _get_properties_for_entity(hotel.get("applicationId"), period.get('key', False))
        period.update(properties)
        if period.get('title') and period.get('start_date') and period.get('end_date'):
            if is_percentage:
                tax_value = float(period.get('title', '0').split('%')[0]) / 100 * float(base_price)
            else:
                tax_value = float(period.get('title', '0').split('%')[0])
            periods.append({
                'tax': tax_value,
                'start_date': datetime.datetime.strptime(period.get('start_date'), "%d/%m/%Y").date(),
                'end_date': datetime.datetime.strptime(period.get('end_date'), "%d/%m/%Y").date(),
                'start_night': int(period.get('start_night', 1)),
                'end_night': int(period.get('end_night', 999))
            })

    # Group and count dates by tax and nights
    logging.info(f"[Accommodation tax] Grouping dates by tax and nights. search_dates: {search_dates}")
    grouped_tax_days = {}
    for date_night_number, date in enumerate(search_dates, start=1):
        date_tax = _get_period_tax_for_date(date, date_night_number, periods, increment)
        if grouped_tax_days.get(date_tax):
            grouped_tax_days[date_tax] += 1
        else:
            grouped_tax_days[date_tax] = 1

    grouped_tax_days = OrderedDict(sorted(grouped_tax_days.items()))
    return grouped_tax_days


def get_converted_tax_by_actual_currency(actual_currency, base_currency, increment):
    exchange_rate = get_currency_from_paraty_adapter(base_currency, actual_currency)
    if exchange_rate:
        exchange_rate = float(exchange_rate)
        increment = float(increment) / exchange_rate
    return increment


def calculate_tax_by_period(grouped_tax_days, multiplier, increment_total=0):
    for tax_value, days in grouped_tax_days.items():
        increment_total += tax_value * days * multiplier

    return increment_total


def _get_range_tax_for_price(price, price_ranges, default_tax):
    """Search for price in range tax and apply the biggest tax. If not found - default tax will be applied"""
    tax = -1
    for price_range in price_ranges:
        if price_range['start_range'] <= price <= price_range['end_range'] and price_range['tax'] > tax:
            tax = price_range['tax']

    return default_tax if tax == -1 else tax


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='WebPageProperty', ttl_seconds=6 * 3600, background_refresh=False)
def _get_properties_for_entity(hotel_code, entity_key):
    properties_result = {}
    properties = get_web_page_properties_by_entity_key(hotel_code, "SPANISH", entity_key)
    for item in properties:
        properties_result.update({item.get("mainKey"): item.get("value")})
    return properties_result


def get_search_dates_list(start_date, nights):
    start_date_obj = parse_date(start_date)
    return [start_date_obj + datetime.timedelta(days=x) for x in range(nights)]


def parse_date(date_str):
    if isinstance(date_str, datetime.datetime):
        return date_str

    try:
        return parse(date_str)
    except Exception as e:
        raise ValueError(f"Could not parse date {date_str}. Error: {e}")


# Taxes are calculated in the same way as they are calculated in the reservation process
def get_accommodation_total_increment(hotel, actual_price, current_result, country_location_custom_tax, amount_after_taxes, tourism_tax_optional):
    increment_total = 0
    accommodation_not_included, accommodation_tax_extended_config, accommodation_tax_property = _get_params_calculate_accommodation_tax(hotel.get("applicationId"), tourism_tax_optional)
    accommodation_tax_value = _get_accommodation_tax(actual_price, accommodation_tax_property, accommodation_not_included)
    hotel_country_location = get_hotel_advance_config_item(hotel, HOTEL_COUNTRY_LOCATION)
    if accommodation_tax_value:
        logging.info(f"[Accommodation tax] accommodation_tax_value: {accommodation_tax_value}")
        increment = float(accommodation_tax_value)
        by_room = True if accommodation_tax_extended_config.get('by_room') else False
        ignore_persons = True if accommodation_tax_extended_config.get('ignore_persons') else False
        persons = int(current_result.get("num_adults"))
        nights = int(current_result.get("num_nights"))
        currency = current_result.get("currency")
        base_currency = get_property_currency(hotel.get("applicationId"), "")

        # We have to send to always the price converted to the current currency, otherwise we have problems with the geo-localized rates
        if hotel_country_location:
            if base_currency and base_currency != currency:
                increment = get_converted_tax_by_actual_currency(currency, base_currency, increment)

        num_rooms = 1

        if int(accommodation_tax_extended_config.get('max_days', 999)) < nights:
            nights = int(accommodation_tax_extended_config['max_days'])
        periods_section = accommodation_tax_extended_config.get('periods')
        price_ranges_section = accommodation_tax_extended_config.get('price_ranges')

        if periods_section:
            grouped_tax_days = retrieve_grouped_tax_by_period(hotel, periods_section, current_result.get("start_date"), nights, increment)

            # Calculate total tax
            if by_room:
                multiplier = num_rooms
            else:
                multiplier = persons

            increment_total += calculate_tax_by_period(grouped_tax_days, multiplier)
            if not increment_total:
                return 0

        elif price_ranges_section and actual_price:
            increment_total = get_increment_from_price_ranges_section(actual_price, hotel, increment, nights, persons, price_ranges_section)
        else:

            if by_room:
                increment_total = increment * nights * num_rooms
            elif ignore_persons:
                increment_total = increment * nights
            else:
                increment_total = increment * nights * persons

    # logging.info("incrementing total because of ACCOMMODATION_TAX_INCREMENT_BY_PAX_NIGHT. increment: %s", increment_total)

    elif country_location_custom_tax:
        logging.info(f"[Accommodation tax] country_location_custom_tax: {country_location_custom_tax}")
        currency = current_result.get("currency")
        base_currency = get_property_currency(hotel.get("applicationId"), None)

        # We have to send to always the price converted to the current currency, otherwise we have problems with the geo-localized rates
        if hotel_country_location:
            increment_total = (amount_after_taxes * country_location_custom_tax) - amount_after_taxes
        # increment_total = get_converted_tax_by_actual_currency(currency, base_currency, increment)

    logging.info(f"[Accommodation tax] Final increment total: {increment_total}")
    return increment_total


def get_increment_from_price_ranges_section(actual_price, hotel, increment, nights, persons, price_ranges_section):
    logging.info(f"[Accommodation tax] Getting increment from price ranges section. Actual price: {actual_price}, Nights: {nights}, Persons: {persons}, Price ranges section: {price_ranges_section}")
    price_ranges_section = get_pictures_from_section_name(hotel, price_ranges_section)
    price_ranges = []
    for price_range in price_ranges_section:
        price_range.update(get_pictures_for_entity(hotel.get("applicationId"), price_range.get('key', False), "es"))
        if price_range.get('title') and price_range.get('start_range') and price_range.get('end_range'):
            price_ranges.append({
                'tax': float(price_range.get('title')),
                'start_range': float(price_range.get('start_range')),
                'end_range': float(price_range.get('end_range'))
            })

    logging.info(f"[Accommodation tax] Price ranges: {price_ranges}")
    range_tax_price = _get_range_tax_for_price(actual_price, price_ranges, increment)
    increment_total = range_tax_price * nights * persons
    return increment_total


@managers_cache(hotel_code_provider=lambda f, a, k: a[0]['applicationId'], entities='WebSection, Picture', ttl_seconds=6 * 3600, background_refresh=False)
def get_pictures_from_section_name(hotel, section_name):
    entity_key = get_web_section(hotel, section_name, "es", set_languages=False)
    main_key = id_to_entity_key(hotel.get("applicationId"), entity_key.key)
    pictures_entity = get_pictures_for_entity(hotel.get("applicationId"), main_key, "", False)
    return pictures_entity


def get_total_accomodation_tax(params):
    logging.info(f"[Accommodation tax] Calculating total accommodation tax. Params: {params}")
    hotel_code = params.get("hotel_code")
    hotel = get_hotel_by_application_id(hotel_code)
    prices_total = params.get("prices_total")
    current_result = params.get("current_result")
    amount_after_taxes = params.get("amount_after_taxes")
    country_location_custom_tax = params.get("country_location_custom_tax")
    tourism_tax_optional = params.get("tourism_tax_optional")

    increment_total = get_accommodation_total_increment(hotel, prices_total, current_result,
                                                        country_location_custom_tax, amount_after_taxes,
                                                        tourism_tax_optional)
    return increment_total


# def get_total_fee(current_result, prices_total, amount_after_taxes):
#     try:
#         hotel_code = current_result.get("hotel_code")
#         hotel = get_hotel_by_application_id(hotel_code)
#         tourism_tax_optional = _get_properties_tripadvisor(hotel, TOURISM_TAX)
#         not_show_tourism_tax = _get_properties_tripadvisor(hotel, NO_SHOW_TOURISM_TAX)
#         country_location_custom_tax = get_country_location_custom_tax(hotel)
#         if not_show_tourism_tax:
#             tourism_tax = 0.0
#         else:
#             tourism_tax = get_accommodation_total_increment(hotel, prices_total, current_result,
#                                                             country_location_custom_tax, amount_after_taxes,
#                                                             tourism_tax_optional)
#     except Exception as e:
#         return "0.0"
#
#     if tourism_tax:
#         return "{0:.2f}".format(float(tourism_tax))
#
#     return "0.0"
