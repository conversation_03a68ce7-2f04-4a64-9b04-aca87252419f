import os, requests, base64, logging
from slack_sdk import WebClient

def send_message(message, custom_token=None, target_chat=None, image=None, image_name=None):
    logging.info("Sending normal message")
    bot_token = custom_token or os.getenv('TOKEN')
    uploaded_websites_id = target_chat or os.getenv('CHAT_ID')

    if not target_chat:
        target_chat = uploaded_websites_id

    if message and 'http' in message and ('web-seeker' in message or '.run.app' in message):
        message = message.replace('http', 'avoid_interpreter')

    target_body = {
        'token': bot_token,
        'channel': target_chat,
        'text': message,
        'as_user': True
    }

    logging.info("Sent message to slack")
    response = requests.post('https://slack.com/api/chat.postMessage', target_body)
    logging.info(response.text)

    return response


def send_image(image_content, custom_token=None, custom_chat=None, image_name=None):
    logging.info("Sending image message")
    bot_token = custom_token or os.getenv('TOKEN')
    target_chat = custom_chat or os.getenv('CHAT_ID')

    logging.info(f"Using bot token: {bot_token}")
    logging.info(f"Using chat id: {target_chat}")

    client = WebClient(bot_token)

    image_binary = base64.b64decode(image_content)

    response = client.files_upload(
        channels=target_chat,
        file=image_binary,
        filename=image_name or "imagen.jpg",
        title=image_name or "Imagen enviada desde Python",
    )

    logging.info("Image sent")
    logging.info(response)

    return response
