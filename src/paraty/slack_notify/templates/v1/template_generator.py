import copy

from paraty.slack_notify.templates.v1.template import template as slack_message
from paraty.slack_notify.templates.v1.slack_services import upload_image

def build_message_template(header_text, hotel_name, hotel_code, bold_content, italic_content=None,
                            url_block=None):
    message_template = copy.deepcopy(slack_message)
    message_template[0].get('text')['text'] = header_text
    message_template[1].get('elements')[0].get('elements')[0]['text'] = hotel_name
    message_template[1].get('elements')[0].get('elements')[2]['text'] = hotel_code
    message_template[3].get('elements')[0].get('elements')[0]['text'] = bold_content

    if italic_content:
        message_template[3].get('elements')[0].get('elements')[2]['text'] = italic_content
    else:
        message_template[3].get('elements')[0].get('elements').pop(2)

    if url_block:
        if 'http' in url_block and ('web-seeker' in url_block or '.runa.pp' in url_block):
            url_block.replace('http', 'avoid_interpreter')
        message_template[4].get('accessory')['url'] = url_block
    else:
        if len(message_template) > 4:
            message_template.pop(4)
        elif len(message_template) > 3:
            message_template.pop(3)

    return message_template

def add_image(bot_token, image, image_name, message):
    image_url = upload_image(bot_token, image, image_name)
    if image_url:
        message.append(
            {
                "type": "image",
                "slack_file": {
                    "id": image_url
                },
                "alt_text": image_name
            })
