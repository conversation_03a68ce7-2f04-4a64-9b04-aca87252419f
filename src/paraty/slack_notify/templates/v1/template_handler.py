import os, logging
from paraty.slack_notify.templates.v1.template_generator import build_message_template, add_image
from paraty.slack_notify.templates.v1.slack_services import send_message
import traceback

def send_message_v1(message, custom_token=None, target_chat=None):
    logging.info("Sending message with template v1")
    bot_token = custom_token or os.getenv('TOKEN')
    uploaded_websites_id = target_chat or os.getenv('CHAT_ID')

    try:
        header_text = message.get('header')
        hotel_name = message.get('hotel_name')
        hotel_code = message.get('hotel_code')
        bold_content = message.get('bold_content', '')
        italic_content = message.get('italic_content', '')
        url = message.get('url', '')

        if not target_chat:
            target_chat = uploaded_websites_id


        message_structure = build_message_template(header_text=header_text, hotel_name=hotel_name, hotel_code=hotel_code,
                                          bold_content=bold_content, italic_content=italic_content, url_block=url)

        if (image := message.get('image', '')) and (image_name := message.get('image_name', '')):
            add_image(bot_token, image, image_name, message_structure)

        target_body = {
            'token': bot_token,
            'channel': target_chat,
            'blocks': message_structure,
            'as_user': True
        }

        return send_message(target_body)

    except Exception as e:
        logging.info(f'Something went wrong sending message: {e}')
        logging.error(traceback.format_exc())



