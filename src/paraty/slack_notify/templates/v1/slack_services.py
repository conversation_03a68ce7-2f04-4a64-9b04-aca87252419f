import json
from time import sleep

import base64
import logging
import requests
from slack_sdk import Web<PERSON><PERSON>


def send_message(body_content):
    logging.info("Sent message to slack")
    response = requests.post('https://slack.com/api/chat.postMessage',
                             headers={"content-type": "application/json; charset=utf-8",
                                      "Authorization": f"Bearer {body_content.get('token')}"}, data=json.dumps(body_content))
    logging.info(response.text)

    return response


def upload_image(custom_token, image, image_name):
    client = WebClient(token=custom_token)
    image_binary = base64.b64decode(image)
    upload = client.files_upload_v2(
        file=image_binary,
        filename=image_name or "imagen.jpg",
        title=image_name or "Imagen enviada desde Python",
    )

    counter = 0
    upload_data = upload.get('file')

    while not upload_data['mimetype'] or counter > 15:
        counter += 1
        sleep(1)
        file_info = client.files_info(file=upload_data['id'])
        upload_data = file_info.get('file')

    logging.info(f'Image took {counter}s to upload')
    return upload_data.get('id')