import logging
from paraty.slack_notify.templates.default.default_message_handler import send_message, send_image
from paraty.slack_notify.templates.v1.template_handler import send_message_v1

logging.basicConfig(level=logging.INFO)


def slack_message(request):
    logging.info("Received slack message")
    request_json = request.get_json()

    if request_json and 'message_v1' in request_json:
        message_content = request_json['message_v1']
        custom_token = request_json.get('token')
        custom_chat = request_json.get('chat_id')
        response = send_message_v1(message_content, custom_token=custom_token, target_chat=custom_chat)

        return {
            'message': response.json() if response else None
        }

    elif request_json and 'message' in request_json:
        message_content = request_json['message']
        custom_token = request_json.get('token')
        custom_chat = request_json.get('chat_id')
        response = send_message(message_content, custom_token, custom_chat)

        if request_json.get('image'):
            image_content = request_json['image']
            image_response = send_image(image_content, custom_token, custom_chat,
                                        image_name=request_json.get('image_name'))

            return {
                'message': response.json() if response else None,
                'image': image_response.data
            }

        return {
            'message': response.json() if response else None
        }

    logging.info("Missing message to send")
    return {
        'message': {}
    }
