import logging

from google.cloud.datastore import Key

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_entity_by_key
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.model.web_page_properties import WEB_PROPERTIES_PER_ENTITY


def _get_all_valid_entity_keys(hotel_code):

	result = set()
	all_entities = WEB_PROPERTIES_PER_ENTITY.keys()
	for current_entity in all_entities:
		all_keys = datastore_communicator.get_using_entity_and_params(entity_name=current_entity, keys_only=True, hotel_code=hotel_code)
		for current_key in all_keys:
			current_url_safe_key = id_to_entity_key(hotel_code, current_key.key)
			result.add(current_url_safe_key)

	return result


def clean_wp_with_crazy_value(hotel_code):

	all_properties = datastore_communicator.get_using_entity_and_params(entity_name='WebPageProperty', return_cursor=True, hotel_code=hotel_code)
	properties_to_remove = []
	for i, property in enumerate(all_properties):
		if property['value'] and len(property['value']) > 100000:
			properties_to_remove.append(property)
			related_entity_key = Key.from_legacy_urlsafe(property['entityKey'])
			related_entity = get_entity_by_key(related_entity_key, hotel_code)
			print(related_entity['name'])

			if related_entity.kind == 'Picture':
				related_to_picture_entity_key = Key.from_legacy_urlsafe(related_entity['mainKey'])
				related_to_picture_entity = get_entity_by_key(related_to_picture_entity_key, hotel_code)
				print(related_to_picture_entity['name'])

			print('property size: %s' % len(property['value']))

	print("properties with crazy size: %s" % len(properties_to_remove))

def clean_wp_with_null_value(hotel_code):

	all_properties = datastore_communicator.get_using_entity_and_params(entity_name='WebPageProperty', return_cursor=True, hotel_code=hotel_code)
	properties_to_remove = []
	for i, property in enumerate(all_properties):
		if not property['value']:
			properties_to_remove.append(property)

	print("properties to remove: %s" % len(properties_to_remove))

	entity_keys_to_delete = [x.key for x in properties_to_remove]
	datastore_communicator.delete_entity_multi(entity_keys_to_delete, hotel_code)



def clean_wp_without_associated_entity(hotel_code):
	all_valid_entity_keys = _get_all_valid_entity_keys(hotel_code)
	all_properties = datastore_communicator.get_using_entity_and_params(entity_name='WebPageProperty', return_cursor=True, hotel_code=hotel_code)
	properties_to_delete = []
	for i, property in enumerate(all_properties):
		print("checking property: %s" % i)
		entity_key = property['entityKey']

		if not entity_key in all_valid_entity_keys:
			properties_to_delete.append(property)

	print(len(properties_to_delete))
	entity_keys_to_delete = [x.key for x in properties_to_delete]
	datastore_communicator.delete_entity_multi(entity_keys_to_delete, hotel_code)


if __name__ == '__main__':

	all_hotels = get_all_valid_hotels()
	for hotel in all_hotels:
		hotel_code = hotel.get('applicationId')
		if not 'best-' in hotel_code:
			continue

		print(f"Cleaning {hotel_code}")

		# _get_all_valid_entity_keys('fuerte-rompido')
		clean_wp_with_null_value(hotel_code)
		# clean_wp_with_crazy_value('fuerte-rompido')
		# all_valid_hotels = get_all_valid_hotels()
		# found_starting_point = False
		# for i, hotel in enumerate(all_valid_hotels):
		# 	if 'fuerte' in hotel['applicationId']:
		# 		clean_wp_without_associated_entity(hotel['applicationId'])
		clean_wp_without_associated_entity(hotel_code)
