from google.cloud.datastore import Key

from paraty.maintainance.web_page_properties_cleaner import _get_all_valid_entity_keys
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_entity_by_key
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def clean_pictures_without_associated_entity(hotel_code):
	all_valid_entity_keys = _get_all_valid_entity_keys(hotel_code)
	all_properties = datastore_communicator.get_using_entity_and_params(entity_name='Picture', return_cursor=True, hotel_code=hotel_code)
	pictures_to_remove = []
	for i, picture in enumerate(all_properties):
		entity_key = picture['mainKey']

		if not entity_key in all_valid_entity_keys:
			try:
				related_to_picture_entity_key = Key.from_legacy_urlsafe(entity_key)
				related_to_picture_entity = get_entity_by_key(related_to_picture_entity_key, hotel_code)
				if related_to_picture_entity_key and not related_to_picture_entity:
					pictures_to_remove.append(picture)
			except:
				#Expected, i.e. if frontPicture or logo
				pass

	print(len(pictures_to_remove))

	entity_keys_to_delete = [x.key for x in pictures_to_remove]
	datastore_communicator.delete_entity_multi(entity_keys_to_delete, hotel_code)
	# for current in pictures_to_remove:
	# 	datastore_communicator.delete_entity('Picture', current.key.id, hotel_code)




if __name__ == '__main__':
	all_valid_hotels = get_all_valid_hotels()
	found_starting_point = False
	for i, hotel in enumerate(all_valid_hotels):
		if 'olee' in hotel['applicationId']:
			clean_pictures_without_associated_entity(hotel['applicationId'])