import datetime
import logging

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_available_namespaces
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def chunks(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]



def clean_sessions_in_namespace(hotel_code, current_namespace):

    expiration_threshold = datetime.datetime.now() - datetime.timedelta(0, 86400)

    logging.info("Cleaning namespace: %s" % current_namespace)

    all_sessions_to_delete = datastore_communicator.get_using_entity_and_params('UserSession', [('timestamp', '<', expiration_threshold)], keys_only=True, return_cursor=True, hotel_code=hotel_code)

    sessions_to_delete = []
    for session in all_sessions_to_delete:
        sessions_to_delete.append(session)
        if sessions_to_delete and len(sessions_to_delete) == 500:
            datastore_communicator.delete_entity_multi(sessions_to_delete, hotel_code)
            sessions_to_delete = []
            print("Deleted 500 sessions")


def clean_all_namespaces(hotel_code):
    logging.info("Entering session cleaner for hotel: %s" % hotel_code)
    all_namespaces = get_available_namespaces(hotel_code)
    for namespace in all_namespaces:
        clean_sessions_in_namespace(hotel_code, namespace)

if __name__ == '__main__':
    all_hotels = get_all_valid_hotels()
    for hotel in all_hotels:
        clean_all_namespaces(hotel['applicationId'])