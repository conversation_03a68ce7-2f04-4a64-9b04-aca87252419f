import time

import requests


def pin_healthcheck():

    manager_version = 'https://2024-02-01g-dot-admin-hotel.appspot.com'
    # manager_version = 'https://admin-hotel.appspot.com'
    manager_version = 'http://127.0.0.1:8888'


    response = requests.get(f'{manager_version}/healthcheck', timeout=20)
    response_time = response.elapsed.total_seconds()
    print(f"{manager_version}, {response.status_code}: {response_time}")


if __name__ == '__main__':
    while True:
        pin_healthcheck()
        time.sleep(0.1)
