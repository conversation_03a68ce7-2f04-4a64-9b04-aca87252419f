from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def clean_rts_with_strings_instead_bools(hotel_code):

	all_rts = datastore_communicator.get_using_entity_and_params(entity_name='RoomTypeStatus', return_cursor=True, hotel_code=hotel_code)

	for i, rts in enumerate(all_rts):
		open = rts['open']

		if isinstance(open, str):
			print("RTS with bad string found")

			if open.lower() == "true":
				new_open = True
			else:
				new_open = False

			print("Converting string %s to bool %s" % (open, new_open))
			new_open = True

			rts["open"] = new_open
			rts_id = int(rts.key.id)

			reservation_updated_id = datastore_communicator.save_to_datastore("RoomTypeStatus", rts_id, rts, hotel_code=hotel_code)

		elif isinstance(open, bool):
			print("OK")

		else:
			print("ERROR!")



def check_by_hotel(hotel, word):
	return word in hotel['applicationId']


def check_by_channel(hotel, channel_name):

	try:
		integrations = datastore_communicator.get_using_entity_and_params(entity_name='IntegrationConfiguration', search_params=[('name', '=', channel_name)], hotel_code=hotel['applicationId'])

		if integrations:
			if integrations[0].get("downloadBooking"):
				return True
		return False
	except:
		return False


if __name__ == '__main__':
	all_valid_hotels = get_all_valid_hotels()
	found_starting_point = False
	for i, hotel in enumerate(all_valid_hotels):
		if check_by_hotel(hotel, 'veintiuno'):
		#if check_by_channel(hotel, 'yieldplanet'):
			print("")
			print("")
			print("NEW HOTEL %s" % hotel['applicationId'])
			clean_rts_with_strings_instead_bools(hotel['applicationId'])