from paraty_commons_3.datastore import datastore_communicator


'''
This is due to a bug in the code that provokes the HotelMetadatas are being created when they are not found...

Once the bug is fixed we can remove this code
'''


def remove_duplicated_hotel_metadatas(entity_name, field_to_sort_by, hotel_code):
    all_entities = datastore_communicator.get_using_entity_and_params(entity_name, search_params=[], hotel_code=hotel_code)

    aux_structure = {}

    for entity in all_entities:
        key = entity.get(field_to_sort_by)
        if not key in aux_structure:
            aux_structure[key] = []

        aux_structure[key].append(entity)

    keys_to_delete = []
    for k, v in aux_structure.items():
        if len(v) > 1:
            v.sort(key=lambda x: x.get('configurations', []), reverse=True)
            found_one = False
            for i in v:
                prefix = i.get('location_prefix')
                account_manager = i.get('account_manager')
                goals = i.get('goals')
                configurations = i.get('configurations')
                print(i)
                if (not prefix) and (not account_manager) and (not goals) and (not configurations):
                    print('Deleted: %s ' % i)
                    #datastore_communicator.delete_entity(entity_name, i.key.id_or_name, hotel_code)
                    keys_to_delete.append(i.key)
                elif found_one and (not account_manager) and (not goals) and (not configurations):
                    keys_to_delete.append(i.key)
                elif prefix:
                    found_one = True

            print('Duplicated: %s ' % k)

    print('Keys to delete: %s ' % len(keys_to_delete))
    datastore_communicator.delete_entity_multi(keys_to_delete, hotel_code)
    print('Finished')


if __name__ == '__main__':
    remove_duplicated_hotel_metadatas('HotelMetadata', 'applicationId', 'admin-hotel')

