import random

from google.cloud import datastore
from paraty.backups.backup_constants import BAC<PERSON><PERSON>_BUCKET, HOTEL_BAC<PERSON>UP_FUNCTION
from paraty.backups.backup_utils import backup_entities
from paraty.development.restore_backup_utils import get_last_backup
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.google_functions_utils import functions_utils
from paraty.backups.backup_constants import HOTEL_BACKUP_QUEUE, BACKUP_ENTITIES_FUNCTION, BACKUP_BUCKET
from built_tools_2_constants import BUILD_TOOLS_FUNCTIONS_PATH
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

# Note that userclub is just not saved because it is too big in best-corporativa
from paraty_commons_3.logging.my_gae_logging import logging

BOOKING_ENTITIES_WITH_PICTURES = ['RoomType', 'Promotion', 'PriceIncrease']
ENTITIES_IGNORE = ['GiftBono', 'Reservation', 'Consent', 'ExternalIdentifiers', 'BookingHtml', 'Booking1Html', 'cache', 'pageentry', 'usersession', 'userentry', 'sessionmodel', 'errorswebmodel', 'refreshstatus', 'logbackup', 'UserClub','ServiceExternal']
BOOKING_ENTITIES_TO_RESTORE = ['RatePeriod','RoomType', 'Rate', 'RateCondition', 'Regimen', 'Promotion', 'IntegrationConfiguration']

BOOKING_ENTITIES_WITH_WPP = ['RoomType', 'Rate', 'Regimen', 'RateCondition', 'Supplement', 'MultiRate', 'News', 'Promotion', 'PriceIncrease']
ONLYWEB_ENTITIES_WITH_WPP = ['Picture', 'WebSection', 'News']

ENTITIES_FOR_WEBSEEKERS = ['WysiwygHistory', 'Wysiwyg']

EXTRA_ENTITIES_ONLY_WEB_TO_IGNORE = ['Reservation', 'UserClub', 'Supplement', 'FinalPriceDay', 'priceIncrease', 'priceTimer', 'redirection', 'webconfiguration',
                                     'Restriction', 'YieldModification', 'SatisfactionForm', 'GiftBono']
ENTITIES_IGNORE_ONLY_WEB = ENTITIES_IGNORE + BOOKING_ENTITIES_TO_RESTORE + EXTRA_ENTITIES_ONLY_WEB_TO_IGNORE



ALL_ENTITIES_BY_DEFAULT_IN_BACKUP = ['Agencies', 'ConfigurationProperty', 'Consent', 'Currency', 'IntegrationConfiguration', 'MarketingLogo',
            'MultiRate', 'News', 'Picture', 'PriceIncrease', 'Promocode', 'Promotion', 'Rate', 'RateCondition',
            'RatePeriod', 'Redirection', 'Regimen', 'RoomType', 'RoomTypeStatus', 'Supplement',
           'WebConfiguration', 'WebSection', 'Wysiwyg', 'WysiwygHistory', 'WebPageProperty', 'GiftBono']

def _get_all_entities(hotel_code):
    project, namespace = hotel_manager_utils.get_hotel_project_and_namespace(hotel_code)
    new_client = datastore.Client(project=project, namespace=namespace)
    query = new_client.query(kind='__kind__')
    query.keys_only()
    kinds = [entity.key.id_or_name for entity in query.fetch()]
    return kinds


def _get_kinds_to_save(hotel_code, only_web=False, add_advanced_configurations=True, add_reservations=True, webseekers=True):
    all_kinds = _get_all_entities(hotel_code)
    valid_kinds = []

    if only_web:
        entities_to_ignore = ENTITIES_IGNORE_ONLY_WEB
    else:
        entities_to_ignore = ENTITIES_IGNORE

    if not add_advanced_configurations:
        entities_to_ignore += ['ConfigurationProperty']

    if not add_reservations:
        entities_to_ignore += ['Reservation']

    if not webseekers:
        entities_to_ignore += ENTITIES_FOR_WEBSEEKERS

    for kind in all_kinds:
        to_ignore = False

        for content_to_ignore in entities_to_ignore:
            if content_to_ignore.lower() in kind.lower() or kind.startswith("_"):
                to_ignore = True
                break
        if not to_ignore:
            valid_kinds.append(kind)

    return valid_kinds


def execute_hotel_manager_backup(request):
    date_format = '%Y-%m-%d-%H-%M-%S'
    backup_entities(BACKUP_BUCKET, 'admin-hotel', 'admin-hotel', ['ParatyUser', 'HotelApplication', 'HotelMetadata'], date_format=date_format)
    return 'OK'


def execute_hotel_backup(hotel_code):
    try:
        logging.info("Executing backup for %s", hotel_code)
        valid_entities = _get_kinds_to_save(hotel_code)
        date_format = '%Y-%m-%d-%H-%M-%S'
        backup_entities(BACKUP_BUCKET, 'hotels', hotel_code, valid_entities, date_format=date_format)
        return 'OK'
    except Exception as e:
        logging.info("ERROR making backup: %s, e:%s", hotel_code, e)
        return 'KO'


def execute_hotels_backup(request):
    function_url = lambda hotel_code: '%s/%s?hotel_code=%s&global_path=hotel' % (BUILD_TOOLS_FUNCTIONS_PATH, HOTEL_BACKUP_FUNCTION, hotel_code)

    functions_utils.create_task_for_each_hotel(HOTEL_BACKUP_QUEUE, url_creator=function_url, include_non_production=True)

    return 'OK'


if __name__ == '__main__':

    execute_hotel_manager_backup(None)
    # remaining = ['hotel-puentereal', 'hotel-rosamar', 'flashhotelbenidorm', 'ona-corporativa', 'holiday-palace', 'daguisa-goldenfenix', 'acebuchal', 'best-serenade', 'ap-adriana', 'ipv-backup', 'holiday-corporativa', 'granpalas-experience', 'holiday-polynesia', 'vik-corporativa', 'dreamland-corpo', 'tarifa-lances', 'best-corporate', 'best-osuna', 'best-frontmaritim', 'blaumar-acacias', 'ona-bahia-blanca', 'demo5', 'best-autohogar', 'onhotel', 'test3', 'villa-flamenca', 'playagolf-islantilla', 'hacienda-alamo', 'best-4barcelona', 'la-barracuda', 'landmar-gigantes', 'ap-corporate', 'hotel-esmeralda', 'hacienda-apartamentos', 'bahiablanca', 'neptuno-roquetas', 'vime-marbella', 'matalascanas', 'toboso-corporativa', 'blaumar-blaumar', 'bg-corporativa', 'ohtels-europe', 'ohtels-corporate', 'ipv-palace']
    #
    # for x in remaining[16:]:
    # 	execute_hotel_backup(x)
    #
    # params = [(x['applicationId'],) for x in get_all_valid_hotels()]
    #
    # random.shuffle(params)
    #
    # execute_in_parallel(execute_hotel_backup, params, max_concurrency=10)

# execute_hotel_backup('ipv-palace')
# all_valid_hotels = get_all_valid_hotels()
# found_starting_point = False
# for i, hotel in enumerate(all_valid_hotels):
# 	try:
# 		if i >= 0:
# 			found_starting_point = True
#
# 		if found_starting_point:
# 			execute_hotel_backup(hotel['applicationId'])
# 	except Exception as e:
# 		logging.error(e)
