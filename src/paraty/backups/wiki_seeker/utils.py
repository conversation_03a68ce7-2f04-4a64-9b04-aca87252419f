import datetime
import logging

from google.auth import compute_engine
from google.auth.transport.requests import AuthorizedSession
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from google.cloud import storage
import io

from paraty.backups.wiki_seeker.constants import BUCKET_NAME, MIME_TYPE_MAPPING

# Autenticación from service account
# actual_path = os.path.dirname(os.path.realpath(__file__))
# credentials_path = os.path.join(actual_path, '../../../../credentials.json')
# service_account_credentials = service_account.Credentials.from_service_account_file(
#     credentials_path,
#     scopes=['https://www.googleapis.com/auth/drive']
# )

service_account_credentials = compute_engine.Credentials()

def get_all_files_from_drive(drive_name='paratytech'):
    # Autenticación (asumiendo que ya has configurado la cuenta de servicio)
    drive_service = build('drive', 'v3', credentials=service_account_credentials)

    # Listar unidades disponibles
    available_drives = drive_service.drives().list().execute()
    target_drive = [x for x in available_drives['drives'] if x['name'] == drive_name][0]

    # Listar todos los archivos de la unidad
    next_page_token = None
    items = []
    files_by_id = {}
    while True:
        results = drive_service.files().list(
            corpora='drive',
            includeItemsFromAllDrives=True,
            supportsAllDrives=True,
            driveId=target_drive['id'],
            pageSize=1000,
            pageToken=next_page_token,
            fields='nextPageToken, files(id, name, mimeType, parents, size, exportLinks)',
        ).execute()

        items += results.get('files', [])
        files_by_id.update({x['id']: x for x in items})

        if not results.get('nextPageToken'):
            break

        else:
            next_page_token = results.get('nextPageToken')

    return files_by_id


def build_tree_path(item, files_by_id):
    tree_path = []

    if item.get('parents'):
        parent_id = item['parents'][0]
        while parent_id:
            parent = files_by_id.get(parent_id)
            if not parent:
                break

            tree_path.append(parent['name'])
            parent_id = parent.get('parents', [None])[0]

    return tree_path


def upload_files_to_gcs(files_by_id, folder_backup='wiki_seeker'):

    items = files_by_id.values()
    drive_service = build('drive', 'v3', credentials=service_account_credentials)
    storage_client = storage.Client()
    bucket = storage_client.get_bucket(BUCKET_NAME)

    # Descargar y subir archivos
    for item in items:

        # If its a folder, doesnt need a backup
        if item['mimeType'] == 'application/vnd.google-apps.folder':
            continue

        # Cant copy a shortcut
        if item['mimeType'] == 'application/vnd.google-apps.shortcut':
            continue

        tree_path = build_tree_path(item, files_by_id)
        tree_path.reverse()

        file_name = item['name']

        # Calculate the file type to be saved
        mimetype_customization = MIME_TYPE_MAPPING.get(item['mimeType'])
        export_link = None
        if mimetype_customization:
            target_media_export = mimetype_customization['mimetype']
            file_name = file_name + '.' + mimetype_customization['extension']

            export_links_available = item.get('exportLinks')
            export_link = export_links_available.get(target_media_export)
        else:
            request = drive_service.files().get_media(fileId=item['id'])


        # Upload to GCS
        actual_date = datetime.datetime.now().strftime("%Y-%m-%d")
        base_path = f'{folder_backup}/{actual_date}'
        if tree_path:
            full_path = '/'.join(tree_path)
            target_path = f'{base_path}/{full_path}/{file_name}'
        else:
            target_path = f'{base_path}/{file_name}'

        blob = bucket.blob(target_path)
        if blob.exists():
            logging.info(f'File {target_path} already exists')
            continue
        else:
            logging.info(f'Uploading file {target_path}')



        # Download on memory
        if not export_link:
            fh = io.BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            done = False

            while done is False:
                status, done = downloader.next_chunk()
            fh.seek(0)

            blob.upload_from_file(fh)
        else:
            authed_session = AuthorizedSession(service_account_credentials)
            response = authed_session.get(export_link)
            response_bytes = response.content
            blob.upload_from_string(response_bytes)
