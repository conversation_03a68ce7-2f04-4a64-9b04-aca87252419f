from paraty.backups.wiki_seeker.utils import get_all_files_from_drive, upload_files_to_gcs


"""
    IMPORTANT INFORMATION:
    - Shortcuts will not be backed up
    - Folders will not be backed up
"""


def perform_backup_of_wikis(request):
    _backup_drive(drive_name='paratytech', gcs_folder_name='wiki_seeker')
    _backup_drive(drive_name='dataseekers', gcs_folder_name='dataseekers_wiki_seeker')
    return 'ok'


def _backup_drive(drive_name, gcs_folder_name):
    files_by_id = get_all_files_from_drive(drive_name)
    upload_files_to_gcs(files_by_id, folder_backup=gcs_folder_name)


if __name__ == '__main__':
    perform_backup_of_wikis(None)
