import io
import json
import logging
import zipfile
import zlib
from datetime import date, datetime

from google.cloud import storage
from paraty import Config

from paraty.backups.backup_constants import BACKUP_BUCKET
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.security_utils import encrypt_random_text

ENTITIES_TO_ENCRYPT = ['Reservation', 'UserClub', 'SatisfactionForm']

# i.e. We remove the data that is not safe to save
ENTITIES_TO_CLEAN_BEFORE_SAVING = ['ParatyUser']

def entities_generic_backup(request):
	hotel_code = request.values.get("hotel_code")
	entities = request.values.get('entities').split(",")
	global_path = request.values.get('global_path')
	bucket_name = BACKUP_BUCKET
	backup_entities(bucket_name=bucket_name, global_path=global_path, hotel_code=hotel_code, entities=entities)
	return 'OK'

#Works with either a hotel_code or a specific project and namespace
def backup_entities(bucket_name, global_path, hotel_code, entities, compress_if_large=True, date_format='%Y-%m-%d', specific_fields=None):

	today = datetime.now().strftime(date_format)

	for entity_name in entities:
		all_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, hotel_code=hotel_code)

		if not all_entities:
			continue

		all_entities_json = []
		for current_entity in all_entities:
			new_entity = dict(current_entity)

			if specific_fields:
				for key in list(new_entity.keys()):
					if key not in specific_fields:
						del new_entity[key]

				if 'key_name' in new_entity:
					new_entity['key_name'] = current_entity.key.name

			new_entity['id'] = current_entity.id

			if datastore_communicator.is_hotel_datastore(hotel_code):
				new_entity['key'] = datastore_utils.id_to_entity_key(hotel_code, current_entity.key)

			all_entities_json.append(new_entity)

		text = json.dumps(all_entities_json, default=str)
		if entity_name in ENTITIES_TO_ENCRYPT:
			#We compress them first because after encryption they compress poorly in zip format
			compressed_text = str(zlib.compress(text.encode("utf-8")))
			text = encrypt_random_text(hotel_code, compressed_text)

		elif entity_name in ENTITIES_TO_CLEAN_BEFORE_SAVING:
			for entity in all_entities_json:
				entity['password'] = '*********'
			text = json.dumps(all_entities_json, default=str)

		hotel_code_in_path = hotel_code + "/"
		if not datastore_communicator.is_hotel_datastore(hotel_code):
			hotel_code_in_path = ''

		path = '%s/%s%s' % (global_path, hotel_code_in_path, today)

		if specific_fields:
			file_name = '%s/%s_only_%s.json' % (path, entity_name,  '_'.join(specific_fields))
		else:
			file_name = '%s/%s.json' % (path, entity_name)

		logging.info("Saving file %s" % file_name)
		save_text_file(bucket_name, file_name, text, compress_if_large=compress_if_large)


@timeit
def get_all_from_entity(entity_name, hotel_code):
	all_entities = datastore_communicator.get_using_entity_and_params(entity_name=entity_name, hotel_code=hotel_code, return_cursor=True)
	all_entities_json = []
	for current_entity in all_entities:
		new_entity = dict(current_entity)
		new_entity['id'] = current_entity.id

		if datastore_communicator.is_hotel_datastore(hotel_code):
			new_entity['key'] = datastore_utils.id_to_entity_key(hotel_code, current_entity.key)

		all_entities_json.append(new_entity)

	return all_entities_json


def save_text_file(bucket_name, full_file_path, content, content_type='application/json', password=None, compress_if_large=True):

	if compress_if_large and len(content) > 150000 or password:
		return save_in_zip_file(bucket_name, full_file_path, content)

	storage_client = storage.Client(Config.PROJECT)
	bucket = storage_client.bucket(bucket_name)
	thumbnail_blob = bucket.blob(full_file_path)
	thumbnail_blob.upload_from_string(content, content_type=content_type)

	logging.info("Saved file %s" % full_file_path)


def save_binary_to_file(bucket_name, data, full_file_path):
	try:
		client = storage.Client()
		bucket = client.bucket(bucket_name)
		bucket.blob(full_file_path).upload_from_string(data)
		return bucket.blob(full_file_path).public_url

	except Exception as e:
		print(e)

	return None


def save_multiple_in_zip_file(bucket_name, zip_path, multiple_blobs):

	f = io.BytesIO()
	z = zipfile.ZipFile(f, 'w', zipfile.ZIP_DEFLATED)

	for current_blob in multiple_blobs:
		file_name = "/".join(current_blob.name.split("/")[-2:])
		z.writestr(file_name, current_blob.download_as_bytes())

	z.close()

	f.seek(0)

	storage_client = storage.Client()
	bucket = storage_client.bucket(bucket_name)
	thumbnail_blob = bucket.blob(zip_path)
	thumbnail_blob.upload_from_file(f, content_type='application/zip')
	return thumbnail_blob.public_url


def save_in_zip_file(bucket_name, full_file_path, content, password=None):

	f = io.BytesIO()
	z = zipfile.ZipFile(f, 'w', zipfile.ZIP_DEFLATED)

	if password:
		#TODO, this is not working with zipfile
		pass

	z.writestr(full_file_path.split("/")[-1], content)
	z.close()
	f.seek(0)

	storage_client = storage.Client(Config.PROJECT)
	bucket = storage_client.bucket(bucket_name)
	thumbnail_blob = bucket.blob(full_file_path.replace(".json", ".zip"))
	thumbnail_blob.upload_from_file(f, content_type='application/zip')


if __name__ == '__main__':
	backup_entities('paraty_backups', 'web_seeker/', 'hotel-zen', ['Regimen'])
	# save_text_file("paraty_backups", "prueba/prueba2.json", '{"name": "Hello world"}', "application/json")