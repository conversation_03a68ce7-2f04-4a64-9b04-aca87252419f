from built_tools_2_constants import BUILD_TOOLS_FUNCTIONS_PATH
from paraty.backups.backup_constants import WEB_SEEKER_BACKUP_QUEUE, BACKUP_ENTITIES_FUNCTION, <PERSON><PERSON><PERSON>UP_BUCKET
from paraty.backups.backup_utils import backup_entities
from paraty_commons_3.google_functions_utils import functions_utils


def execute_webseeker_backup(request):

	save_wysiwyg_function_url = lambda hotel_code: '%s/%s?hotel_code=%s&entities=%s&global_path=web_seeker' % (BUILD_TOOLS_FUNCTIONS_PATH, BACKUP_ENTITIES_FUNCTION, hotel_code, 'Wysiwyg')
	functions_utils.create_task_for_each_hotel(WEB_SEEKER_BACKUP_QUEUE, url_creator=save_wysiwyg_function_url, include_non_production=True)

	#Save
	ENTITIES_TO_SAVE_IN_WEB_SEEKER = ['Font', 'LibraryComponent', 'Users', 'WebComponent']
	backup_entities(<PERSON><PERSON><PERSON><PERSON>_BUCKET, 'web_seeker/web_seeker', 'web-seeker:', ENTITIES_TO_SAVE_IN_WEB_SEEKER)

	return 'OK'