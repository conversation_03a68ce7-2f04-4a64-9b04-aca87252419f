# Required by common libraries
# If this is not defined we know we are testing it locally
import logging
import os
from paraty_commons_3.decorators.cache import timebased_cache

#See https://cloud.google.com/functions/docs/env-var#nodejs_10_and_subsequent_runtimes
IN_DEV_ENVIRONMENT = not os.environ.get('FUNCTION_TARGET')

logging.info("DEV: %s", IN_DEV_ENVIRONMENT)


class Config(object):
    DEV = IN_DEV_ENVIRONMENT
    LOCATION = 'europe-west1'
    PROJECT = 'build-tools-2'
    NAMESPACE = ''
    TEMPLATES_PATH = './templates'
    TESTING = False


if Config.DEV:
    Config.TEMPLATES_PATH = '../templates'

# # In google functions we suppose environment to be stateless so there is no point in caching for one single request
# timebased_cache.disable_cache()
