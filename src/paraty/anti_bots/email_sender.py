import os
from datetime import datetime

import requests
from jinja2 import Environment

from paraty_commons_3 import email_utils

EMAILS_TO_NOTIFY = "<EMAIL>,<EMAIL>,<EMAIL>"
GET_POTENTIAL_BOTS_URL = "https://security-seeker.appspot.com/get_potential_bot_ips"
HOURS = 1
REQUEST_THRESHOLD = 400


def send_bot_notification_email():
    result = requests.get(f"{GET_POTENTIAL_BOTS_URL}?hours={HOURS}").json()

    potential_bots = [potential_bot for potential_bot in result
                      if potential_bot["COUNTER"] > REQUEST_THRESHOLD and "banned" not in potential_bot]

    for bot in potential_bots:
        bot["LAST_ACCESS"] = format_date(bot["LAST_ACCESS"])

    email_html = _render_template(potential_bots)
    if potential_bots:
        email_utils.sendEmail(EMAILS_TO_NOTIFY, "Potential Bots", "", email_html, send_default_bcc=False)


def _render_template(potential_bots):
    email_template_data = open(os.path.dirname(os.path.abspath(__file__)) + '/templates/potential_bots_email.html', 'r').read()
    template = Environment().from_string(email_template_data)
    return template.render(potential_bots=potential_bots)


def format_date(date_str):
    date_obj = datetime.strptime(date_str, "%a, %d %b %Y %H:%M:%S %Z")
    return date_obj.strftime("%Y-%m-%d")


if __name__ == '__main__':
    send_bot_notification_email()
