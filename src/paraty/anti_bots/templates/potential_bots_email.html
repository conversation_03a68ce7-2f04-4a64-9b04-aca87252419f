<!DOCTYPE html>
<html>
<head>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            background-color: #4CAF50; /* Green */
            border: none;
            color: white;
            padding: 10px 24px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h2>Potential Bots</h2>
    <table>
        <tr>
            <th>IP</th>
            <th>COUNTER</th>
            <th>EXAMPLE_HOST</th>
            <th>LAST_ACCESS</th>
            <th>VISITED_HOSTS</th>
            <th>Country</th>
            <th>ISP</th>
            <th>Hosting</th>
            <th>Proxy</th>
            <th>Action</th>
        </tr>
        {% for bot in potential_bots %}
        <tr>
            <td>{{ bot.IP }}</td>
            <td>{{ bot.COUNTER }}</td>
            <td>{{ bot.EXAMPLE_HOST }}</td>
            <td>{{ bot.LAST_ACCESS }}</td>
            <td>{{ bot.VISITED_HOSTS }}</td>
            <td>{{ bot.info.country }}</td>
            <td>{{ bot.info.isp }}</td>
            <td>{{ 'Yes' if bot.info.hosting else 'No' }}</td>
            <td>{{ 'Yes' if bot.info.proxy else 'No' }}</td>
            <td><a href="https://security-seeker.appspot.com/block_ip/{{ bot.IP }}" class="button">Block Ip</a></td>
        </tr>
        {% endfor %}
    </table>
    <br>

<p>Sent by build-tools-2</p>
</body>
</html>