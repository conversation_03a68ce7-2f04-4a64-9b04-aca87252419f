import csv
import zipfile
import io
import datetime
import json
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator # Assuming this is the correct module
import logging

# --- Hardcoded values ---
ZIP_FILE_NAME = "triptease_report_YYYY_MM.zip"  # Placeholder, will be formatted
COMMISSION_PERCENTAGE_HARDCODED = 10.0  # Example: 10%
# TODO: Define actual start and end dates
START_DATE_HARDCODED = "2025-05-01" # Example date
END_DATE_HARDCODED = "2025-05-31" # Example date
# INTEGRATION_NAME = "TRIPTEASE_ADAPTER" # No longer needed
UTM_LABEL_CONTAINS = "GHA CPA"
TRIPTEASE_HOTELS_CSV_PATH = "triptease_hotels.csv"
# --- End Hardcoded values ---

def get_triptease_commission_for_hotel(hotel_code: str, default_commission: float) -> float:
    """
    Retrieves the Triptease commission percentage for a specific hotel from its HotelMetadata.
    Falls back to a default if not found or if an error occurs.
    """
    try:
        # Assuming get_hotel_metadata_by_application_id is available from common_data_provider or hotel_manager_utils
        # Based on search, it's in hotel_manager_utils
        hotel_metadata = hotel_manager_utils.get_hotel_metadata_by_application_id(hotel_code)

        if hotel_metadata and 'configurations' in hotel_metadata:
            configurations = hotel_metadata.get('configurations', [])
            for config_str in configurations:
                if " @@ " in config_str:
                    key, value = config_str.split(" @@ ", 1)
                    if key.strip().lower() == "triptease":
                        commission_val = float(value.strip())
                        print(f"Found Triptease commission for {hotel_code}: {commission_val}%")
                        return commission_val
            logging.warning(f"Triptease commission key not found in HotelMetadata for {hotel_code}. Using default.")
        else:
            logging.warning(f"No HotelMetadata or configurations found for {hotel_code}. Using default commission.")
    except ValueError as e:
        logging.error(f"Error converting commission to float for {hotel_code}: {e}. Using default commission.")
    except Exception as e:
        logging.error(f"Error fetching or parsing HotelMetadata for {hotel_code}: {e}. Using default commission.")
    
    return default_commission

def get_hotels_from_csv(csv_path: str) -> list:
    """
    Reads hotel codes from a CSV file and returns a list of hotel dicts.
    The CSV should have a header, and one column named "Hotel" with hotel codes.
    """
    hotels_to_process = []
    print(f"Reading hotel codes from CSV: {csv_path}")
    try:
        with open(csv_path, mode='r', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            if "Hotel" not in reader.fieldnames:
                logging.error(f"CSV file {csv_path} must have a column named 'Hotel'.")
                return []
            for row in reader:
                hotel_code = row["Hotel"].strip()
                if hotel_code:
                    # Create a mock hotel object similar to what other functions expect
                    hotels_to_process.append({'applicationId': hotel_code, 'name': hotel_code})
    except FileNotFoundError:
        logging.error(f"Hotel CSV file not found: {csv_path}")
        return []
    except Exception as e:
        logging.error(f"Error reading hotel CSV file {csv_path}: {e}")
        return []
    
    print(f"Found {len(hotels_to_process)} hotels in {csv_path}.")
    return hotels_to_process

def get_filtered_hotels_from_csv(csv_path: str) -> list:
    """
    Reads hotel codes from a CSV file, then filters the list of all valid hotels
    to include only those whose applicationID is in the CSV.
    The CSV should have a header and one column named "Hotel" with hotel codes.
    """
    hotel_codes_from_csv = set()
    print(f"Reading target hotel codes from CSV: {csv_path}")
    try:
        with open(csv_path, mode='r', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            if "Hotel" not in reader.fieldnames:
                logging.error(f"CSV file {csv_path} must have a column named 'Hotel'.")
                return []
            for row in reader:
                hotel_code = row["Hotel"].strip()
                if hotel_code:
                    hotel_codes_from_csv.add(hotel_code)
    except FileNotFoundError:
        logging.error(f"Hotel CSV file not found: {csv_path}. No hotels will be processed.")
        return []
    except Exception as e:
        logging.error(f"Error reading hotel CSV file {csv_path}: {e}. No hotels will be processed.")
        return []

    if not hotel_codes_from_csv:
        print(f"No hotel codes found in {csv_path}. No hotels will be processed.")
        return []
    
    print(f"Found {len(hotel_codes_from_csv)} target hotel codes in {csv_path}. Now filtering all valid hotels.")

    all_valid_hotels = hotel_manager_utils.get_all_valid_hotels()
    hotels_to_process = []

    for hotel in all_valid_hotels:
        hotel_app_id = hotel.get("applicationId")
        if hotel_app_id in hotel_codes_from_csv:
            hotels_to_process.append(hotel)
            # Optionally, remove from set to check if all CSV codes were found later
            # hotel_codes_from_csv.remove(hotel_app_id) 
    
    # if hotel_codes_from_csv: # Some codes in CSV were not found in all_valid_hotels
    #     logging.warning(f"The following hotel codes from {csv_path} were not found or are not valid: {list(hotel_codes_from_csv)}")

    print(f"Filtered down to {len(hotels_to_process)} hotels to process based on CSV and validity.")
    return hotels_to_process

def get_reservations_for_hotel(hotel_code: str, start_date: str, end_date: str) -> list:
    """
    Fetches reservations for a given hotel code within a specific date range.
    The dates should be in 'YYYY-MM-DD' format.
    """
    print(f"Fetching reservations for hotel {hotel_code} from {start_date} to {end_date}")
    # Convert dates to include full day

    search_params = [
        ('endDate', '>=', start_date),
        ('endDate', '<=', end_date)
    ]
    reservations = datastore_communicator.get_using_entity_and_params(
        'Reservation',
        search_params=search_params,
        hotel_code=hotel_code
    )
    print(f"Found {len(reservations)} reservations for hotel {hotel_code} before filtering.")
    return reservations

def filter_reservations_by_utm_label(reservations: list, utm_label_substring: str) -> list:
    """
    Filters reservations based on the presence of a substring in extra_info.utm_label_generated.
    """
    filtered_reservations = []
    for res in reservations:
        extra_info_str = res.get("extraInfo", "{}")
        extra_info = {}
        try:
            if isinstance(extra_info_str, str): # Ensure it's a string before parsing
                 extra_info = json.loads(extra_info_str) if extra_info_str else {}
            elif isinstance(extra_info_str, dict): # If it's already a dict
                extra_info = extra_info_str
        except json.JSONDecodeError:
            print(f"Warning: Could not parse extraInfo for reservation {res.get('identifier')}: {extra_info_str[:100]}...") # Log truncated extraInfo
            extra_info = {} # Default to empty dict on error

        if isinstance(extra_info, dict): # Proceed only if extra_info is a dict
            utm_label = extra_info.get("utm_label_generated", "")
            if utm_label_substring in utm_label:
                filtered_reservations.append(res)
    print(f"Filtered down to {len(filtered_reservations)} reservations based on UTM label: '{utm_label_substring}'.")
    return filtered_reservations

def create_csv_data(reservations: list, hotel_specific_commission: float) -> list:
    """
    Creates the CSV data rows based on the provided image format.
    Uses hotel-specific commission.
    """
    csv_rows = []
    total_commission_value = 0
    num_reservations = len(reservations)

    # Header for summary
    csv_rows.append(["sep="]) # As per image
    csv_rows.append(["Num reservations", num_reservations])
    # Placeholder for total commission, will be calculated and updated later
    total_commission_row_index = len(csv_rows)
    csv_rows.append(["Total commission", 0]) # Placeholder
    csv_rows.append([]) # Empty row as separator

    # Detailed header
    csv_rows.append([
        "identifier", "booking timestamp", "checkin", "checkout",
        "price", "commission %", "commission"
    ])

    for res in reservations:
        price = float(res.get("price", 0)) # Ensure price is float
        price_supplements = float(res.get("priceSupplements", 0)) # Get priceSupplements
        total_price = price + price_supplements # Calculate total price

        commission_value = (total_price * hotel_specific_commission) / 100
        total_commission_value += commission_value

        # Format dates as YYYY-MM-DD if they are not already strings
        # Assuming booking_timestamp, checkin, checkout are available and in a parsable format or already strings
        # For booking_timestamp, it's often a datetime object or string like 'YYYY-MM-DD HH:MM:SS'
        booking_ts_raw = res.get("timestamp") # 'timestamp' seems to be booking creation
        booking_timestamp_str = ""
        if isinstance(booking_ts_raw, datetime.datetime):
            booking_timestamp_str = booking_ts_raw.strftime('%Y-%m-%d')
        elif isinstance(booking_ts_raw, str):
            if ' ' in booking_ts_raw: # Attempt to parse "YYYY-MM-DD HH:MM:SS"
                try:
                    booking_timestamp_str = datetime.datetime.strptime(booking_ts_raw.split(' ')[0], '%Y-%m-%d').strftime('%Y-%m-%d')
                except ValueError:
                    booking_timestamp_str = booking_ts_raw.split(' ')[0] # Fallback to taking the date part
            else: # Assume it might already be "YYYY-MM-DD"
                 booking_timestamp_str = booking_ts_raw
        else:
            booking_timestamp_str = str(booking_ts_raw) if booking_ts_raw else ""


        checkin_date_raw = res.get("startDate") # Common field name for check-in
        checkin_str = ""
        if isinstance(checkin_date_raw, datetime.date): # Handles both datetime.date and datetime.datetime
            checkin_str = checkin_date_raw.strftime('%Y-%m-%d')
        elif isinstance(checkin_date_raw, str):
            checkin_str = checkin_date_raw # Assuming YYYY-MM-DD string
        else:
            checkin_str = str(checkin_date_raw) if checkin_date_raw else ""

        checkout_date_raw = res.get("endDate") # Common field name for check-out
        checkout_str = ""
        if isinstance(checkout_date_raw, datetime.date): # Handles both datetime.date and datetime.datetime
            checkout_str = checkout_date_raw.strftime('%Y-%m-%d')
        elif isinstance(checkout_date_raw, str):
            checkout_str = checkout_date_raw # Assuming YYYY-MM-DD string
        else:
            checkout_str = str(checkout_date_raw) if checkout_date_raw else ""


        csv_rows.append([
            res.get("identifier", ""),
            booking_timestamp_str,
            checkin_str,
            checkout_str,
            f"{total_price:.2f}".replace(".", ","), # Use total_price
            f"{hotel_specific_commission:.2f}%".replace(".", ","),
            f"{commission_value:.2f}".replace(".", ",")
        ])

    # Update total commission in the summary
    csv_rows[total_commission_row_index][1] = f"{total_commission_value:.2f}".replace(".", ",")
    return csv_rows


def generate_report():
    """
    Main function to generate the Triptease billing report.
    """
    # Determine dynamic ZIP file name based on current month/year or specified period
    # For now, using hardcoded and replacing placeholders
    # This could be improved to use the actual START_DATE_HARDCODED for naming
    report_month_year = datetime.datetime.now().strftime("%Y_%m") # Example: 2024_03
    # Or derive from START_DATE_HARDCODED if it represents a specific month
    try:
        report_date_obj = datetime.datetime.strptime(START_DATE_HARDCODED, "%Y-%m-%d")
        report_month_year = report_date_obj.strftime("%Y_%m")
    except ValueError:
        pass # Keep current month if parsing fails

    current_zip_file_name = ZIP_FILE_NAME.replace("YYYY_MM", report_month_year)
    print(f"Generating report: {current_zip_file_name}")

    hotels_to_process = get_filtered_hotels_from_csv(TRIPTEASE_HOTELS_CSV_PATH)

    if not hotels_to_process:
        print("No hotels found with the specified integration. Exiting.")
        return

    # Use an in-memory buffer for the ZIP file
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zf:
        for hotel in hotels_to_process:
            hotel_code = hotel.get("applicationId")
            if not hotel_code:
                print(f"Hotel missing 'applicationId', skipping: {hotel.get('name', 'Unknown Hotel')}")
                continue

            print(f"Processing hotel: {hotel_code}")
            
            # Get hotel-specific commission
            current_hotel_commission = get_triptease_commission_for_hotel(hotel_code, COMMISSION_PERCENTAGE_HARDCODED)
            print(f"Using commission {current_hotel_commission}% for hotel {hotel_code}")

            reservations = get_reservations_for_hotel(hotel_code, START_DATE_HARDCODED, END_DATE_HARDCODED)
            filtered_reservations = filter_reservations_by_utm_label(reservations, UTM_LABEL_CONTAINS)

            if not filtered_reservations:
                print(f"No relevant reservations found for hotel {hotel_code} after filtering. Skipping CSV generation.")
                continue

            csv_data_rows = create_csv_data(filtered_reservations, current_hotel_commission)

            # Create CSV content in memory
            csv_buffer = io.StringIO()
            csv_writer = csv.writer(csv_buffer, delimiter=';') # Using semicolon as seen in "sep="
            for row in csv_data_rows:
                csv_writer.writerow(row)
            
            csv_content = csv_buffer.getvalue()
            csv_buffer.close()

            # Define CSV file name: ZIP_FILE_NAME (without .zip) _ hotel_code .csv
            base_zip_name = current_zip_file_name.replace(".zip", "")
            csv_file_name = f"{base_zip_name}_{hotel_code}.csv"
            
            zf.writestr(csv_file_name, csv_content)
            print(f"Added {csv_file_name} to ZIP.")

    # Save the ZIP file
    with open(current_zip_file_name, "wb") as f:
        f.write(zip_buffer.getvalue())
    zip_buffer.close()
    print(f"Successfully created ZIP file: {current_zip_file_name}")


if __name__ == "__main__":
    # This part is for local execution.
    # You might need to set up Application Default Credentials or other auth
    # for datastore_communicator and other Google Cloud client libraries to work.
    print("Starting Triptease Billing Report Generation...")
    
    # Example: For local testing, you might need to initialize parts of your app's context
    # if common_data_provider or datastore_communicator depend on it.
    # This is highly dependent on your project's structure.
    # For example, if 'Config.DEV' needs to be True for local datastore emulators:
    # try:
    #     from paraty import Config
    #     Config.DEV = True # Or some other setup
    # except ImportError:
    #     print("Could not import paraty.Config for local setup.")

    generate_report()
    print("Triptease Billing Report Generation Finished.") 