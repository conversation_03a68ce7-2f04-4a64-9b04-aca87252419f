import datetime
import requests
from requests.auth import H<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from built_tools_2_constants import BUILD_TOOLS_FUNCTIONS_PATH
from paraty.backups.backup_utils import save_binary_to_file, save_multiple_in_zip_file
from paraty.billing.billing_constants import B<PERSON><PERSON>ING_QUEUE, MANAGER_URL, BILLING_BUCKET, BILLS_FOLDER, EMAILS_ADMIN, EMAILS_PORTUGAL, EMAILS_LATAM
from paraty_commons_3 import queue_utils, email_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_hotel_metadata_by_application_id, get_all_valid_hotels
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.security_utils import get_secret
from paraty_commons_3.storage.storage_utils import get_all_files_in_bucket


def zip_and_send_bills(request):
    blobs = list(get_all_files_in_bucket(BILLING_BUCKET))

    expected_folders = ['all', 'callcenter', 'portugal', 'latam']
    today = str(datetime.date.today())

    result = {}

    for folder in expected_folders:

        expected_prefix = '%s/%s%s/%s/' % (BILLING_BUCKET, BILLS_FOLDER, today, folder)
        multiple_blobs = []
        for blob in blobs:
            if blob.id.startswith(expected_prefix):
                multiple_blobs.append(blob)

        zip_output_path = '%s%s/%s.zip' % (BILLS_FOLDER, today, folder)

        save_multiple_in_zip_file(BILLING_BUCKET, zip_output_path, multiple_blobs)
        result[folder] = 'https://storage.cloud.google.com/%s/%s' % (BILLING_BUCKET, zip_output_path)

    content_html = '''
        <a href="%s">Todas</a><br/>
        <a href="%s">Call Center</a><br/>
        <a href="%s">Portugal</a><br/>
        <a href="%s">Latam</a><br/>
        <p>Nota: Estos ficheros estarán disponibles sólo 60 días y requieren login para poder acceder</p>
    ''' % (result['all'], result['callcenter'], result['portugal'], result['latam'])

    email_utils.sendEmail(EMAILS_ADMIN, _previous_month_email_title(), '', content_html, sender='Facturación Paraty <<EMAIL>>')

    content_html = '''
            <a href="%s">Portugal</a><br/>
            <p>Nota: Estos ficheros estarán disponibles sólo 60 días y requieren login para poder acceder</p>
        ''' % (result['portugal'])

    email_utils.sendEmail(EMAILS_PORTUGAL, _previous_month_email_title(), '', content_html, sender='Facturación Paraty <<EMAIL>>')

    content_html = '''
            <a href="%s">Latam</a><br/>
            <p>Nota: Estos ficheros estarán disponibles sólo 60 días y requieren login para poder acceder</p>
        ''' % (result['latam'])

    email_utils.sendEmail(EMAILS_LATAM, _previous_month_email_title(), '', content_html, sender='Facturación Paraty <<EMAIL>>')

    return 'OK'


def _previous_month_email_title():
    first_day_of_last_month = (datetime.datetime.now() - datetime.timedelta(days=28)).replace(day=1)
    last_day_of_last_month = _last_day_of_month(first_day_of_last_month)

    start_date = first_day_of_last_month.strftime('%Y-%m-%d')
    end_date = last_day_of_last_month.strftime('%Y-%m-%d')

    return 'Facturas reservas (%s a %s) generadas el %s' % (start_date, end_date, str(datetime.date.today()))


def default_billing_for_previous_month(request):
    # Find start and end of previous month (note that we are sending bills as early as the 28th of the current month
    first_day_of_last_month = (datetime.datetime.now() - datetime.timedelta(days=20)).replace(day=1)
    last_day_of_last_month = _last_day_of_month(first_day_of_last_month)

    startDate = first_day_of_last_month.strftime('%Y-%m-%d')
    endDate = last_day_of_last_month.strftime('%Y-%m-%d')

    print('Date range: %s to %s' % (startDate, endDate))

    # Obtain the list of hotels
    hotels = get_all_hotels()
    hotels = [x for x in list(hotels.values()) if x.get('enabled')]  # Note that we are including also those not inProduction (in case it was forgotten)

    portugal_hotel_ids = _get_portugal_hotel_code()

    latam_hotel_ids = _get_latam_hotel_code()

    DOWNLOAD_BILL_URL = '%s/download_bill' % (BUILD_TOOLS_FUNCTIONS_PATH,)

    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    logging.info("Starting billing for %s hotels" % (len(hotels),))

    for hotel in hotels:
        try:
            using_departure = _hotel_uses_departure(hotel['applicationId'])
            use_sale_date = _hotel_uses_sale_date_for_billing(hotel['applicationId'])

            hotel_id = hotel['id']

            queue_utils.create_task_with_url_get_target(BILLING_QUEUE,
                                                        '%s?startDate=%s&endDate=%s&hotelId=%s&departure=%s&useSaleDate=%s&path=all' % (DOWNLOAD_BILL_URL, startDate, endDate, hotel_id, using_departure, use_sale_date),
                                                        name='%s__%s' % (hotel['applicationId'], timestamp))

            queue_utils.create_task_with_url_get_target(BILLING_QUEUE,
                                                        '%s?startDate=%s&endDate=%s&hotelId=%s&departure=%s&useSaleDate=%s&path=callcenter&source=Callcenter' % (
                                                            DOWNLOAD_BILL_URL, startDate, endDate, hotel_id, using_departure, use_sale_date),
                                                        name='%s__%s_call' % (hotel['applicationId'], timestamp))

            if hotel_id in portugal_hotel_ids:
                queue_utils.create_task_with_url_get_target(BILLING_QUEUE,
                                                            '%s?startDate=%s&endDate=%s&hotelId=%s&departure=%s&useSaleDate=%s&path=portugal' % (DOWNLOAD_BILL_URL, startDate, endDate, hotel_id, using_departure, use_sale_date),
                                                            name='%s__%s_portugal' % (hotel['applicationId'], timestamp))

            if hotel_id in latam_hotel_ids:
                queue_utils.create_task_with_url_get_target(BILLING_QUEUE,
                                                            '%s?startDate=%s&endDate=%s&hotelId=%s&departure=%s&useSaleDate=%s&path=latam' % (DOWNLOAD_BILL_URL, startDate, endDate, hotel_id, using_departure, use_sale_date),
                                                            name='%s__%s_latam' % (hotel['applicationId'], timestamp))

        except Exception as e:
            logging.error('Error creating task for hotel %s: %s' % (hotel['applicationId'], str(e)))
            raise e

    return 'OK'


def download_to_storage(file_url, bucket, destination_path):
    logging.info("Called: %s", file_url)

    billing_user, billing_password = get_secret('security-seeker', 'billing_manager_user_and_password').split(":")
    response = requests.get(file_url, auth=HTTPBasicAuth(billing_user, billing_password))

    if response.status_code == 200:
        data = response.content
        save_binary_to_file(bucket, data, destination_path)
    else:
        if response.status_code != 404:
            logging.warning("Error downloading file at download_to_storage %s: %s", file_url, response.status_code)
            raise Exception("Error downloading file at download_to_storage %s: %s" % (file_url, response.status_code))
        else:
            logging.info("No Bookings found at download_to_storage %s", file_url)

    return


def download_bill(request):
    start_date = request.values.get('startDate')
    end_date = request.values.get('endDate')
    hotel_id = request.values.get('hotelId')
    using_departure = request.values.get("departure", '').lower() == 'true'
    using_sale_date = request.values.get("useSaleDate", '').lower() == 'true'
    source_filter = request.values.get('source')
    path = request.values.get("path")

    hotel_application = get_entity('HotelApplication', int(hotel_id), 'admin-hotel')
    name = hotel_application.get('name')
    try:
        logging.info("Generating bill for %s (%s) from %s to %s" % (name, hotel_id, start_date, end_date))

        if using_departure:
            excel_url = MANAGER_URL + "/MainPage/excel?departureStartDate=%s&departureEndDate=%s&hotelId=%s" % (start_date, end_date, hotel_id)
        elif using_sale_date:
            excel_url = MANAGER_URL + "/MainPage/excel?startDate=%s&endDate=%s&hotelId=%s" % (start_date, end_date, hotel_id)
        else:
            excel_url = MANAGER_URL + "/MainPage/excel?entryStartDate=%s&entryEndDate=%s&hotelId=%s" % (start_date, end_date, hotel_id)

        if source_filter:
            excel_url += "&sourceFilter=%s" % source_filter

        destination_file = "%s.xls" % name.replace(":", "").replace(" ", "_").replace("*", "stars").replace("&", "")

        destination_folder = BILLS_FOLDER + str(datetime.date.today()) + "/" + path

        destination_path = destination_folder + "/" + destination_file
        download_to_storage(excel_url, BILLING_BUCKET, destination_path)

        return 'OK'

    except Exception as e:
        logging.exception("Problems when processing download_bill for %s (%s) from %s to %s" % (name, hotel_id, start_date, end_date))
        logging.exception(e)
        raise e


def get_user_by_name(user_name):
    return datastore_communicator.get_using_entity_and_params('ParatyUser', search_params=[('name', '=', user_name)], hotel_code='admin-hotel')


def _get_portugal_hotel_code():
    portugal_user = get_user_by_name('hotelesportugal')
    return portugal_user[0]['accesibleApplications']


def _get_latam_hotel_code():
    latam_user = get_user_by_name('Informe Paraty LATAM')
    return latam_user[0]['accesibleApplications']


def _last_day_of_month(any_day):
    next_month = any_day.replace(day=28) + datetime.timedelta(days=4)  # this will never fail
    return next_month - datetime.timedelta(days=next_month.day)


def _hotel_uses_departure(application_id):
    hotel_metadata = get_hotel_metadata_by_application_id(application_id)

    if not hotel_metadata:
        return False

    if not 'configurations' in hotel_metadata:
        return False

    configurations = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in hotel_metadata.get('configurations')}

    if 'departure' in configurations:
        return True
    else:
        return False


def _hotel_uses_sale_date_for_billing(application_id):
    hotel_metadata = get_hotel_metadata_by_application_id(application_id)

    if not hotel_metadata:
        return False

    if not 'configurations' in hotel_metadata:
        return False

    configurations = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in hotel_metadata.get('configurations')}

    if 'useSaleDate' in configurations:
        return True
    else:
        return False
