import datetime
import json
import logging
import pickle
import zlib

import requests
from google.cloud import ndb, datastore

from models.hotel_task_entry import HotelTaskEntry

from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_project_and_namespace, get_internal_url, get_all_valid_hotels

from google.cloud import scheduler_v1
from google.cloud import run_v2

from paraty_commons_3.session.model import UserSession

STEP_TIME = 40
MAX_AGE_DEFAULT = 86400
MAX_CONCURRENT = 20

RUN_JOB_NAME = "projects/build-tools-2/locations/europe-west1/jobs/job-process-cleaner"
SCHEDULER_JOB_NAME = "projects/build-tools-2/locations/europe-west1/jobs/clean-sessions-job"

result = {}


def get_session(hotel_code, session_id):

    domain = get_internal_url(hotel_code)
    read_session_url = (
        f"{domain}/utils?action=temporal_read_session&sid={session_id}"
    )
    response = requests.get(read_session_url)
    response.raise_for_status()
    return json.loads(response.text)


def get_session_old(hotel_code, session_id):
    project, namespace = get_hotel_project_and_namespace(hotel_code)
    client = ndb.Client(project=project, namespace=namespace)
    with client.context():
        currentSession = UserSession.get_by_id(session_id)
        if not currentSession:
            return None

    if currentSession and currentSession.compressedContent2:
        content = {}
        try:
            content = json.loads(json.loads(zlib.decompress(currentSession.compressedContent2)))
        except Exception as e:
            content = json.loads(zlib.decompress(currentSession.compressedContent2))
        return content

    if currentSession and currentSession.compressedContent:
        try:
            content = pickle.loads(zlib.decompress(currentSession.compressedContent))
            return content
        except Exception as e:
            content = json.loads(zlib.decompress(currentSession.compressedContent))
            return content

    if currentSession and currentSession.content2:
        return json.loads(currentSession.content2)

    if currentSession and currentSession.content and isinstance(currentSession.content, dict):
        return currentSession.content

    return {}


def _get_hotels_to_process():
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    valid_hotels = list(filter(lambda x: x.get('applicationId').find('test') < 0 and
                                         x.get('applicationId').find('demo') < 0, all_hotels))
    return valid_hotels, len(valid_hotels)


def clear_task_list():
    logging.info("Cleaning hotel task entries...")
    ds_client = datastore.Client()
    query = ds_client.query(kind="HotelTaskEntry")
    entities = list(query.fetch())
    ds_client.delete_multi(entities)


def create_task_hotel(hotel, task_index):
    with ndb.Client().context():
        hotel_task_entry = HotelTaskEntry()
        hotel_task_entry.task_index = task_index
        hotel_task_entry.hotel_code = hotel['applicationId']
        seconds = int((task_index / MAX_CONCURRENT) * STEP_TIME)
        hotel_task_entry.time_start = datetime.datetime.now() + datetime.timedelta(seconds=seconds)
        hotel_task_entry.extra_data = json.dumps(hotel)
        logging.info(f"{task_index} -> {hotel['applicationId']} -> {hotel_task_entry.time_start}")
        hotel_task_entry.put()


def cleanup_all_sessions_execute():

    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type, X-Auth-Token',
        'Access-Control-Max-Age': '3600'
    }

    all_valid_hotels = get_all_valid_hotels()

    hotels, count = _get_hotels_to_process()

    clear_task_list()
    for task_index, hotel in enumerate(hotels):
        create_task_hotel(hotel, task_index)

    logging.info("Preparing successfull to clean sessions!")

    client_run = run_v2.JobsClient()
    request = run_v2.GetJobRequest(name=RUN_JOB_NAME)
    response = client_run.get_job(request=request)
    response.template.task_count = count
    response.template.template.timeout = datetime.timedelta(hours=1)
    request = run_v2.UpdateJobRequest(job=response)
    operation = client_run.update_job(request=request)

    logging.info(operation)

    client_scheduler = scheduler_v1.CloudSchedulerClient()
    request_execute = scheduler_v1.RunJobRequest(name=SCHEDULER_JOB_NAME)
    operation = client_scheduler.run_job(request=request_execute)

    logging.info(operation)

    return "OK", 200, headers
