import datetime
import io
import json
import logging
import os
import pickle
import zlib

import six
from flask import request
from google.cloud import ndb
from google.appengine.ext import ndb as ndb_old

from booking_process.utils.booking.tempBookingModel import PersonalDetailsEncoder, PersonalDetails
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.email_utils import makeTraceback
from paraty_commons_3.session.model import UserSession as NewUserSession
from paraty_commons_3.datastore.datastore_communicator import get_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_project_and_namespace
from paraty_commons_3.redis.redis_communicator import build_redis_client_hotel

EUROPE_REDIS = "34.78.221.41"
AMERICA_REDIS = "34.67.168.188"

SESSION_EXPIRATION_TIME = 60 * 30  # 30 minutes


def custom_pickle_loads(data):
    try:
        # Python2 hotel-webs compatibility
        python2_encoding = 'latin1'
        loaded_element = CustomUnpickler(io.BytesIO(data), encoding=python2_encoding, python2_hw=True).load()
        if type(loaded_element) == str:
            # At some points sessions pickle are saved as string instead of objects
            loaded_element_bytes = loaded_element.encode(python2_encoding)
            return CustomUnpickler(io.BytesIO(loaded_element_bytes), encoding=python2_encoding, python2_hw=True).load()

        return loaded_element

    except Exception as e:
        try:
            # Python3 hotel-webs compatibility
            logging.info("Python3 session compatibility")
            return CustomUnpickler(io.BytesIO(data)).load()
        except Exception as e:
            traceback_message = makeTraceback()
            logging.error(f"Unable to unpickle data: {str(e)}\n{traceback_message}")
            return None


def read_session_hotel(sid=None, hotel_code=None):
    hotel_code = hotel_code or request.values.get('namespace')
    session_id = sid or request.values.get('sid')
    logging.info(f"Reading session {session_id} from hotel {hotel_code}")

    session_data = _get_session_object(session_id, hotel_code)

    if not session_data:
        logging.info("Session not found")
        return json.dumps({})

    _prepare_content_of_session_persistance(session_data)

    target_content = session_data['content']
    max_attempts = 4
    tries_counter = 0

    while isinstance(target_content, bytes) and tries_counter < max_attempts:
        target_content = custom_pickle_loads(target_content)
        tries_counter += 1

    return json.dumps(target_content, cls=PersonalDetailsEncoder)


def write_session_hotel(sid=None, hotel_code=None, data=None):
    hotel_code = hotel_code or request.values.get('namespace')
    session_id = sid or request.values.get('sid')
    source = request.values.get('source')

    logging.info(f"Writing session of hotel {hotel_code} with sid {session_id}")
    logging.info("Request source: %s", source)
    logging.info("Data to be upddated: %s", data)

    if not source:
        logging.warning("No source for session write, and it's mandatory")
        return False, 'Mandatory source not provided'

    session_object = _get_session_object(session_id, hotel_code)

    _prepare_content_of_session_persistance(session_object)

    actual_session_data = session_object['content']
    if isinstance(actual_session_data, bytes):
        actual_session_data = custom_pickle_loads(actual_session_data)

        # Repeated code due to old bug, remove this block in 5 days from this commit, dont be scare, remove this
        if isinstance(actual_session_data, bytes):
            actual_session_data = custom_pickle_loads(actual_session_data)

    actual_session_data.update(data)

    # Save history
    actual_session_data.setdefault('session_write_history', []).append({
        'path': 'build-tools2/write_session_from_hotel_handler',
        'source': source,
        'utc_timestamp': datetime.datetime.utcnow().isoformat()
    })

    session_object.content = actual_session_data
    session_object.content2 = None

    # Save at redis
    _write_session_to_redis(session_id, session_object, hotel_code)

    try:
        session_dict = {key_element: session_object[key_element] for key_element in list(session_object._properties.keys())}
    except Exception as e:
        logging.warning("Error getting session dict: %s", e)
        session_dict = dict(session_object)

    try:
        logging.info("Saving session to datastore")
        session_dict['content'] = pickle.dumps(actual_session_data)
        datastore_communicator.save_to_datastore('UserSession', session_id, session_dict, hotel_code=hotel_code, exclude_from_indexes=['compressedContent', 'compressedContent2'])
    except Exception as e:
        logging.warning("Error saving session to datastore: %s", e)
        _compress_and_save_session(session_dict, session_id, hotel_code)

    logging.info("Session saved")
    return True, 'Correctly saved'


def _compress_and_save_session(session_dict, session_id, hotel_code):
    if type(session_dict['content']) is bytes:
        session_dict['content'] = pickle.loads(session_dict['content'])

    session_dict['content']['compressed'] = True
    valid_content = session_dict['content']

    session_dict['compressedContent'] = zlib.compress(pickle.dumps(valid_content, protocol=2))
    session_dict['content'] = None
    datastore_communicator.save_to_datastore('UserSession', session_id, session_dict, hotel_code=hotel_code, exclude_from_indexes=['compressedContent', 'compressedContent2'])


def _get_session_object(sid, hotel_code):
    session_data = _read_session_from_redis(sid, hotel_code)
    if session_data is dict:
        model_session_data = NewUserSession()
        model_session_data.content = session_data
        model_session_data.key = ndb.Key('UserSession', sid)
        session_data = model_session_data

    if not session_data:
        logging.info("Getting session from datastore, not found in redis")
        project, namespace = get_hotel_project_and_namespace(hotel_code)
        client = ndb.Client(project=project, namespace=namespace)
        with client.context():
            session_data = get_entity('UserSession', sid, hotel_code=hotel_code)

    return session_data


def _read_session_from_redis(sid, hotel_code):
    redis_sid = "%s_@_%s" % (hotel_code, sid)
    sid_key = 'session_data_%s' % redis_sid

    redis_instance = build_redis_client_hotel(hotel_code)
    serialized_object = redis_instance.get(sid_key)

    if serialized_object is None:
        logging.warning("Session not found in redis: %s", sid)
        return None

    return custom_pickle_loads(serialized_object)


def _write_session_to_redis(sid, session_object, hotel_code):
    logging.info("Saving session to redis")
    redis_sid = "%s_@_%s" % (hotel_code, sid)
    sid_key = 'session_data_%s' % redis_sid

    redis_client = build_redis_client_hotel(hotel_code)
    serialized_object = pickle.dumps(session_object)
    redis_client.set(sid_key, serialized_object, SESSION_EXPIRATION_TIME)


class Python2PickleProperty(ndb_old.BlobProperty):
    def _from_base_type(self, value):
        if isinstance(value, dict):
            return value
        return CustomUnpickler(io.BytesIO(value), encoding='latin1', python2_hw=True).load()

    def _to_base_type(self, value):
        if os.environ.get('NDB_USE_CROSS_COMPATIBLE_PICKLE_PROTOCOL', False):
            protocol = 2
        else:
            protocol = pickle.HIGHEST_PROTOCOL

        return pickle.dumps(value, protocol)


class UserSession(ndb_old.Model):
    # Default
    content = Python2PickleProperty(compressed=False)
    timestamp = ndb_old.DateTimeProperty(auto_now_add=True)
    content2 = ndb_old.BlobProperty(indexed=False)
    compressedContent = ndb_old.BlobProperty(indexed=False)
    compressedContent2 = ndb_old.BlobProperty(indexed=False)

    def __getitem__(self, item):
        return getattr(self, item)

    def __setitem__(self, key, value):
        setattr(self, key, value)


class CustomUnpickler(pickle.Unpickler):
    def __init__(self, *args, **kwargs):
        if kwargs.get('python2_hw'):
            self.target_user_session_model = UserSession
            kwargs.pop('python2_hw')
        else:
            self.target_user_session_model = NewUserSession

        super().__init__(*args, **kwargs)

    def find_class(self, module, name):
        if module == "booking_process.utils.session.session_manager" and name == "UserSession":
            return self.target_user_session_model

        if (module == 'booking_process.utils.booking.tempBookingModel' or module == 'booking_process.models.personal_details_model') and name == 'PersonalDetails':
            return PersonalDetails

        if module == "paraty.util.session.session_manager" and name == "UserSession":
            return self.target_user_session_model

        if 'django.utils.safestring' in module and name == "SafeString":
            return str

        if 'google.appengine.api.datastore_types' in module and name == "Text":
            from google.appengine.ext.db import Text
            return Text

        if 'google.appengine._internal.django.utils.safestring' in module and 'SafeUnicode' in name:
            class SafeTextOld(six.text_type):
                def __dict__(self):
                    return {}

                def __reduce__(self):
                    return (str, (super().__str__(),))

            return SafeTextOld

        return super().find_class(module, name)


def _prepare_content_of_session_persistance(currentSession):
    if currentSession and not currentSession['content'] and currentSession['content2']:
        recovered_content = custom_pickle_loads(currentSession['content2'])
        if recovered_content and not recovered_content is dict:
            try:
                recovered_content = json.loads(recovered_content)
            except Exception as e:
                recovered_content = recovered_content

        currentSession['content'] = recovered_content
        currentSession['content2'] = None

    # Sometimes We need to compress things if size is too large
    elif currentSession and not currentSession['content'] and not currentSession['content2'] and currentSession['compressedContent']:
        currentSession['content'] = custom_pickle_loads(zlib.decompress(currentSession['compressedContent']))
        currentSession['compressedContent'] = None

    elif currentSession and not currentSession['content'] and not currentSession['content2'] and currentSession['compressedContent2']:
        try:
            currentSession['content'] = json.loads(json.loads(zlib.decompress(currentSession['compressedContent2'])))
        except Exception as e:
            currentSession['content'] = json.loads(zlib.decompress(currentSession['compressedContent2']))
        currentSession['compressedContent'] = None
