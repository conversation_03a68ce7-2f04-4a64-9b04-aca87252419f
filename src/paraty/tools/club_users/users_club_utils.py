import logging

from paraty_commons_3 import security_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

CLUB_USER_MODEL = 'UserClub'


def get_hotels_with_club_users() -> list:

    result = []
    all_hotels = get_all_hotels()
    for hotel in all_hotels.values():
        try:
            users = datastore_communicator.get_using_entity_and_params(CLUB_USER_MODEL, search_params=[], keys_only=True, hotel_code=hotel['applicationId'])
            if users:
                logging.info(hotel['applicationId'])
                result.append(hotel['applicationId'])
        except Exception as e:
            logging.error("Error at: %s", hotel['applicationId'])

    return result


def encrypt_password(hotel_code):

    all_club_users = datastore_communicator.get_using_entity_and_params(CLUB_USER_MODEL, search_params=[], hotel_code=hotel_code)

    logging.info("Found %s clients", len(all_club_users))
    affected_entities = []
    for x in all_club_users:

        # if not 'paraty' in x['email']:
        #     continue

        if not 'password_hash' in x and 'password' in x:
            password = x['password']
            hashed_password = security_utils.hash_password(password)
            x['password_hash'] = hashed_password

            # logging.info("Changing %s", x['email'])
            affected_entities.append(x)

        # x['password'] = 'veryfuckinglongpasswordtoberemoved13843038' # Random Text to make sure it is not valid when login

    logging.info("Saving %s clients", len(affected_entities))

    datastore_communicator.save_entity_multi(all_club_users, hotel_code=hotel_code)


HOTELES_CON_USER_CLUB = ['hotel-puentereal', 'hotel-rosamar', 'flashhotelbenidorm', 'ona-corporativa', 'holiday-palace', 'daguisa-goldenfenix', 'acebuchal', 'best-serenade', 'ap-adriana', 'ipv-backup', 'holiday-corporativa', 'granpalas-experience', 'holiday-polynesia', 'vik-corporativa', 'dreamland-corpo', 'tarifa-lances', 'best-corporate', 'best-osuna', 'best-frontmaritim', 'blaumar-acacias', 'ona-bahia-blanca', 'demo5', 'best-autohogar', 'onhotel', 'test3', 'villa-flamenca', 'playagolf-islantilla', 'hacienda-alamo', 'best-4barcelona', 'la-barracuda', 'landmar-gigantes', 'ap-corporate', 'hotel-esmeralda', 'hacienda-apartamentos', 'bahiablanca', 'neptuno-roquetas', 'vime-marbella', 'matalascanas', 'toboso-corporativa', 'blaumar-blaumar', 'bg-corporativa', 'ohtels-europe', 'ohtels-corporate', 'ipv-palace']


if __name__ == '__main__':
    for hotel in HOTELES_CON_USER_CLUB[18:]:
        print("")
        print(hotel)
        encrypt_password(hotel)
    # print(get_hotels_with_club_users())