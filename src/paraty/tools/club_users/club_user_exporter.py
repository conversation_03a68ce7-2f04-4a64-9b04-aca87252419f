import json
import logging

from paraty.tools.club_users.users_club_utils import CLUB_USER_MODEL
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def _create_users_export(hotel_code):
    all_club_users = datastore_communicator.get_using_entity_and_params(CLUB_USER_MODEL, search_params=[('timestamp', '>', '2023-02')], hotel_code=hotel_code)
    logging.info("Found %s clients", len(all_club_users))

    if len(all_club_users) == 0:
        logging.info("No users found for hotel: %s", hotel_code)
        return

    # Create json file with all users
    with open('./exports/{}.json'.format(hotel_code), 'w') as f:
        f.write('[\n')
        for x in all_club_users:

            str_entity = json.dumps(x)
            json_entity = json.loads(str_entity)
            json_entity.pop('password', None)
            json_entity.pop('password_hash', None)
            json_entity.pop('fakebirthday', None)

            ready_entity = json.dumps(json_entity)

            f.write(f'{ready_entity},\n')

        f.write(']')



if __name__ == '__main__':
    all_hotels = get_all_valid_hotels()
    valid_hotels = [x for x in all_hotels if x['applicationId'].startswith('best-')]
    for hotel in valid_hotels:
        print('Processing hotel: {}'.format(hotel['applicationId']))
        _create_users_export(hotel['applicationId'])

    print('Done!')