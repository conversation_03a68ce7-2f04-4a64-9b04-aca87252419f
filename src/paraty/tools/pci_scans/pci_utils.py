import csv

import pydig

VALID_DNS = ('ghs.googlehosted.com.', '*************', '*************', '*************', 'ghs.google.com.', '***************')

def read_csv_file(file_path):
    print("Reading file: " + file_path)

    # Read csv file, line by line
    with open(file_path, 'r') as csv_file:
        reader = csv_file.readlines()
        for i, line in enumerate(reader[1:]):

            print(f"Processing line {i} of {len(reader)}")

            domain = line.split(",")[1]
            target = get_dns_target(domain)
            if not target in VALID_DNS:
                print(f"FAILED: {domain} -> {target}")


def get_dns_target(domain):

    print("Getting DNS target for: " + domain)

    # Clean domain
    domain = domain.strip().replace("https://", "").replace("\n", "").replace("com/", "com")

    result = pydig.query(domain, 'A')
    # if not result:
    #     result = pydig.query(domain, 'A')

    if result[0] in VALID_DNS:
        return result[0]

    return result[-1]


if __name__ == '__main__':
    read_csv_file('Paratytech_listado_hoteles_10012022.csv')