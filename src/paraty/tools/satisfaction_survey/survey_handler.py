import base64
import logging
import time
import urllib.parse

from postmarker.core import PostmarkClient

from paraty.tools.satisfaction_survey.survey_utils import get_survey_target_emails, get_survey_translations, \
    get_survey_template, download_survey
from paraty_commons_3 import queue_utils
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_account_manager

BCC_EMAILS = ['<EMAIL>', '<EMAIL>']

ERRORS = {}

AUTH_KEY = 'Bearer ************************************'


def send_survey_form_to_all_hotels(request):
    """
    Function that will send the survey form to all the hotels that are in production
    Available params:
        - hotel_code: if this param is present, the survey will be sent only to this hotel
        - custom_email: if this param is present, the survey will be sent to this email
        - custom_account: if this param is present, the survey will be sent to this account manager
        - custom_dates: if this param is present, the survey will be between these dates
    """

    if request.values.get('process_queue_element'):
        try:
            _process_queue_element(request)
        except Exception as e:
            logging.error('Error processing survey: %s' % e)

        return 'ok'

    if request and request.headers.get('Authorization') != AUTH_KEY:
        return 'Unauthorized'

    specific_hotel = request and request.values.get('hotel_code')
    hotel_contains = request and request.values.get('hotel_contains')
    custom_email = request and request.values.get('custom_email')
    custom_account = request and request.values.get('custom_account')
    custom_dates = request and request.values.get('custom_dates')  # 01-01-2021;01-02-2021

    # DEV
    # custom_account = '<EMAIL>'
    # custom_email = '<EMAIL>'
    # specific_hotel = 'sno-formigal'
    # hotel_contains = 'marmoris'

    all_hotels = get_all_hotels()

    process_url = 'https://send-survey-to-all-hotels-nv3f7s7soa-ew.a.run.app'

    for hotel_code, hotel_data in all_hotels.items():
        if not hotel_data.get('inProduction') or not hotel_data.get('enabled'):
            continue

        if specific_hotel and hotel_code != specific_hotel:
            continue

        if hotel_contains and hotel_code.find(hotel_contains) == -1:
            continue

        params = {
            'hotel_code': hotel_code,
            'hotel_name': hotel_data.get('name'),
            'process_queue_element': True
        }

        if custom_email:
            params['custom_email'] = custom_email

        if custom_account:
            params['custom_account'] = custom_account

        if custom_dates:
            params['custom_dates'] = custom_dates

        params_encoded = urllib.parse.urlencode(params)

        queue_utils.create_task_with_url_get_target(
            'survey-reports',
            '%s?%s' % (process_url, params_encoded),
            name='survey_%s_%s' % (hotel_code, time.time())
        )

    # notify_errors()

    return 'ok'


def _process_queue_element(request):
    hotel_code = request.values.get('hotel_code')
    hotel_name = request.values.get('hotel_name')
    custom_email = request.values.get('custom_email')
    custom_account = request.values.get('custom_account')
    custom_dates = request.values.get('custom_dates')  # 01-01-2021;01-02-2021

    _process_hotel_survey(hotel_code, hotel_name, custom_email, custom_account, custom_dates)

    return 'ok'


def _process_hotel_survey(hotel_code, hotel_name, custom_email=None, custom_account=None, custom_dates=None):
    try:
        start = time.time()

        account_email = get_account_manager(hotel_code)
        if not account_email:
            account_email = '<EMAIL>'

        if custom_account:
            account_email = custom_account

        survey_file = download_survey(hotel_code, custom_dates=custom_dates)

        if not survey_file:
            ERRORS.setdefault(account_email, {}).setdefault('missing_answers', []).append(hotel_name)
            return

        account_email = account_email.replace(';', ',')
        logging.info('Account email: ' + account_email)

        target_emails = get_survey_target_emails(hotel_code)
        if not target_emails:
            ERRORS.setdefault(account_email, {}).setdefault('missing_emails', []).append(hotel_name)
            return

        translations = get_survey_translations(hotel_code)
        target_html = get_survey_template(translations)

        csv_file = download_survey(hotel_code, csv=True, custom_dates=custom_dates)

        if 'survey_reports' in str(survey_file):
            target_extension = '.html'
            mime_type = 'text/html'
        else:
            target_extension = '.pdf'
            mime_type = 'application/octet-stream'

        survey_file = base64.b64encode(survey_file).decode('utf-8')
        csv_file = base64.b64encode(csv_file).decode('utf-8')

        email_files = [(hotel_name + target_extension, survey_file, mime_type)]
        if csv_file:
            email_files.append((hotel_name + ".csv", csv_file, "text/csv"))

        email_sender = '<EMAIL>'
        # target_emails = ['<EMAIL>']

        if custom_email:
            target_emails = [custom_email]

        logging.info('Sending survey to hotel: ' + hotel_code)
        logging.info('Target emails: ' + str(target_emails))

        target_suject = f"{translations['T_survey_title']} - {hotel_name}"
        blind_copy_emails = [account_email] + BCC_EMAILS

        postmark = PostmarkClient(server_token='************************************')
        postmark.emails.send(
            From=email_sender,
            To=','.join(target_emails),
            Bcc=','.join(blind_copy_emails),
            Subject=target_suject,
            HtmlBody=target_html,
            ReplyTo=account_email,
            Attachments=email_files
        )

    except Exception as e:
        ERRORS.setdefault('<EMAIL>', {}).setdefault('script_error', []).append(hotel_name)
        # Print traceback
        logging.info(make_traceback())
        logging.info(e)
        logging.warning('Error sending survey to hotel: ' + hotel_code)

    end = time.time()
    logging.info("Time taken is {} seconds".format(end - start))


def notify_errors():
    """script_error, missing_emails, missing_answers"""
    if ERRORS:
        for email, errors in ERRORS.items():
            email_body = ''

            if errors.get('script_error'):
                email_body += '<h4>Error in script</h4>  <br>'
                for hotel in errors.get('script_error'):
                    email_body += hotel + '<br>'

                email_body += '<br><br><br>'

            if errors.get('missing_emails'):
                email_body += '<h4>Missing emails</h4>  <br>'
                for hotel in errors.get('missing_emails'):
                    email_body += hotel + '<br>'

                email_body += '<br><br><br>'

            if errors.get('missing_answers'):
                email_body += '<h4>Missing answers</h4>  <br>'
                for hotel in errors.get('missing_answers'):
                    email_body += hotel + '<br>'

                email_body += '<br><br><br>'

            postmark = PostmarkClient(server_token='************************************')
            postmark.emails.send(
                From='Paraty Tech<<EMAIL>>',
                # To=email,
                To='<EMAIL>',
                Cc='<EMAIL>',
                Subject='Cuestionarios de satisfacción no enviados [Posible revisión necesaria]',
                HtmlBody=email_body,
                ReplyTo='<EMAIL>'
            )


if __name__ == '__main__':
    send_survey_form_to_all_hotels(None)
