import datetime
import io
import json
import os

import requests
from flask import render_template_string
from jinja2 import Environment

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_internal_url
from paraty_commons_3.language_utils import SPANISH


def get_survey_target_emails(hotel_code):
    survey_target_emails = get_hotel_advance_config_value(hotel_code, 'Satisfaction survey email')
    if not survey_target_emails:
        return []

    return survey_target_emails.replace(';', ',').split(',')


def get_survey_default_language(hotel_code):
    custom_language = get_hotel_advance_config_value(hotel_code, 'Language Management')
    if not custom_language:
        return SPANISH

    return custom_language


def get_survey_translations(hotel_code):
    language_management = get_survey_default_language(hotel_code)
    general_dicts_translates = open(os.path.dirname(os.path.abspath(__file__)) + '/assets/general_translates.json', 'r').read()
    general_dicts_translates = json.loads(general_dicts_translates)
    target_language_dict = general_dicts_translates.get(language_management, 'SPANISH')

    return target_language_dict


def get_survey_template(context):
    email_template_data = open(os.path.dirname(os.path.abspath(__file__)) + '/assets/emailhotel.html', 'r').read()
    template = Environment().from_string(email_template_data)
    return template.render(**context)


def download_survey(hotel_code, custom_dates=None, csv=False):
    now = datetime.datetime.now()
    survey_report_year = now.year
    survey_report_month = now.month - 1

    survey_report_month_numbers = 1

    # Custom dates search
    if custom_dates and custom_dates == 'actual_month':
        survey_report_month = now.month

    elif custom_dates:
        start_date, end_date = custom_dates.split(";")
        start_datetime = datetime.datetime.strptime(start_date, '%d-%m-%Y')
        end_datetime = datetime.datetime.strptime(end_date, '%d-%m-%Y')

        survey_report_year = start_datetime.year
        survey_report_month = start_datetime.month
        survey_report_month_numbers = end_datetime.month - start_datetime.month
        if survey_report_month_numbers < 0:
            survey_report_month_numbers = survey_report_month_numbers + 12

    if survey_report_month == 0:
        survey_report_month = 12
        survey_report_year = survey_report_year - 1

    survey_route = "/survey/report?year={}&month={}&number={}".format(survey_report_year, survey_report_month,
                                                                      survey_report_month_numbers)
    if csv:
        survey_route = survey_route + '&csv=true'

    hotel_url = get_internal_url(hotel_code)

    survey_url = hotel_url + survey_route
    print(survey_url)

    if hotel_url:
        try:
            survey_response = requests.get(survey_url)
            if survey_response.status_code == 404:
                return

            # return survey_response.text
            return survey_response.content
        except Exception as e:
            print(e)
            return False