from google.cloud import monitoring_v3
from google.cloud.monitoring_v3 import NotificationChannel, AlertPolicy

from paraty.tools.alerts.project_list import PROJECTS

alert_policy_client = monitoring_v3.AlertPolicyServiceClient()
notification_channel_client = monitoring_v3.NotificationChannelServiceClient()
metric_client = monitoring_v3.MetricServiceClient()

ERROR_COUNT = 20
DURATION_IN_MINUTES = 30
DURATION_IN_SECONDS = DURATION_IN_MINUTES * 60
CHANNEL_NAME = "Frontend"
ALERT_POLICY_NAME = "Error Count Alert"
EMAIL_ADDRESSES = "<EMAIL>"

CHANNEL_BODY = {
    "display_name": CHANNEL_NAME,
    "type": "email",
    "labels": {
        "email_address": EMAIL_ADDRESSES
    }
}


def create_project_alerts():
    for project_id in PROJECTS:
        parent = f"projects/{project_id}"
        channel_id = _get_notification_channel_id(parent)
        _create_policy(project_id, parent, channel_id)


def _get_notification_channel_id(parent):
    channels = notification_channel_client.list_notification_channels(request={"name": parent})

    created_channel = None
    for channel in channels:
        if channel.display_name == CHANNEL_NAME:
            created_channel = channel
            break

    if created_channel:
        print(f"Notification channel already created for {parent}")
    else:
        created_channel = notification_channel_client.create_notification_channel(name=parent, notification_channel=NotificationChannel(CHANNEL_BODY))

    return created_channel.name.split('/')[-1]


def _create_policy(project_id, parent, channel_id):
    alert_policies = alert_policy_client.list_alert_policies(name=parent)
    for alert_policy in alert_policies:
        if alert_policy.display_name == ALERT_POLICY_NAME:
            print(f"Alert policy already created for {project_id}")
            return

    policy = {
        "display_name": ALERT_POLICY_NAME,
        "documentation": {
            "content": f"More than {ERROR_COUNT} errors have occurred in {DURATION_IN_MINUTES} minutes in {project_id} logs.",
            "mime_type": "text/markdown"
        },
        "conditions": [
            {
                "display_name": "logging/user/errors [SUM]",
                "condition_threshold": {
                    "filter": f'resource.type="gae_app" metric.labels.severity="ERROR" metric.type="logging.googleapis.com/log_entry_count"',
                    "comparison": 'COMPARISON_GT',
                    "threshold_value": ERROR_COUNT,
                    "duration": f"{DURATION_IN_SECONDS}s",
                    "trigger": {
                        "count": 1
                    },
                    "aggregations": [
                        {
                            "alignment_period": f"{DURATION_IN_SECONDS}s",
                            "per_series_aligner": "ALIGN_DELTA",
                            "cross_series_reducer": "REDUCE_SUM"
                        }
                    ]
                }
            }
        ],
        "combiner": "OR",
        "notification_channels": [f"projects/{project_id}/notificationChannels/{channel_id}"],
    }

    created_policy = alert_policy_client.create_alert_policy(name=parent, alert_policy=AlertPolicy(policy))
    print("Created alert policy:", created_policy.name)


if __name__ == '__main__':
    create_project_alerts()
