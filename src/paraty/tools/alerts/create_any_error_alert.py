from google.cloud import monitoring_v3
from google.cloud.monitoring_v3 import NotificationChannel, AlertPolicy

from paraty.tools.alerts.project_list import PROJECTS

alert_policy_client = monitoring_v3.AlertPolicyServiceClient()
notification_channel_client = monitoring_v3.NotificationChannelServiceClient()
metric_client = monitoring_v3.MetricServiceClient()

ERROR_COUNT = 20
DURATION_IN_MINUTES = 30
DURATION_IN_SECONDS = DURATION_IN_MINUTES * 60
CHANNEL_NAME = "Bugseeker"
ALERT_POLICY_NAME = "Error notification"
WEBHOOK_URL = "https://europe-west1-assistant-seeker.cloudfunctions.net/google_alert"

CHANNEL_BODY = {
    "display_name": CHANNEL_NAME,
    "type": "webhook_tokenauth",
    "labels": {
        "url": WEBHOOK_URL
    }
}


def create_project_alerts(project_id):
    
  
    parent = f"projects/{project_id}"
    channel_id = _get_notification_channel_id(parent)

    try:
        _create_policy(project_id, parent, channel_id)
    except Exception as e:
        print(f"Error creating {parent}")


def _get_notification_channel_id(parent):
    channels = notification_channel_client.list_notification_channels(request={"name": parent})

    created_channel = None
    for channel in channels:
        if channel.display_name == CHANNEL_NAME:
            created_channel = channel
            break

    if created_channel:
        print(f"Notification channel already created for {parent}")
    else:
        created_channel = notification_channel_client.create_notification_channel(name=parent, notification_channel=NotificationChannel(CHANNEL_BODY))

    return created_channel.name.split('/')[-1]


def _create_policy(project_id, parent, channel_id):
    alert_policies = alert_policy_client.list_alert_policies(name=parent)
    for alert_policy in alert_policies:
        if alert_policy.display_name == ALERT_POLICY_NAME:
            print(f"Alert policy already created for {project_id}")
            return

    policy = {
        "display_name": ALERT_POLICY_NAME,
        "documentation": {
            "content": f"An error have occurred in in {project_id} logs.",
            "mime_type": "text/markdown"
        },
        "conditions": [
            {
                "display_name": "Log match condition",
                "condition_matched_log": {
                    "filter": 'severity>=ERROR',
                    "label_extractors": {
                        "trace1": 'REGEXP_EXTRACT(labels."appengine.googleapis.com/trace_id", "(.*)")',
                        "trace": 'REGEXP_EXTRACT(trace, "(.*)")',
                        "protopayloadtraceid": 'REGEXP_EXTRACT(protoPayload.traceId, "(.*)")',
                        "jsontrace": 'REGEXP_EXTRACT(jsonPayload.trace, "(.*)")'
                    }
                }
            }
        ],
        "alert_strategy": {
            "notification_rate_limit": {
                "period": "300s"
            },
            "auto_close": "604800s"
        },
        "combiner": "OR",
        "notification_channels": [f"projects/{project_id}/notificationChannels/{channel_id}"]
    }

    created_policy = alert_policy_client.create_alert_policy(name=parent, alert_policy=AlertPolicy(policy))
    print(f"Created alert policy: {created_policy.name}")


def update_channels(parent):
    channels = notification_channel_client.list_notification_channels(request={"name": parent})

    for channel in channels:
        if channel.display_name == CHANNEL_NAME:
            channel.labels.update({
                "url": WEBHOOK_URL
            })
            notification_channel_client.update_notification_channel(notification_channel=channel)
            print(f"Channel updated")
            return

    print(f"No channel found")


if __name__ == '__main__':
    create_project_alerts()

    # for project_id in PROJECTS:
    #     parent = f"projects/{project_id}"
    #     try:
    #         update_channels(parent)
    #     except:
    #         print(f"Error updating {parent}")
