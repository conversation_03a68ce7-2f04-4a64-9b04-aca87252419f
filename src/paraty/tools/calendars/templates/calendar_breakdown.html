<!-- Insert CDN link to Bootstrap 5.2.3 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>

<a href="{{ results_url }}" target="_blank" style="
    font-size: 12px;
    width: 87%;
    margin: 30px auto;
    display: block;
    overflow: hidden;
">{{ results_url }}</a>
<br>
<!-- Create a filters form -->
<div class="container">
    <div class="filters card">
        <div class="card-header">
            <h3 class="panel-title">Filters</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-sm">
                    <label for="date_filter">Filter by date</label>
                    <br>
                    <input id="date_filter" type="date" name="day" onchange="date_filter(this)" />
                </div>
                <div class="col-sm">
                    <label for="room_filter">Filter by rooms</label><br>
                    <input id="room_filter" type="text" name="room" onkeydown="room_filter(this)" onfocusout="room_filter(this)" />
                </div>
                <div class="col-sm">
                    <label for="rate_filter">Filter by rate</label><br>
                    <input id="rate_filter" type="text" name="rate" onkeydown="rate_filter(this)" onfocusout="rate_filter(this)" />
                </div>
            </div>
            <br>
            <button class="btn btn-info" type="button" style="cursor: pointer;" onclick="reset_filters()">Reset filters</button>
        </div>
    </div>
</div>

<br><br>

<div class="container">
    {% for date, day_results in results.items() %}
        <div class="day_wrapper card" data-day="{{ date }}">
            <div class="card-header">
                <h3 class="panel-title">Day {{ date }}</h3>
            </div>
            <div class="card-body">
                {% for room_name, room_results in day_results.items() %}
                    <div class="room_wrapper card" data-room="{{ room_name }}">
                        <div class="card-header">
                            <h3 class="panel-title">{{ room_name }}</h3>
                        </div>
                        {% for rate_name, rate_results in room_results.items() %}
                            <table class="table rates_wrapper" style="table-layout: fixed;" data-rate="{{ rate_name }}">
                                <thead>
                                    <tr>
                                        <th colspan="6">{{ rate_name }}</th>
                                    </tr>
                                    <tr>
                                        <th>Board</th>
                                        <th>Price</th>
                                        <th>Price with discount</th>
                                        <th>Min Stay</th>
                                        <th>Max Stay</th>
                                        <th>Promotions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for board_name, board_results in rate_results.items() %}
                                    <tr>
                                        <td>{{ board_name }}</td>
                                        <td>{{ board_results.price }}</td>
                                        <td>{{ board_results.price_with_discount }}</td>
                                        <td>{{ board_results.min_stay }}</td>
                                        <td>{{ board_results.max_stay }}</td>
                                        <td>{{ board_results.promotions }}</td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endfor %}
</div>


<script>
    var date_value, room_value, rate_value, board_value;

    function date_filter(element) {
        // Transform selected value yyyy-mm-dd to dd/mm/yyyy
        let date = new Date(element.value);
        let day = date.getDate();
        let month = date.getMonth() + 1;
        let year = date.getFullYear();

        if (day < 10) {
            day = "0" + day;
        }

        if (month < 10) {
            month = "0" + month;
        }

        date_value = day + "/" + month + "/" + year;
        main_filter();
    }

    function room_filter(element) {
        room_value = element.value;
        main_filter();
    }

    function rate_filter(element) {
        rate_value = element.value;
        main_filter();
    }

    function reset_filters() {
        date_value = null;
        room_value = null;
        rate_value = null;
        board_value = null;

        document.getElementById("date_filter").value = "";
        document.getElementById("room_filter").value = "";
        document.getElementById("rate_filter").value = "";

        main_filter();
    }

    function main_filter() {
        // DATES FILTERS
        if (date_value) {
            console.log(date_value);
            let all_days = document.getElementsByClassName("day_wrapper");
            for (let i = 0; i < all_days.length; i++) {
                let day = all_days[i];
                let day_data = day.getAttribute("data-day");
                if (day_data === date_value) {
                    day.style.display = "block";
                } else {
                    day.style.display = "none";
                }
            }
        } else {
            let all_days = document.getElementsByClassName("day_wrapper");
            for (let i = 0; i < all_days.length; i++) {
                let day = all_days[i];
                day.style.display = "block";
            }
        }

        // ROOMS FILTERS
        if (room_value) {
            let all_rooms = document.getElementsByClassName("room_wrapper");
            for (let i = 0; i < all_rooms.length; i++) {
                let room = all_rooms[i];
                let room_data = room.getAttribute("data-room");
                let regex = new RegExp(room_value, "i");
                if (regex.test(room_data)) {
                    room.style.display = "block";
                } else {
                    room.style.display = "none";
                }
            }
        } else {
            let all_rooms = document.getElementsByClassName("room_wrapper");
            for (let i = 0; i < all_rooms.length; i++) {
                let room = all_rooms[i];
                room.style.display = "block";
            }
        }

        // RATES FILTERS
        if (rate_value) {
            let all_rates = document.getElementsByClassName("rates_wrapper");
            for (let i = 0; i < all_rates.length; i++) {
                let rate = all_rates[i];
                let rate_data = rate.getAttribute("data-rate");
                let regex = new RegExp(rate_value, "i");
                if (regex.test(rate_data)) {
                    rate.style.display = "table";
                } else {
                    rate.style.display = "none";
                }
            }
        } else {
            let all_rates = document.getElementsByClassName("rates_wrapper");
            for (let i = 0; i < all_rates.length; i++) {
                let rate = all_rates[i];
                rate.style.display = "table";
            }
        }
    }
</script>