import datetime, requests, os
import json

import htmlmin
from jinja2 import Environment, FileSystemLoader, select_autoescape

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item, get_rooms_of_hotel, \
    get_rates_of_hotel, get_boards_of_hotel, get_promotions_of_hotel, get_price_increase_of_hotel, \
    get_multirates_of_hotel, get_hotel_web_config_item

current_dir = os.path.dirname(os.path.abspath(__file__))

env = Environment(
    loader=FileSystemLoader(os.path.join(current_dir, 'templates')),
    autoescape=select_autoescape(['html', 'xml'])
)


def get_calendar_results_breakdown(request):
    """
    This function is used to get the calendar results breakdown
    :param request:
    hotel_code: the hotel code
    month: the month
    year: the year
    adults: the number of adults
    children: the number of children
    babies: the number of babies
    :return:
    """
    hotel_code = request.values.get('hotel_code')
    if not hotel_code:
        return '"hotel_code" param is required'

    target_month = request.values.get('month')
    if not target_month:
        return '"month" param is required'

    target_year = request.values.get('year')
    if not target_year:
        return '"year" param is required'

    device = request.values.get('device')
    if not device:
        return '"device" param is required'

    country_code = request.values.get('country_code')
    if not country_code:
        return '"country_code" param is required'

    adults = request.values.get('adults')
    if not adults:
        return '"adults" param is required'

    children = request.values.get('children')
    if not children:
        return '"children" param is required'

    babies = request.values.get('babies')
    if not babies:
        return '"babies" param is required'

    manager_endpoint = _get_manager_endpoint(hotel_code)
    results_from_manager, results_url = _get_results_from_manager(
        manager_endpoint=manager_endpoint,
        hotel_code=hotel_code,
        target_month=target_month,
        target_year=target_year,
        device=device,
        country_code=country_code,
        adults=adults,
        children=children,
        babies=babies,
        promocode=request.values.get('promocode'),
        flight_hotel=request.values.get('flight_hotel'),
    )

    formatted_results = _format_results(results_from_manager, hotel_code)

    if request.values.get('json'):
        return json.dumps({
            'results': formatted_results,
            'results_url': results_url
        }), 200, {'Content-Type': 'application/json'}

    http_response_results = _get_http_response_results(formatted_results, results_url)

    return http_response_results


def _get_manager_endpoint(hotel_code):
    manager_endpoint = 'https://background-264405814672.us-central1.run.app'
    alternative_manager = get_hotel_advance_config_item({'applicationId': hotel_code}, 'Use alternative manager')
    if alternative_manager:
        manager_endpoint = alternative_manager[0]['value']

    alternative_background_manager = get_hotel_advance_config_item({'applicationId': hotel_code}, 'Use alternative background manager')
    if alternative_background_manager:
        manager_endpoint = alternative_background_manager[0]['value']

    return manager_endpoint


def _get_results_from_manager(**kwargs):
    manager_endpoint = kwargs.get('manager_endpoint')
    hotel_code = kwargs.get('hotel_code')
    target_month = kwargs.get('target_month')
    target_year = kwargs.get('target_year')
    device = kwargs.get('device')
    country_code = kwargs.get('country_code')
    adults = kwargs.get('adults')
    children = kwargs.get('children')
    babies = kwargs.get('babies')
    promocode = kwargs.get('promocode', '')

    start_datetime = datetime.datetime(int(target_year), int(target_month), 1)

    if int(target_month) == 12:
        end_datetime = datetime.datetime(int(target_year) + 1, 1, 1)
    else:
        end_datetime = datetime.datetime(int(target_year), int(target_month) + 1, 1)

    start_date = start_datetime.strftime('%d-%m-%Y')
    end_date = end_datetime.strftime('%d-%m-%Y')
    start_date = start_date.replace('-', '%2F')
    end_date = end_date.replace('-', '%2F')

    path_endpoint = f'/search/?applicationId={hotel_code}&' \
                    f'countryCode={country_code}&' \
                    f'startDate={start_date}&' \
                    f'endDate={end_date}&' \
                    f'numRooms=1&' \
                    f'adultsRoom1={adults}&' \
                    f'adultsRoom2=2&adultsRoom3=2&' \
                    f'babiesRoom1={babies}&' \
                    f'babiesRoom2=0&babiesRoom3=0&acceptIncompleteRates=true&' \
                    f'childrenRoom1={children}&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&source=Web&' \
                    f'device={device}&promocode={promocode}&language=en&referrer=build-tools-2-calendar-breakdown'

    if kwargs.get("flight_hotel"):
        path_endpoint += '&flight_hotel=True'

    url = f'{manager_endpoint}{path_endpoint}'

    manager_results = requests.get(url, auth=('paco', 'paco'))
    manager_results = manager_results.json()

    return manager_results, url


def _format_results(manager_results, hotel_code):
    language = 'SPANISH'
    formatted_results = {}

    hotel_rooms = get_rooms_of_hotel({'applicationId': hotel_code})
    hotel_rooms = {room['key']: room for room in hotel_rooms}

    hotel_rates = get_rates_of_hotel({'applicationId': hotel_code})
    hotel_rates = {rate['key']: rate for rate in hotel_rates}

    hotel_multi_rates = get_multirates_of_hotel({'applicationId': hotel_code})
    for multirate in hotel_multi_rates:
        multirate['name'] = f'[Multirate] {multirate["name"]}'
    hotel_rates.update({multirate['key']: multirate for multirate in hotel_multi_rates})


    hotel_boards = get_boards_of_hotel({'applicationId': hotel_code})
    hotel_boards = {board['key']: board for board in hotel_boards}

    hotel_promotions = get_promotions_of_hotel({'applicationId': hotel_code})
    hotel_promotions = {promotion['key']: promotion for promotion in hotel_promotions}

    hotel_packages = get_price_increase_of_hotel({'applicationId': hotel_code})
    hotel_packages = {package['key']: package for package in hotel_packages}

    disable_calendar_rates = get_hotel_web_config_item(hotel_code, 'rates_double_calendar')
    rates_to_avoid_by_config = []
    if disable_calendar_rates and disable_calendar_rates.get('avoid'):
        rates_to_avoid_by_config += disable_calendar_rates.get('avoid').split("@@@")

    start_datetime = datetime.datetime.strptime(manager_results['search']['startDate'], '%Y-%m-%d')
    end_datetime = datetime.datetime.strptime(manager_results['search']['endDate'], '%Y-%m-%d')

    for rooms_list in manager_results['resultsPerDay']:
        for room_key, rates_list in rooms_list.items():
            room_info = hotel_rooms.get(room_key)
            for rate_key, boards_list in rates_list.items():
                package_name = None

                if is_package(rate_key):
                    package_key, rate_key = get_package_and_rate_from_key(rate_key)
                    package_name = hotel_packages.get(package_key, {}).get('name')


                rate_info = hotel_rates.get(rate_key)
                if not rate_info:
                    rate_info = {'name': 'Rate not existent in manager', 'localName': rate_key}

                for board_key, prices_list in boards_list.items():
                    board_info = hotel_boards.get(board_key)

                    is_correct_prices_length = len(prices_list) == (end_datetime - start_datetime).days

                    for index, price in enumerate(prices_list):

                        target_date_string = start_datetime + datetime.timedelta(days=index)
                        target_date_string = target_date_string.strftime('%d/%m/%Y')

                        formatted_results.setdefault(target_date_string, {})
                        room_name = room_info.get('name')
                        formatted_results[target_date_string].setdefault(room_name, {})

                        rate_name = f"{rate_info.get('name')} ({rate_info.get('localName')})"

                        if package_name:
                            rate_name = '[PACKAGE: ' + package_name + '] ' + rate_name

                        if str(rate_info.get('id')) in rates_to_avoid_by_config:
                            rate_name = f"{rate_name} (Disabled by configuration)"

                        formatted_results[target_date_string][room_name].setdefault(rate_name, {})

                        if manager_results['extra']['dailyRestrictions'].get(room_key, {}).get(rate_key, {}).get(board_key):
                            min_stay = manager_results['extra']['dailyRestrictions'].get(room_key, {}).get(rate_key, {}).get(board_key, {}).get('minStay', [1] * 200)[index]
                            max_stay = manager_results['extra']['dailyRestrictions'].get(room_key, {}).get(rate_key, {}).get(board_key, {}).get('maxStay', [9999] * 200)[index]
                        else:
                            min_stay = 'Not defined in manager response'
                            max_stay = 'Not defined in manager response'


                        promotions_list = []
                        price_with_discount = price

                        for promotion_dict in manager_results.get('promotions'):
                            match_promotion = promotion_dict.get(room_key, {}).get(rate_key, {}).get(board_key, {})
                            if match_promotion:
                                for promotion_element in match_promotion.get('multiple_promotions', []):
                                    promotions_list.append({
                                        'promotion_name': hotel_promotions.get((promotion_element.get('key')), {}).get(
                                            'name'),
                                        'discount': promotion_element.get('price', [] * 200)[index],
                                    })

                                    price_with_discount -= promotion_element.get('price', [] * 200)[index]

                        board_name = board_info.get('name') if board_info else 'No board on datastore'
                        formatted_results[target_date_string][room_name][rate_name][board_name] = {
                            'price': price if is_correct_prices_length else 'Manager response gives incorrect number of results, will fail to display correctly',
                            'price_with_discount': price_with_discount,
                            'min_stay': min_stay,
                            'max_stay': max_stay,
                            'promotions': promotions_list
                        }

    return formatted_results


def _get_http_response_results(formatted_results, results_url):
    context = {
        'results': formatted_results,
        'results_url': results_url
    }
    template = env.get_template('calendar_breakdown.html')
    results = template.render(context)
    results_minified = htmlmin.minify(results, remove_empty_space=True)
    return results_minified


def is_package(rate_key):
    if not rate_key:
        return False

    return rate_key.startswith("PACKAGE_")


def get_package_and_rate_from_key(rate_key):
    if not is_package(rate_key):
        return None


    PACKAGE_SEPARATOR = "_@_"
    PACKAGE_SEPARATOR_LEGACY = "_"

    if not PACKAGE_SEPARATOR in rate_key:
        # Use Legacy
        splitted = rate_key.split(PACKAGE_SEPARATOR_LEGACY)

        # Key including _, we need to fix it
        if len(splitted) > 3:

            if len(splitted[1]) > len(splitted[3]):
                return splitted[1], "%s_%s" % (splitted[2], splitted[3])
            else:
                return "%s_%s" % (splitted[1], splitted[2]), splitted[3]

        else:
            return splitted[1], splitted[2]

    else:
        splitted = rate_key.split(PACKAGE_SEPARATOR)
        # Return package_key, rate_key
        return splitted[1], splitted[2]

