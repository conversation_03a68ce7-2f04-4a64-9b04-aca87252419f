import datetime
import json
import logging

import requests

from paraty_commons_3 import date_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

__author__ = 'fmatheis'





def _get_duplicated_bookings(file_name):

	with open(file_name, 'r') as f:
		all_push = json.loads(f.read())

	duplicated_bookings = {}
	for current_hotel in all_push:
		hotel = list(current_hotel.keys())[0]
		bookings = current_hotel[hotel]
		current_duplicated = []
		for booking in bookings.keys():
			if booking.startswith('0') and booking[1:] in bookings:
				current_duplicated.append(booking)
		if current_duplicated:
			duplicated_bookings[hotel] = current_duplicated


	# Write file with duplicated bookings
	with open('duplicated_bookings.json', 'w') as f:
		f.write(json.dumps(duplicated_bookings, indent=4))


def _get_all_push_since(min_date):

	min_datetime = date_utils.string_to_date(min_date)

	all_push = []
	all_hotels = get_all_valid_hotels()
	for hotel in all_hotels:

		current_push = datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent',
													   search_params=[('path', '=', '/push_reservation'),
																	  ('hotel_code', '=', hotel['applicationId']),
																	  ('timestamp', '>=', min_datetime)],
														   			 hotel_code="siteminder-adapter:")
		if current_push:
			all_identifiers = {x['request_id']:str(x['timestamp']) for x in current_push}
			all_push.append({hotel['applicationId']:all_identifiers})

	# Write file with all push
	with open('all_push.json', 'w') as f:
		f.write(json.dumps(all_push, indent=4))


def _call_integration_cancelling_duplicated(file_name):

	with open(file_name, 'r') as f:
		duplicated_bookings = json.loads(f.read())

	for hotel_code, identifiers in duplicated_bookings.items():
		for identifier in identifiers:
			pass # TODO
			# requests.post("https://siteminder-adapter.appspot.com/push_reservationXXXXXXXXX", data=json.dumps({
			# 	"hotel_code": hotel_code,
			# 	"identifier": identifier,
			# 	"status": "CANCELLED"
			# }))
			# print("CANCELLED: " + hotel_code + " " + identifier)




if __name__ == "__main__":

	# _get_all_push_since('2023-02-05')
	# _get_duplicated_bookings('all_push.json')
	_call_integration_cancelling_duplicated('duplicated_bookings.json')