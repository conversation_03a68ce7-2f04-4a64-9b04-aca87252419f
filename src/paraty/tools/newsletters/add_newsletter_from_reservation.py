import json

import requests

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel, get_hotel_advance_config_item
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def add_reservation_to_newsletter(hotel_info):
    domain = get_hotel_advance_config_item(hotel_info, 'Dominio asociado')[0]['value']
    print(domain)

    from_date = "2022-01-01"
    to_date = "2022-05-18"
    available_reservation = get_reservations_of_hotel(hotel_info, from_datetime=from_date, to_datetime=to_date)

    only_allowed_newsletter = []
    for reservation in available_reservation:
        extra_info = json.loads(reservation['extraInfo'])
        if extra_info.get('check-allow-notifications'):
            only_allowed_newsletter.append(reservation)


    target_endpoint = domain + '/utils'

    for booking_to_add in only_allowed_newsletter:
        post_params = {
            'action': 'newsletter',
            'added_programatically': True,
            'namespace': hotel_info['applicationId'],
            'email': booking_to_add['email'],
            'name': booking_to_add['name'],
            'surname': booking_to_add['lastName']
        }

        requests.post(target_endpoint, post_params)


if __name__ == '__main__':
    hotels_to_check = [
        "secure-granhoteldelcoto",
        "nuba-comarruga",
        "suites-tarifa",
        "pena-parda",
        "port-benidorm",
        "port-jardin",
        "zahara-sol",
        "sirena-3",
        "port-elche",
        "port-vista",
        "tarifa-lances",
        "blau-cel",
        "port-azafata",
        "port-denia",
        "webseeker-data",
        "diufain-conil",
        "port-huerto",
        "port-europa",
        "test-backend2",
        "blau-corporativa",
        "bahia-cadiz",
        "sirena-aptos",
        "port-fiesta",
        "sirena-4",
        "blau-mar",
        "sirena-corpo",
        "puertobahia-spa",
        "port-alicante",
        "estival-centurion",
        "test4-copia10",
        "port-feria"
    ]


    all_valid_hotels = get_all_valid_hotels()
    for i, hotel in enumerate(all_valid_hotels):
        if hotel['applicationId'] in hotels_to_check:
            add_reservation_to_newsletter(hotel)
