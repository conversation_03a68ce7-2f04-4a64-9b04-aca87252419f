import json
import logging
import time
import zlib

import redis
from cachetools import LRUCache

REDIS_HOST = "************"
REDIS_PORT = 6666
REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"

WPP_MEMORY_CACHE = LRUCache(maxsize=2000)
WPP_REDIS_POOL = redis.ConnectionPool(host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, socket_timeout=3, socket_connect_timeout=3)

def _get_cache_key(hotel_code, language):
    return f"wpp_v1_{hotel_code}_{language}"

def _get_last_remove_key(hotel_code):
    return f"wpp_v1_last_remove_{hotel_code}"

def cache_value_has_change(hotel_code, language, original_timestamp):
    cached_value = get_cached_wpp_for_hotel(hotel_code, language)
    if cached_value:
        cached_result = json.loads(cached_value)
        return original_timestamp != cached_result.get("timestamp")

    return False


def get_cached_wpp_for_hotel(hotel_code, language):
    cache_key = _get_cache_key(hotel_code, language)
    redis_client = redis.Redis(connection_pool=WPP_REDIS_POOL)

    cached_content = None

    last_remove_key = _get_last_remove_key(hotel_code)
    last_remove_redis_timestamp = redis_client.get(last_remove_key)
    last_remove_memory_timestamp = WPP_MEMORY_CACHE.get(last_remove_key)
    expired_memory_entries = last_remove_redis_timestamp and last_remove_memory_timestamp and float(last_remove_redis_timestamp) > float(last_remove_memory_timestamp)

    # Memory
    if cache_key in WPP_MEMORY_CACHE and not expired_memory_entries:
        logging.info(f"Memory cache hit for hotel {hotel_code} - {language}")
        cached_content = WPP_MEMORY_CACHE[cache_key]

    if expired_memory_entries:
        logging.info(f"Memory cache expired for hotel {hotel_code} - {language}")

    # Redis
    if not cached_content:
        cached_content = redis_client.get(cache_key)
        if cached_content:
            logging.info(f"Redis cache hit for hotel {hotel_code} - {language}")

    # Decompress content
    if cached_content:
        try:
            decompressed_data = zlib.decompress(cached_content)
            cached_content = decompressed_data.decode()
        except Exception as e:
            logging.error(f"Error decompressing cached content for hotel {hotel_code} - {language}: {e}")
            cached_content = None

    return cached_content


def set_cached_wpp_for_hotel(hotel_code: str, language: str, cached_content: str) -> None:
    redis_client = redis.Redis(connection_pool=WPP_REDIS_POOL)
    cache_key = _get_cache_key(hotel_code, language)

    cached_content = zlib.compress(cached_content.encode(), level=9)

    WPP_MEMORY_CACHE[cache_key] = cached_content
    redis_client.set(cache_key, cached_content)

    WPP_MEMORY_CACHE[_get_last_remove_key(hotel_code)] = time.time()


def delete_cached_wpp_for_hotel(hotel_code: str, wpp_ids: list[str]):
    redis_client = redis.Redis(connection_pool=WPP_REDIS_POOL)
    keys = redis_client.keys(f"wpp_v1_{hotel_code}_*")

    for key in keys:
        redis_client.delete(key)

    redis_client.set(_get_last_remove_key(hotel_code), time.time())

    logging.info(f"Deleted {len(keys)} cache keys for hotel {hotel_code}")