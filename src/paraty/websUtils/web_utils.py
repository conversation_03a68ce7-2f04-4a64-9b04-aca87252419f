import logging
import unicodedata

import re
from html.entities import name2codepoint, codepoint2name



LANGUAGE_CODES = {
	"SPANISH": 'es',
	"ENGLISH": 'en',
	"GERMAN": 'de',
	"FRENCH": 'fr',
	"PORTUGUESE": 'pt',
	"POLISH": 'pl',
	"CATALAN": 'ca',
	"RUSSIAN": 'ru',
	"DUTCH": 'nl',
	"FINNISH": 'fi',
	"ITALIAN": 'it',
	"SWEDISH": 'sv',
	"JAPANESE": 'ja',
	"KOREAN": 'ko',
	"CHINESE_TRADITIONAL": 'zh-Hant',
	"CHINESE_SIMPLIFIED": 'zh-Hans'
	}

def get_language_code(language):
	return LANGUAGE_CODES.get(language, 'en')


def build_friendly_url(request):

	name = request.values.get("name")
	extension = request.values.get("extension", ".html")
	language = request.values.get("language", "")

	if not name:
		return ''

	result = unescape(name)
	result = result.replace('"',"").replace(' ', '-').replace("'", '').replace(",", "").replace("%","").lower().replace("*","").replace("(","").replace(")","").replace("!","").replace(";","").replace("&","").replace(":","")
	result = result.replace("?", "").replace('/', '')

	try:

		symbols = (u"абвгдеёзийклмнопрстуфхъыьэАБВГДЕЁЗИЙКЛМНОПРСТУФХЪЫЬЭ",
						u"abvgdeezijklmnoprstufh-y-eABVGDEEZIJKLMNOPRSTUFH'Y'E")

		tr = {ord(a): ord(b) for a, b in zip(*symbols)}
		result = result.translate(tr)
		result = unicodedata.normalize('NFKD', result).encode('ASCII', 'ignore').lower()

	except Exception as e:
		logging.warning("Exception normalizing %s: %s" % (result,e))

	url = result.decode("utf-8") + extension

	if language:
		language_code = get_language_code(language)
		url = language_code + "/" + url


	return "/" + url


##
# Removes HTML or XML character references and entities from a text string.
#
# @param text The HTML (or XML) source text.
# @return The plain text, as a Unicode string, if necessary.
#
# http://stackoverflow.com/questions/57708/convert-xml-html-entities-into-unicode-string-in-python
# Takek from http://effbot.org/zone/re-sub.htm#unescape-html
# This function works wonderfully. Long live Fredrik

def unescape(text):
	def fixup(m):
		text = m.group(0)
		if text[:2] == "&#":
			# character reference
			try:
				if text[:3] == "&#x":
					return chr(int(text[3:-1], 16))
				else:
					return chr(int(text[2:-1]))
			except ValueError:
				pass
		else:
			# named entity
			try:
				text = chr(name2codepoint[text[1:-1]])
			except KeyError:
				pass
		return text # leave as is

	if not text:
		return ""

	return re.sub("&#?\w+;", fixup, text)

def escape_unicode(text):
	t = ""
	for i in text:
		if ord(i) in codepoint2name:
			name = codepoint2name.get(ord(i))
			t += "&" + name + ";"
		else:
			t += i
	return t