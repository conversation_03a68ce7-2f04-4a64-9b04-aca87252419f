import os

from paraty import Config
from paraty.anti_bots.email_sender import send_bot_notification_email as do_send_bot_notification_email
from paraty.backups.hotels import hotel_backup as hotel_backup_module
from paraty.backups.wiki_seeker.main import perform_backup_of_wikis
from paraty.checkers import checker_utils, page_entry_checker
from paraty.checkers.active_bookings import check_active_bookings_handler
from paraty.checkers.html_checks import check_html_handler
from paraty.chrome_extension.paraty_tools_handler import handle_paraty_tools_chrome_extension_handler
from paraty.development.agency import add_reservation_info, add_reservation_info_hotel
from paraty.development.cancel_pending_reservations_not_paid import cancel_pending_reservations_not_paid,\
    cancel_reservations_expired_link, cancel_reservations_hotel
from paraty.development.check_if_identifier_exists import check_identifier
from paraty.development.check_if_identifier_is_used import check_identifier_used
from paraty.development.encrypt_PrecheckinReservation import encrypt_or_decrypt_precheckin_reservations
from paraty.development.encrypt_pending_reservation import encrypt_pending_reservation
from paraty.development.generate_payment_link_encrypt import generate_payment_link_reservation
from paraty.development.users.get_if_user_logged_in_callcenter import do_get_if_users_logged_in_callcenter
from paraty.maintainance import admin_hotel_crons
from paraty.backups import backup_utils
from paraty.backups.web_seeker import web_seeker_backup
from paraty.billing import monthly_billing
from paraty.build import build_utils
from paraty.development import development_utils, populate_datastore_utils, project_location_finder
from paraty.development.generate_confirmation_link_encrypt import generate_confirmation_link_reservation
from paraty.general import general_utils
from paraty.bookings.redirect_occupancy import get_new_combination as do_get_new_combination
from paraty.bookings.calendar_generator import generate_calendar_files

from paraty.slack_notify import notify

from paraty.queues import task_queue_common_functions
from paraty.session.cleanup import cleanup_all_sessions_execute
from paraty.session.session_utils import read_session_hotel, write_session_hotel
from paraty.tools.calendars.get_calendar_utils_breakdown import get_calendar_results_breakdown
from paraty.tools.satisfaction_survey.survey_handler import send_survey_form_to_all_hotels
from paraty.utilities.tax.tax_utils import get_total_accomodation_tax as _get_total_accomodation_tax
from paraty.utm_sources import utm_sources
from paraty.webPageProperties import web_page_properties_utils
from paraty.websUtils import web_utils
from paraty.checkers import identity_checks, postal_code_checks
from paraty.bookings import user_club
from paraty.bookings import process_bookings
from paraty.bookings import external_ids_table
from paraty.gdrive import configs_excels
import google.cloud.logging


# Added logs for cloud functions to be easier to track issues
if not os.environ.get('FUNCTION_TARGET') and not Config.DEV or os.environ.get('ENABLE_SETUP_LOGGING'):
    client = google.cloud.logging.Client()
    client.setup_logging()

'''
This file will contain all the functions exposed in this project
'''


# ===== STORAGE ==============================================

def create_file_at_storage(request):
    return general_utils.create_file_at_storage(request)


def backup_wikis_at_gcs_handler(request):
    return perform_backup_of_wikis(request)


# ===== CHECKER ==============================================

def get_hotels_to_check(request):
    return checker_utils.get_hotels_to_check()


# ===== COMMON ===============================================

def entities_generic_backup(request):
    return backup_utils.entities_generic_backup(request)


# ===== WEB SEEKER ============================================

def execute_webseeker_backup(request):
    return web_seeker_backup.execute_webseeker_backup(request)


# ===== HOTEL MANAGER ==========================================
def execute_hotel_manager_backup(request):
    return hotel_backup_module.execute_hotel_manager_backup(request)


# ===== HOTEL ============================================

def execute_hotels_backup(request):
    return hotel_backup_module.execute_hotels_backup(request)


def hotel_backup(request):
    hotel_code = request.values.get('hotel_code')
    return hotel_backup_module.execute_hotel_backup(hotel_code)


# ===== BILLING ===============================================

# Downloads one bill for one hotel
def download_bill(request):
    return monthly_billing.download_bill(request)


def user_club_delete_transaction(request):
    return user_club.user_club_points(request)


# Downloads all the bills for the previous month
def default_billing_for_previous_month(request):
    return monthly_billing.default_billing_for_previous_month(request)


def zip_and_send_bills(request):
    return monthly_billing.zip_and_send_bills(request)


# ===== UTM SOURCES ===========================================
def get_utm_sources(request):
    return utm_sources.get_utm_sources(request)


# ===== BUILDING ===============================================

def get_web_update_configuration(request):
    return build_utils.get_web_update_configuration(request)


def get_all_from_entity(request):
    return development_utils.get_all_from_entity(request)


# ===== WEB UTILS ===============================================

def build_friendly_url(request):
    return web_utils.build_friendly_url(request)


# ===== QUEUES ===============================================

def list_tasks_in_queue(request):
    return task_queue_common_functions.list_tasks_in_queue(request)


# =========== HOTEL MANAGER CRONS! ==============================

def exec_maintenance_admin_hotel(request):
    return admin_hotel_crons.exec_maintenance_admin_hotel(request)


def generic_maintenance(request):
    '''
    Here we can add any function that we want to execute periodically
    This will be called hourly
    '''
    from paraty.maintainance.inconsistency_utils import remove_duplicated_hotel_metadatas
    remove_duplicated_hotel_metadatas('HotelMetadata', 'applicationId', 'admin-hotel')
    return 'OK'


# ===== QUEUE UTILS ===============================================

def queue_url_call(request):
    return general_utils.queue_url_call(request)


# ======MULTIPLE FUNCTIONS==========================================

def execute_populate_function(request):
    client = google.cloud.logging.Client()
    client.setup_logging()

    return populate_datastore_utils.execute_funcion(request)


# ======SLACK NOTIFICATION==========================================

def slack_notify(request):
    client = google.cloud.logging.Client()
    client.setup_logging()

    return notify.slack_message(request)

# ======CHECK HTML==========================================

def check_given_html(request):
    return check_html_handler(request)


# ======CHECK ACTIVE BOOKINGS ==========================================

def check_active_bookings(request):
    return check_active_bookings_handler(request)


# ======CREATE FUNCTIONS==========================================

def execute_create_hotel(request):
    return populate_datastore_utils.create_hotel(request)


def get_hotel_location_prefix(request):
    return project_location_finder.get_hotel_location_prefix(request)


# ====== SEND SATISFACTION SURVEY EMAILS TO ALL HOTELS ==========================================

def send_survey_to_all_hotels(request):
    return send_survey_form_to_all_hotels(request)


# ======== CALENDAR RESULTS BREAKDOWN ==============
def get_calendar_results_breakdown_handler(request):
    return get_calendar_results_breakdown(request)


# ===== CLEANUP SESSION ==============================================

def cleanup_all_sessions(request):
    return cleanup_all_sessions_execute()


def read_session_from_hotel_handler(request):
    return read_session_hotel(), 200, {'Content-Type': 'application/json'}


def write_session_from_hotel_handler(request):
    session_writed = write_session_hotel(data=request.get_json())
    status_response = 200 if session_writed[0] else 403
    return session_writed[1], status_response


# ======PROCESS ANTIOTA BOOKINGS========================
def process_antiota_bookings(request):
    return process_bookings.process_antiota_bookings()


# ======VALIDATE IDENTIFIERS==========================================

def validate_identifier(request):
    body = request.get_json()
    country_id = body.get('country')
    id_number = body.get('id')
    return str(identity_checks.validate_identifier(country_id, id_number))


def validate_zip_code(request):
    body = request.get_json()
    country_id = body.get('country')
    validate_zip_code = body.get('postal_code')
    return str(postal_code_checks.validate_zip_code(country_id, validate_zip_code))


# ======WEBPAGE PROPERTIES===============================

def get_web_page_properties_for_language(request):
    return web_page_properties_utils.get_web_page_properties_for_language(request)


def refresh_web_page_properties_for_language(request):
    return web_page_properties_utils.refresh_web_page_properties_for_language(request)


def clean_web_page_properties_for_hotel(request):
    return web_page_properties_utils.clean_web_page_properties_for_hotel(request)


def delete_web_page_property_for_hotel(request):
    return web_page_properties_utils.delete_web_page_property_for_hotel(request)


# ======================== WEBPAGE ENTRY ========================

def delete_invalid_page_entries(request):
    return page_entry_checker.delete_invalid_page_entries_handler()


# ========================CONFIGURATIONS EXCELS===============================
def fill_advanced_config_excel(request):
    return configs_excels.fill_advanced_configs_excel()


def fill_all_hotels_adapters_excel(request):
    return configs_excels.fill_all_hotels_adapters_excel()


def fill_xml_config_excel(request):
    return configs_excels.fill_xml_configs_excel()


def fill_web_config_excel(request):
    return configs_excels.fill_web_configs_excel()


def fill_users_config_excel(request):
    return configs_excels.fill_users_configs_excel()


def fill_users_permissions_excel(request):
    return configs_excels.fill_users_permissions_excel()


# =====CANCEL PENDING RESERVATIONS===============================================
def cancel_pending_reservations(request):
    return cancel_pending_reservations_not_paid()


# =====CHECK IF IDENTIFIER IS BEING USED===============================================


def check_if_identifier_exists(request):
    return check_identifier(request)


def check_if_identifier_is_used(request):
    # This is the most recent version
    return check_identifier_used(request)

# =====ENCRYPT PendingReservation FROM ALL HOTELS===============================================


def encrypt_or_decrypt_precheckin_reservations_handler(request):
    return encrypt_or_decrypt_precheckin_reservations(request)


def encrypt_pending_reservation_cf(request):
    return encrypt_pending_reservation(request)


def paraty_chrome_extension_handler(request):
    # This is used to work with Paraty Chrome Extension
    return handle_paraty_tools_chrome_extension_handler(), 200, {
        'Access-Control-Allow-Origin': '*',
    }


def paraty_add_reservation_agency(request):
    return add_reservation_info()


def paraty_add_reservation_agency_task(request):
    remote_hotel = request.args.get("remote_hotel")
    hotel_code = request.args.get("hotel_code")
    reservations_from = request.args.get("reservations_from")

    return add_reservation_info_hotel(remote_hotel, hotel_code, reservations_from)


def fill_external_ids_table(request):
    return external_ids_table.fill_external_ids_table()


def cancel_reservations_expired(request):
    return cancel_reservations_expired_link()


def cancel_reservations_expired_link_task(request):
    hotel_code = request.args.get("hotel_code")
    return cancel_reservations_hotel(hotel_code)



def generate_payment_link(request):
    identifier = request.values.get("identifier")
    hotel_code = request.values.get("hotel_code")

    return generate_payment_link_reservation(hotel_code, identifier)


def send_bot_notification_email(request):
    do_send_bot_notification_email()
    return ""


# ===== TAX  UTILS ===============================================
def get_total_accommodation_tax(request):
    params = request.get_json()
    increment_total = _get_total_accomodation_tax(params)
    return str(increment_total)


def get_if_users_logged_in_callcenter(request):
    params = request.get_json()
    users = params.get('users', [])
    start_date = params.get('start_date')
    months = int(params.get('months', 1))

    return do_get_if_users_logged_in_callcenter(users, start_date, months)


# ===== SEARCH  UTILS ===============================================
def get_new_combination(request):
    body = request.get_json()
    return do_get_new_combination(body.get('hotel_code', ''), body.get('search', ""), body.get('num_max_rooms', False))


def get_confirmation_link_encrypt(request):
    hotel_code = request.values.get("hotel_code")
    identifier = request.values.get("identifier")
    email = request.values.get("email", "")
    is_for_manager = request.values.get("is_for_manager", "true").lower() == "true"

    return generate_confirmation_link_reservation(hotel_code, identifier, email, is_for_manager)


def generate_calendar_files_handler(request):
    data = request.get_json()
    return generate_calendar_files(data)
