import json
import os
from datetime import datetime
from .custom_serializer import to_json
from jinja2 import Environment, FileSystemLoader, select_autoescape

DATA_DUMP_FILE = 'data_dump.json'
TEMPLATE_UNITTEST_FILE = 'unit_test_template.py.tmp'
PROJECT_ROOT_PATH = os.sep.join(os.path.dirname(__file__).split(os.sep)[:-2])


def create_test_folder(path):
	if not os.path.exists(path):
		os.makedirs(path)
	return True


def write_dump_args(test_path, dump_args, locals_var, globals_var):
	jsonString = safe_serialize({
		'parameters': dump_args,
		'locals': locals_var,
		'globals': globals_var
	})
	data_dum_file = os.path.join(test_path, DATA_DUMP_FILE)

	# Create data_dump.json file
	with open(data_dum_file, "w", encoding='utf-8') as file:
		file.write(jsonString)
		file.close()


def write_unittest(test_path, function_name, function_argnames, args, f):

	TEST_FUNCTION_FILE = f'test_{function_name}.py'

	env = Environment(
		loader=FileSystemLoader(os.path.sep.join(__file__.split(os.path.sep)[0:-1])),
		autoescape=select_autoescape()
	)
	t = env.get_template(TEMPLATE_UNITTEST_FILE)

	import_settings = get_import_from_function_name(f)

	parameters = ', '.join(list(function_argnames))
	render = t.render(
		function_name=function_name,
		data_dump_file='\'data_dump.json\'',
		# parameters=parameters,
		import_settings=import_settings,
		class_name=f.__qualname__.split('.')[0] if '.' in f.__qualname__ else None
	)

	test_file = os.path.join(test_path, TEST_FUNCTION_FILE)

	# Create test_{function_name}.py file
	with open(test_file, "w", encoding='utf-8') as file:
		file.write(render)
		file.close()


def default(o):
	if type(o).__qualname__ == 'function':
		return
	if isinstance(o, (datetime.date, datetime.datetime)):
		return o.isoformat()


def get_clean_dict(obj):
	clean_dict = {}
	for k, v in obj.items():
		if type(v) in [int, float, bool, str, list]:
			clean_dict[k] = v
		elif type(v) in [dict]:
			clean_dict[k] = get_clean_dict(v)
		elif type(v).__qualname__ != 'function':
			clean_dict[k] = v
	return clean_dict


def safe_serialize(obj):
	obj_serializable = get_clean_dict(obj)
	return json.dumps(obj_serializable, indent=4, default=to_json)


def get_import_from_function_name(my_function):

	if '.' in my_function.__qualname__:
		my_class, my_method = my_function.__qualname__.split('.')
		return f'from {my_function.__module__} import {my_class}'
	else:
		return f'from {my_function.__module__} import {my_function.__name__}'


def create_test(get_test_path=lambda x: './src/test/', is_enabled=lambda x: False):

	def decorate(f):

		def do_testing(*args, **kwargs):

			if is_enabled(f):

				test_path = get_test_path(f)

				# Create test folder if don't exist
				create_test_folder(test_path)

				# Getting the argument names of the called function
				function_argnames = f.__code__.co_varnames[:f.__code__.co_argcount]

				# Getting the Function name of the called function
				function_name = f.__name__

				dump_args = {}
				for tup in zip(list(function_argnames), list(args)):
					dump_args[tup[0]] = tup[1]

				# Dump args, locals and globals to be able to reproduce the call without problems
				write_dump_args(test_path, dump_args, locals(), globals())

				# Create a new unit test (pytest)
				write_unittest(test_path, function_name, function_argnames, args, f)

			result = f(*args, **kwargs)
			return result

		return do_testing

	return decorate
