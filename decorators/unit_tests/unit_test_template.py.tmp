from paraty import Config
import json
{{ import_settings }}


def test_{{ function_name }}():

	f = open({{ data_dump_file }})

	# returns JSON object as a dictionary
	data = json.load(f)

	# Closing file
	f.close()

	args = []

	{% raw -%}
	pars = data.get('parameters', {})
	for key, value in pars.items():
		args.append(value)
	{% endraw %}

	# TODO, ADD Context initialization, etc....

	{% if class_name %}
	{{ class_name }}.{{ function_name }}( *args )
	{% else %}
	{{ function_name }}( *args )
	{% endif %}


