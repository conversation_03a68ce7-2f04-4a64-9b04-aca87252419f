import os
import time
from functools import wraps

import logging

def timeit(method):
	
	@wraps(method)
	def timed(*args, **kw):
		if not os.environ.get('GOOGLE_CLOUD_PROJECT'):
			ts = time.time()
			result = method(*args, **kw)
			te = time.time()
			# logging.info('%r (%r) %2.2f sec' % (method.__name__, kw, te-ts))

			resultTime = "%2.2f" % (te-ts)
			if resultTime != "0.00":
				logging.info('%r (%r %r) %2.2f sec' % (method.__name__, args, kw, te-ts))

			return result

		else:
			return method(*args, **kw)
		
	return timed


def timeit_no_verbose(method):
	@wraps(method)
	def timed(*args, **kw):
		ts = time.time()
		result = method(*args, **kw)
		te = time.time()
		# logging.info('%r (%r) %2.2f sec' % (method.__name__, kw, te-ts))

		resultTime = "%2.2f" % (te - ts)
		if resultTime != "0.00":
			logging.info('%r %2.2f sec' % (method.__name__, te - ts))

		return result

	return timed