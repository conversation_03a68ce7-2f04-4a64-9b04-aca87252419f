from datetime import datetime, timedelta
import logging
from google.cloud import ndb
from copy import deepcopy
from flask import request


CACHE_SECONDS_FOR_HOTELADS= 24*60*60 #24 horas
class CachedSearch(ndb.Model):

    value = ndb.TextProperty()
    ttl = ndb.DateTimeProperty()
    timestamp = ndb.DateTimeProperty()

def get_search(search):
    logging.info("Looking for cached search for key %s" % search)
    cached_search = CachedSearch.get_by_id(search)

    if not cached_search:
        return None

    if cached_search.ttl < datetime.now():
        logging.info("Search is expired, deleting it")
        cached_search.key.delete()
        return None

    logging.info("Found cached search!")
    return cached_search

def set_search_value(search, search_no_ages, value, ttl=24 * 60 * 60):
    '''
    Default TTL is 24 hours
    '''

    ttl_datetime = datetime.now() + timedelta(seconds=ttl)
    CachedSearch(id=search, value=value, ttl=ttl_datetime, timestamp=datetime.now()).put()
    CachedSearch(id=search_no_ages, value=value, ttl=ttl_datetime, timestamp=datetime.now()).put()


def _build_key_cache_from_search(request):
    # Read parameters

    param_list = []
    param_list.append(request.args.get('applicationId', ""))
    param_list.append(request.args.get('promocode', ""))
    param_list.append(request.args.get('startDate', ""))
    param_list.append(request.args.get('endDate', ""))
    param_list.append(request.args.get('numRooms', ""))
    param_list.append(request.args.get('adultsRoom1', ""))
    param_list.append(request.args.get('adultsRoom2', ""))
    param_list.append(request.args.get('adultsRoom3', ""))
    param_list.append(request.args.get('childrenRoom1', ""))
    param_list.append(request.args.get('childrenRoom2', ""))
    param_list.append(request.args.get('childrenRoom3', ""))
    param_list.append(request.args.get('babiesRoom1', ""))
    param_list.append(request.args.get('babiesRoom2', ""))
    param_list.append(request.args.get('babiesRoom3', ""))

    param_list.append(request.args.get('acceptIncompleteRates', ""))
    param_list.append(request.args.get("language", "es"))

    if request.args.get('countryCodeKeyCache'):
        # not needed if present, let's cache all searches regardless country
        param_list.append(request.args.get('countryCodeKeyCache', ""))
    else:
        param_list.append(request.args.get('countryCode', ""))

    if request.args.get("device"):
        param_list.append(request.args.get("device"))

    # be careful with AGES! Call center always send 3 (even there aren't kids in search ), so we add ages in key only if children arrive
    # and priceseekers the opposite: never send ages  (even there aren kids in search ), so we are going to create a double key

    param_list_no_ages = deepcopy(param_list)

    if not request.args.get('childrenRoom1', "0") == "0":
        param_list.append(request.args.get('agesKid1', ""))

    if not request.args.get('childrenRoom2', "0") == "0":
        param_list.append(request.args.get('agesKid2,', ""))

    if not request.args.get('childrenRoom3', "0") == "0":
        param_list.append(request.args.get('agesKid3', ""))

    cache_key = "-".join(param_list)
    cache_key_no_ages = "-".join(param_list_no_ages)

    logging.info("cache key for searches: %s", cache_key)
    return cache_key, cache_key_no_ages


def search_with_persistent_cache(search_cache=60*3, search_ttl=(24 * 60 * 60)*2):
    '''
    By Default, Search cache is 3 minutes, time to live is 2 days
    '''

    def decorate(f):

        def persistent_search(*args, **kwargs):

            search_key, search_key_no_ages = _build_key_cache_from_search(request)

            if request.args.get("source", "") == "HotelAds":
                time_delta = timedelta(seconds=CACHE_SECONDS_FOR_HOTELADS)
            else:
                time_delta = timedelta(seconds=search_cache)

            result = None
            if request.args.get("agesKid1"):
                result = get_search(search_key)
            else:
                result = get_search(search_key_no_ages)

            if not result or (datetime.now() - result.timestamp > time_delta):

                try:
                    function_result = f(*args, **kwargs)
                except Exception as e:
                    if result:
                        return result.value
                    raise e

                if result:
                    result.key.delete()

                logging.info("Search function completed, creating new cached search for key %s" % search_key)
                set_search_value(search_key, search_key_no_ages, function_result, search_ttl)

                return function_result

            else:
                logging.info("Found valid NEW cached search")
                return result.value

        return persistent_search

    return decorate