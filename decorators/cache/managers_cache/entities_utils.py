import datetime
import logging
import os
import pickle
import threading
from functools import wraps

import redis
import requests
from cachetools import TTLCache

from paraty_commons_3.datastore.datastore_utils import get_location_prefix
from paraty_commons_3.decorators.cache.managers_cache.redis_utils import POOL_REDIS, REDIS_PORT, REDIS_PASSWORD

ENTITIES_TIMESTAMPS_REDIS_EU = '34.34.175.234'
ENTITIES_TIMESTAMPS_REDIS_US = '34.58.177.135'

TIMESTAMP_CHECK_INTERVAL = int(os.environ.get('TIMESTAMP_CHECK_INTERVAL') or 30)
logging.info(f"Timestamp checking internal: {TIMESTAMP_CHECK_INTERVAL}")

cache = {}
cache_expiry = {}

def timed_cache(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        force_refresh = kwargs.pop("_force_refresh", False)
        key = func.__name__ + str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))
        current_time = datetime.datetime.now().timestamp()

        if key in cache and not force_refresh and cache_expiry[key] > current_time:
            return cache[key]
        else:
            result = func(*args, **kwargs)
            cache[key] = result
            cache_expiry[key] = current_time + TIMESTAMP_CHECK_INTERVAL
            return result

    return wrapper


def _get_entity_timestamps_host(hotel_code: str) -> str:
    location = get_location_prefix(hotel_code)
    if location and (location.startswith('s') or location.startswith('p')):
        return ENTITIES_TIMESTAMPS_REDIS_US

    return ENTITIES_TIMESTAMPS_REDIS_EU


def _get_entities_connection(hotel_code: str) -> redis.Redis:

    host = _get_entity_timestamps_host(hotel_code)

    if not POOL_REDIS.get(host):
        logging.info("Creating redis connection to obtain timestamps for hotel_code %s: %s" % (hotel_code, host))
        POOL_REDIS[host] = redis.ConnectionPool(host=host, port=REDIS_PORT, password=REDIS_PASSWORD, socket_timeout=3, socket_connect_timeout=3)

    connection = redis.Redis(connection_pool=POOL_REDIS[host])
    try:
        # Added temporary to debug number of connections against redis
        from paraty.config import Config
        project_name = Config.PROJECT or 'paraty-common-3-undefined'
        connection.client_setname(project_name)
    except Exception as e:
        pass

    return connection


def _generate_timestamp_key(tag: str, hotel_code: str) -> str:
    return 'cache_webs_%s_%s' % (tag, hotel_code)


@timed_cache
def get_all_entities_timestamps(hotel_code) -> dict[str, str |None]:
    target_key = _generate_timestamp_key('all_entities', hotel_code)
    logging.info(f"Getting all entities timestamps {target_key}")
    results = _get_entities_connection(hotel_code).get(target_key)
    if results:
        results = pickle.loads(results)
        for key_element, key_value in results.items():
            if type(key_value) is bytes:
                results[key_element] = key_value.decode('utf-8')

        return results
    # Note that we are relying on the fact that the cache is updated by hotel-webs, so we never update this timestamps


def refresh_missing_entity_timestamp(hotel_code: str, tag: str) -> dict:
    """
    Create missing cache timestamp for the entity in redis

    :param hotel_code: The hotel code
    :param tag: The entity name
    :return: Updated timestamps dictionary
    """
    actual_timestamps = get_all_entities_timestamps(hotel_code) or {}
    if tag in actual_timestamps:
        return

    connection = _get_entities_connection(hotel_code)
    target_entity_key = _generate_timestamp_key(tag, hotel_code)
    if connection.get(target_entity_key):
        actual_timestamps[tag] = connection.get(target_entity_key).decode('utf-8')

    else:
        target_timestamp = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=7)
        target_timestamp = target_timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')
        actual_timestamps[tag] = target_timestamp

    all_entities_key = _generate_timestamp_key('all_entities', hotel_code)
    connection.set(all_entities_key, pickle.dumps(actual_timestamps))

    return actual_timestamps


def refresh_entity_timestamp(hotel_code: str, tag: str) -> None:
    target_host = "https://notify-entity-change-lr52ctruda-ew.a.run.app"
    target_path = f'/?hotel_code={hotel_code}&entity={tag}&referrer=paraty-commons-3'
    try:
        requests.get(target_host + target_path, timeout=5)
    except Exception as e:
        logging.warning(f"Error while refreshing entity timestamp: {e}")
