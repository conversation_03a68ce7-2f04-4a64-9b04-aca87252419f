import datetime
import hashlib
import logging
import os
import pickle
import zlib
from typing import Callable
from cachetools import TTLCache

from flask import g, has_app_context
from threading import Lock
import inspect

from paraty_commons_3.audit_utils import make_traceback

try:
    from paraty import Config
except Exception:
    # This is used because unit test of module paraty_commons_3 are not able to import Config
    logging.info("Config not found, setting DEV to True")
    class Config:
        DEV = True

from paraty_commons_3.decorators.cache.managers_cache import redis_constants
from paraty_commons_3.decorators.cache.managers_cache.cache_entry_refresher import execute_in_background
import copy


'''
This cache is inspired in the cache used by Hotel Manager.
The idea is to use only Redis and no Datastore for caching

Eviction policy is based on 2 principles:
- TTL, we can define a max time for a given entry to live
- Entities, If one of the entries related to this entry has changed (the entry timestamp is older than the entity's timestamp), we consider the entry to be expired 

We avoid having to check the timestamps on every call, we will only check it every 1 minute and cache it
This way we reduce the traffic to redis

We define a hierarchy of memory
- Thread local
- Memory
- Redis

'''

GLOBAL_MAX_ENTRY_TTL = 7 * 24 * 60 * 60  # 1 week
IN_MEMORY_TTL = 60 * 60  # 1 hour
MAX_MEMORY_ENTRIES = 1024 ** 10  # No limit

if os.environ.get('MAX_MEMORY_ENTRIES'):
    MAX_MEMORY_ENTRIES = int(os.environ.get('MAX_MEMORY_ENTRIES'))


class ThreadSafeTTLCache:
    def __init__(self, maxsize, ttl):
        self.cache = TTLCache(maxsize, ttl)
        self.lock = Lock()

    def get(self, key):
        with self.lock:
            return self.cache.get(key)


    def set(self, key, value):
        with self.lock:
            self.cache[key] = value


# For the moment we suppose everything fits in memory
_memory_cache = ThreadSafeTTLCache(maxsize=MAX_MEMORY_ENTRIES, ttl=IN_MEMORY_TTL)

def _calculate_key(f, args, kwargs):
    file_name = os.path.basename(inspect.getfile(f)).replace(".py", "")
    result = f'{file_name}.{f.__name__}_{str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))}'
    if len(result) > 500:
        # If the key is too long, we will hash it
        result_hash = hashlib.sha256(result.encode('utf-8'))
        result = result[:50] + result_hash.hexdigest()
    return result


def managers_cache(
        hotel_code_provider: Callable,
        key_generator: Callable =_calculate_key,
        entities: str = "",
        ttl_seconds: int = GLOBAL_MAX_ENTRY_TTL,
        background_refresh: bool = True,
        in_memory: bool = True,
        requires_compression: bool = False,
        only_thread_local_and_memory: bool = False,
        only_thread_local: bool = False,
        deep_copy_data: bool = False):
    """
    Cache based on Hotel Manager's cache

    @param hotel_code_provider: A function to get the hotel code (it receives 3 params, f, args and kwargs, so a lambda would look like this: lambda f, a, k: a[0])
    @param deep_copy_data: If True we deepcopy the data before retrieving it from memory
    @param requires_compression: In case it is a large object we can compress it before storing it in Redis
    @param key_generator: A function to build the key (it receives 3 params, f, args and kwargs, so a lambda would look like this: lambda f, a, k: f'{f._name__}_a[0]')
    @param entities: Entity names separated by , (i.e. 'Rate,WebPageProperty')
    @param ttl_seconds: Max seconds for entries to live
    @param background_refresh: If True we accept invalid entries and refresh them in the background automatically
    @param in_memory: If False we do not store this in memory (in case it is very large or short-lived)
    @param only_thread_local: If True we only cache in thread local
    @param only_thread_local_and_memory: If True we only cache in thread local and memory, it will NOT be persisted to Redis (useful for short-lived intermediate calculations)

    NOTE: Do not modify entities returned by the cache, as these information might be shared among different threads. If this is absolutely necessary, make sure to deep copy the data before modifying it or set the deep_copy_data parameter to True.

    Recommendation: Do not nest persisted cache entries. i.e. Persist only the raw data and all its dependencies should be in memory (using only_thread_and_memory)

    @return: Wrapped function result
    """

    _cache_config = {
        'entities': entities,
        'ttl': ttl_seconds,
        'background_refresh': background_refresh,
        'in_memory': in_memory,
        'only_thread_local': only_thread_local,
        'requires_compression': requires_compression,
        'only_thread_local_and_memory': only_thread_local_and_memory,
        'hotel_code_provider': hotel_code_provider,
        'deep_copy_data': deep_copy_data
    }

    def wrap(f):
        def cached_result(*args, **kw):
            _nested_counter_update(1)

            if _is_nested_cache():
                _cache_config['background_refresh'] = False

            entry_key = key_generator(f, args, kw)
            hotel_code = hotel_code_provider(f, args, kw)
            if Config.DEV:
                entry_key = 'DEV_' + entry_key

            # We can not trust any key generator to set the namespace, so we set it explicitly
            entry_key = f'[manager_cache][{hotel_code}]__{entry_key}'

            result = _get_from_thread_cache(hotel_code, entry_key, f, args, kw, _cache_config)

            _nested_counter_update(-1)

            return result

        return cached_result

    return wrap


def _get_from_thread_cache(hotel_code: str, entry_key, f: Callable, args, kwargs, _cache_config: dict) -> object:

    # i.e. When we execute in parallel inside other threads
    if not has_app_context():
        from paraty import app
        with app.app_context():
            return _get_from_thread_cache(hotel_code, entry_key, f, args, kwargs, _cache_config)


    if not hasattr(g, 'requests_cache'):
        g.requests_cache = {}

    # If thread local return the entity, we don't have to check anything else because it has done when filled in thread local cache
    if entry_key in g.requests_cache:
        logging.debug(f"[manager_cache] {entry_key} found at thread cache")
        return g.requests_cache[entry_key]

    # In some cases we really don't want to cache anything, it is just for performance reasons, convenience storing intermedia  temp data
    if _cache_config['only_thread_local']:
            g.requests_cache[entry_key] = f(*args, **kwargs)
            return g.requests_cache[entry_key]

    entry: dict = _get_from_memory_cache(hotel_code, entry_key, f, args, kwargs, _cache_config)

    g.requests_cache[entry_key] = entry['value']

    return entry['value']


def _get_from_memory_cache(hotel_code, entry_key, f, args, kwargs, _cache_config) -> dict:

    entry: dict = _memory_cache.get(entry_key)
    if entry:
        if _is_entry_valid(hotel_code, entry['timestamp'], _cache_config['entities'], _cache_config['ttl']):
            logging.debug(f"[manager_cache] {entry_key} found at memory")
            return copy.deepcopy(entry)

    # Entry is a dictionary with value and timestamp
    if _cache_config['only_thread_local_and_memory']:
        entry = _calculate_value(hotel_code, entry_key, f, args, kwargs, _cache_config)
    else:
        entry = _get_from_redis(entry_key, f, args, kwargs, _cache_config)

    _memory_cache.set(entry_key, entry)

    return copy.deepcopy(entry)


def _get_related_entities(entities):
    if not entities:
        return []

    return entities.replace(';', ",").split(",")


def _calculate_expiration_timestamp(entry_timestamp: str, ttl: int) -> str:
    datetime_timestamp: datetime =  datetime.datetime.strptime(entry_timestamp, redis_constants.TIMESTAMP_FORMAT)
    final_datetime = datetime_timestamp + datetime.timedelta(seconds=ttl)
    return final_datetime.strftime(redis_constants.TIMESTAMP_FORMAT)


from paraty_commons_3.decorators.cache.managers_cache.redis_utils import get_entry_from_persistence, set_entry_in_persistence
from paraty_commons_3.decorators.cache.managers_cache.entities_utils import get_all_entities_timestamps, \
    refresh_missing_entity_timestamp


def _is_entry_valid(hotel_code: str, entry_timestamp: str, entities: str, ttl: int) -> bool:
    max_valid_timestamp = _calculate_expiration_timestamp(entry_timestamp, ttl)
    current_timestamp = _get_current_timestamp()
    if max_valid_timestamp < current_timestamp:
        logging.info(f"[manager_cache] {entry_timestamp} has expired, invalidating {current_timestamp}")
        return False

    if not entities:
        return True

    entity_timestamps = get_all_entities_timestamps(hotel_code)
    related_entities = _get_related_entities(entities)
    for entity in related_entities:
        has_timestamp_to_compare = entity_timestamps and entity in entity_timestamps and entity_timestamps[entity]
        if not has_timestamp_to_compare:
            entity_timestamps = refresh_missing_entity_timestamp(hotel_code, entity)
            has_timestamp_to_compare = entity_timestamps and entity in entity_timestamps and entity_timestamps[entity]

        if has_timestamp_to_compare and entity_timestamps[entity] > entry_timestamp:
            logging.info(f"[manager_cache] {entity} has changed, timestamp: {entity_timestamps[entity]}")
            logging.info(f"[manager_cache] entry, current: {entry_timestamp}")

            return False
    return True


def _get_from_redis(entry_key, f, args, kwargs, cache_config: dict) -> dict:

    hotel_code = cache_config['hotel_code_provider'](f, args, kwargs)
    entry: dict = _get_entry_from_redis(hotel_code, entry_key, cache_config)
    if entry and _is_entry_valid(hotel_code, entry['timestamp'], cache_config['entities'], cache_config['ttl']):
        logging.debug(f"[manager_cache] {entry_key} found at redis")
        return entry

    request_disabled_background_cache = getattr(g, 'disabled_background_refresh', False)
    environ_disabled_background_cache = os.environ.get('DISABLED_BACKGROUND_REFRESH', False)
    is_disabled_background_cache = request_disabled_background_cache or environ_disabled_background_cache

    if entry and cache_config['background_refresh'] and not is_disabled_background_cache:
        execute_in_background(hotel_code, entry_key, _calculate_value, entry_key, f, args, kwargs, cache_config)
    else:
        entry = _calculate_value(hotel_code, entry_key, f, args, kwargs, cache_config)

    return entry


def _get_current_timestamp():
    now = datetime.datetime.utcnow()
    return now.strftime(redis_constants.TIMESTAMP_FORMAT)


def _get_entry_from_redis(hotel_code:str, entry_key: str, cache_config: dict) -> dict | None:
    result = get_entry_from_persistence(hotel_code, entry_key)
    if result:
        if cache_config['requires_compression']:
            return pickle.loads(zlib.decompress(result))
        else:
            return pickle.loads(result)
    return None


def _set_entry_to_redis(hotel_code: str, entry_key: str, entry: dict, cache_config: dict):

    ttl_in_seconds: int = min(cache_config['ttl'], GLOBAL_MAX_ENTRY_TTL)
    if cache_config['requires_compression']:
        set_entry_in_persistence(hotel_code, entry_key, zlib.compress(pickle.dumps(entry)), ttl_in_seconds)
    else:
        set_entry_in_persistence(hotel_code, entry_key, pickle.dumps(entry), ttl_in_seconds)


def _calculate_value(hotel_code, entry_key, f, args, kwargs, cache_config) -> dict:

    logging.debug(f"[manager_cache] {entry_key} Calculating value!")
    entry = {
        'value': f(*args, **kwargs),
        'timestamp': _get_current_timestamp()
    }

    if not cache_config['only_thread_local_and_memory'] and not cache_config['only_thread_local']:
        _set_entry_to_redis(hotel_code, entry_key, entry, cache_config)

    if not cache_config['only_thread_local'] and cache_config['in_memory']:
        _memory_cache.set(entry_key, entry)

    return entry


def _nested_counter_update(counter: int):
    try:
        if not hasattr(g, 'nested_counter'):
            g.nested_counter = 0

        g.nested_counter += counter
    except Exception as e:
        logging.debug("Exception trying to set counter of cache")
        logging.debug(e)


def _is_nested_cache():
    try:
        if hasattr(g, 'nested_counter') and g.nested_counter > 1:
            return True

    except Exception as e:
        logging.debug("Exception trying to check counter of cache")
        logging.debug(e)