import logging
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Callable

import requests
from flask import g

try:
    from paraty import Config
except Exception:
    # This is used because unit test of module paraty_commons_3 are not able to import Config
    logging.info("Config not found, setting DEV to True")
    class Config:
        PROJECT = 'paraty-common3-unittest'

from paraty_commons_3.audit_utils import make_traceback

MAX_BACKGROUND_EXECUTOR_THREADS = 2

thread_pool_executor = ThreadPoolExecutor(MAX_BACKGROUND_EXECUTOR_THREADS)
executing_currently = {}


def execute_in_background(hotel_code: str, entry_key: str, f: Callable, *args, **kwargs):
    # No point in doing the same thing twice

    # logging.info("Executing currently %s" % len(executing_currently))

    if entry_key in executing_currently:
        logging.debug(f"Ignoring {entry_key}, already executing")
        return

    executing_currently[entry_key] = True  # TODO, change by timestamp
    thread_pool_executor.submit(_execute_and_inform, hotel_code, entry_key, f, *args, **kwargs)

def disable_request_background_cache():
    logging.info("Disable background refresh request")
    g.disabled_background_refresh = True

def enable_request_background_cache():
    logging.info("Enable background refresh request")
    g.disabled_background_refresh = False

def _execute_and_inform(hotel_code: str, entry_key: str, f, *args, **kwargs):
    try:
        logging.info(f"Executing in background: {entry_key}")
        from paraty import app
        with app.app_context():
            if hasattr(g, 'requests_cache'):
                g.requests_cache = {}

            # We need to make sure we do not enter into a loop (i.e. if a nested call also has cache)
            # Also need to make sure we do not cache not fresh data
            g.disabled_background_refresh = True

            f(hotel_code, *args, **kwargs)


    except Exception as e:
        logging.error(f"Error executing: {entry_key}")
        logging.error(e)
        logging.error(make_traceback())
    finally:
        executing_currently.pop(entry_key, None)
        logging.info(f"Finished executing: {entry_key}")


def refresh_entity_timestamps(entity: str, hotel_code: str, referrer: str = Config.PROJECT, sync: bool = False) -> bool:
    """
    Refresh the cache timestamp of the entity in redis and memory.

    :param entity: The entity to refresh
    :param hotel_code: The hotel code
    :param referrer: The referrer. I.e. who is refreshing the cache
    :param sync: If True refresh the cache synchronously, returning the result only when both remote and memory timestamps are refreshed
    :return: True if successful, False otherwise
    """
    target_url = 'https://notify-entity-change-lr52ctruda-ew.a.run.app'
    params = {
        'hotel_code': hotel_code,
        'entity': entity,
        'referrer': referrer
    }
    logging.info(f'Refreshing timestamps for {entity} in {hotel_code}')

    if sync:
        logging.info('Refreshing synchronously')
        params['sync'] = 'true'

    try:
        response = requests.get(target_url, params=params)
    except Exception as e:
        logging.error(f"Error refreshing timestamps for {entity} in {hotel_code}")
        logging.error(e)
        return False

    if response.status_code == 200:
        if sync:
            from paraty_commons_3.decorators.cache.managers_cache.entities_utils import get_all_entities_timestamps
            get_all_entities_timestamps(hotel_code, _force_refresh=True) # Refresh all timestamps in memory cache
        return True
    else:
        logging.error(f"Error refreshing timestamps for {entity} in {hotel_code}")
        logging.error(f'Status code: {response.status_code}')
        logging.error(response.text)
        return False



def refresh_all_entities_timestamps(hotel_code, extra_entities: list|None = None, referrer: str = Config.PROJECT) -> bool:
    """
    Will regenerate all entities timestamps in the cache if needed
    """
    target_url = 'https://notify-entity-change-lr52ctruda-ew.a.run.app/regenerate-all-entities-timestamps'

    extra_entities_query = f'extra_entities={",".join(extra_entities)}' if extra_entities else ''
    target_url = target_url + f'?hotel_code={hotel_code}&referrer={Config.PROJECT}&{extra_entities_query}'

    try:
        logging.info("Regenerating all entities timestamps in hotel %s" % hotel_code)
        requests.get(target_url)
        return True
    except Exception as e:
        pass

    return False