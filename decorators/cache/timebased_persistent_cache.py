from datetime import datetime, timed<PERSON>ta
from copy import deepcopy
import logging
from threading import RLock
from google.cloud import ndb

from paraty import Config

'''
Based on solution proposed at http://www.willmcgugan.com/blog/tech/2007/10/14/timed-caching-decorator/

Modified to support storing cache values at GAE's datastore


Option: In case we want to refresh the cache we can call the method with a named parameter forceClean  (i.e. myMethod(...., forceClean=True)


'''

class TimeBasedCacheEntry(ndb.Model):
	"""To improve performance we use caching for most commonly accessed data."""

	#Content
	jsonContent = ndb.JsonProperty(compressed=False)
	timestamp = ndb.DateTimeProperty(auto_now_add=True)
	compressedJsonContent = ndb.JsonProperty(compressed=True)

	#i.e. The reservation_hotel_code
	entryType = ndb.StringProperty()

	# To make sure entities are cleared using TTL of GAE, see https://console.cloud.google.com/datastore/databases/-default-/ttl?project=hotelads-adapter
	expirationDate = ndb.DateTimeProperty()


def remove_entry(key):
	result = TimeBasedCacheEntry.get_by_id(key)
	if result:
		result.key.delete()

def remove_entry_by_type(entry_type):
	ndb.delete_multi(
		TimeBasedCacheEntry.query(TimeBasedCacheEntry.entryType == entry_type).fetch(keys_only=True)
	)

def remove_all():
	ndb.delete_multi(
		TimeBasedCacheEntry.query().fetch(keys_only=True)
	)
	
	
def clean_cache_by_key(key):
	result = TimeBasedCacheEntry.get_by_id(key)
	if result:
		try:
			result.key.delete()
		except Exception as e:
			logging.error("missing key")
	
	

def persistent_timed_cache(keyGenerator=None, seconds=0, minutes=0, hours=0, days=0, compressed=False, entry_type=None):
	'''
	KeyGenerator -> A funtion used to create the key using the arguments received by the function as parameters
	entry_type -> A funtion used to create the type using the arguments received by the function as parameters

	NOTE: Disabled for local testing

	'''
	time_delta = timedelta(seconds=seconds, minutes=minutes, hours=hours, days=days)

	def decorate(f):

		f._lock = RLock()

		# @timeit
		def do_persistent_cache(*args, **kwargs):

			# lock = f._lock
			# lock.acquire()

			try:
				if keyGenerator:
					key = keyGenerator(args)
				else:
					key = f.__name__ + str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))

				result = None
				# logging.info("persistent_timed_cache, Requesting: %s", key)
				if not Config.TESTING:
					result = TimeBasedCacheEntry.get_by_id(key)


				if not result or (datetime.now() - result.timestamp > time_delta) or kwargs.get('forceClean'):

					logging.info("Missed key at persistent_timed_cache: %s" % key)

					if result:
						result.key.delete()

					# Calculate
					result = f(*args, **kwargs)

					if not Config.TESTING:
						new_entry = TimeBasedCacheEntry(key=ndb.Key('TimeBasedCacheEntry', key))

						expiration_date = timedelta(days=7)
						if time_delta > expiration_date:
							expiration_date = time_delta

						new_entry.expirationDate = datetime.now() + expiration_date

						if compressed:
							new_entry.compressedJsonContent = result
						else:
							new_entry.jsonContent = result

						if entry_type:
							new_entry.entryType = entry_type(args)


						new_entry.put_async()

					return result

				else:

					# logging.info("Found key: %s" % key)

					# Cache
					if compressed:
						return result.compressedJsonContent
					else:
						return result.jsonContent

			finally:
				pass
				# lock.release()

		return do_persistent_cache

	return decorate



