import datetime
import logging
import os
import pickle
import sys
from threading import RLock, Lock

from paraty_commons_3.utils.cache_utils import retrieve_all_cache

try:
	from google.cloud import ndb
except ImportError:
	logging.warning("Google Cloud NDB not found. Distributed cache will not be available")

try:
	from paraty import Config
except ImportError:
	class Config:
		pass
	logging.warning("Redis not found. Distributed cache will not be able to use it.")

from cachetools import TTLCache

from paraty_commons_3.redis.redis_communicator import build_redis_client
from paraty_commons_3.decorators.cache.cache_controller import is_entry_valid

'''

This cache can be used in distributed environments with explicit cache invalidation

It uses two levels of cache:
- A local cache (in memory) to avoid hitting the datastore
- A local datastore cache to avoid hitting each hotels datastore

Synchronized with a lock per key

Uses cache manager to invalidate the cache

'''

# NOTE this can only be enabled if the project supports clearing the cache using cache_controller, otherwise these entries will last forever
USE_PERSISTENCE = False

# If true, everything will be persisted in redis
# It expected a REDIS_URL environment variable to be set in CONFIG, HOST, PASSSWORD in Config
USE_REDIS = False


MAX_CACHE_SIZE = 1200
DEFAULT_MEMORY_LIFE_SECONDS = 600

USE_CACHE = True

# If true, we ignore the global timestamps and just use whatever was set at memory_life_seconds as the expiration date also in the persistence
# Can only be used with redis persistence or the cache will never be cleared (in the datastore)
FORCED_REDIS_TIMESTAMP_PERSISTENCE = False

all_results = []
all_locks = []
all_functions = []

class NonPersistentCacheEntry():
	pass


def disable_cache():
	global USE_CACHE
	USE_CACHE = False


def enable_cache():
	global USE_CACHE
	USE_CACHE = True


def _get_result_from_persistence(key):
	if USE_REDIS:
		redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)
		object = redis_client.get(key)
		if object:
			return pickle.loads(object)
		return None

	else:
		return DistributedCacheEntry.get_by_id(key)


def _save_result_in_persistence(key, result, redis_persistence_life_in_seconds):
	if USE_REDIS:
		redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)
		redis_client.setex(key, redis_persistence_life_in_seconds, pickle.dumps(result))
	else:
		result.put()


if 'google.cloud.ndb' in sys.modules:
	class DistributedCacheEntry(ndb.Model):

		content = ndb.PickleProperty(compressed=False)
		compressedContent = ndb.PickleProperty(compressed=True)

		timestamp = ndb.DateTimeProperty(auto_now=True)

		hotelCode = ndb.StringProperty()


def retreive_all_distribuited_local_cache(name_regex=None, include_value=False, include_size=False):
	return retrieve_all_cache(all_results, all_locks, name_regex, include_value, include_size)


def distributed_cache(hotel_code_generator=lambda x: x[0], key_generator=None, compressed=False, entities=[], memory_life_seconds=DEFAULT_MEMORY_LIFE_SECONDS):
	'''

	Key Generator -> A function used to create the key using the arguments received by the function as parameters
	Hotel Code Generator -> A funtion used to create the hotel code using the arguments received by the function as parameters
	Entities -> A list of entities to be cached

	'''
	def decorate(f):

		f._lock = Lock()

		f._results_entries = TTLCache(maxsize=MAX_CACHE_SIZE, ttl=memory_life_seconds)
		f._locks = TTLCache(maxsize=MAX_CACHE_SIZE, ttl=memory_life_seconds)

		# For global control of all methods using this decorator
		all_results.append(f._results_entries)
		all_locks.append(f._lock)
		all_functions.append(f)

		# @timeit
		def do_distributed_cache(*args, **kwargs):

			if key_generator:
				key = key_generator(args)
			else:
				key = f.__name__ + str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))

			if not key in f._locks:
				with f._lock:
					try:
						f._locks[key] = RLock()
					except Exception as e:
						# Prevent OrderedDict errors, we need to wait a bit and try again
						logging.warning("Error creating lock for key: %s, %s", key, e)
						import time
						time.sleep(1)
						f._locks[key] = RLock()

			with f._locks[key]:

				# 1. Check if the result is in the local cache
				result = f._results_entries.get(key)

				if not result and USE_PERSISTENCE:
					# 2. Check if the result is in the local datastore cache
					result = _get_result_from_persistence(key)
					if result:
						f._results_entries[key] = result

				hotel_code = hotel_code_generator(args)

				if result and USE_CACHE:

					if FORCED_REDIS_TIMESTAMP_PERSISTENCE:
						entry_is_valid = True
					else:
						entry_is_valid = is_entry_valid(hotel_code, str(result.timestamp), entities=entities)

					is_unit_test = os.environ.get('IS_UNIT_TEST', False)
					if is_unit_test and not entry_is_valid:
						logging.info("Unit test detected, will use cache")

					if entry_is_valid or is_unit_test:
						if compressed:
							return result.compressedContent
						else:
							return result.content

				logging.info("Missed key at distributed_cache: %s" % key)

				# Calculate
				function_result = f(*args, **kwargs)

				if result:
					new_entry = result
				else:
					if USE_PERSISTENCE and 'google.cloud.ndb' in sys.modules:
						new_entry = DistributedCacheEntry(key=ndb.Key('DistributedCacheEntry', key))
						new_entry.hotelCode = hotel_code
					else:
						new_entry = NonPersistentCacheEntry()
						new_entry.hotelCode = hotel_code

				if compressed:
					new_entry.compressedContent = function_result
				else:
					new_entry.content = function_result

				new_entry.timestamp = datetime.datetime.utcnow()

				# Note that this is synchronous to make sure that any waiting threads get the new value and the timestamp is updated
				if USE_PERSISTENCE:
					_save_result_in_persistence(key, new_entry, memory_life_seconds)
				else:
					new_entry.timestamp = datetime.datetime.utcnow()

				f._results_entries[key] = new_entry

				return function_result

		return do_distributed_cache

	return decorate
