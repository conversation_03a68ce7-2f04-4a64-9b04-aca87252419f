import logging
import threading


results = []

cache_thread_state = threading.local()


try:
    from paraty import app

    @app.before_request
    def init_tls():
        cache_thread_state.__dict__.clear()

except ImportError:
    # Expected when we are not in a flask app
    logging.warning("If you are in a flask environment, make sure that app is imported before this module")



cache_thread_state.cache = {}

all_key_locks = {}

'''
WARNING, IN FLEXIBLE MACHINES THIS WILL REQUIRE INITIALIZATION FOR EACH request.

You can do this by adding the following code:

'''



def clear_request_cache():
	if hasattr(cache_thread_state, "cache"):
		cache_thread_state.cache.clear()

def local_thread_cache(key_builder=None):

	def decorate(f):

		def do_cache(*args, **kwargs):

			if not hasattr(cache_thread_state, "cache"):
				cache_thread_state.cache = {}

			if key_builder:
				key = key_builder(args)
			else:
				key = f.__name__ + str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))

			if not key in all_key_locks:
				all_key_locks[key] = threading.Lock()

			with all_key_locks[key]:
				if key not in cache_thread_state.cache:
					result = f(*args, **kwargs)
					cache_thread_state.cache[key] = result
					return result

				else:
					return cache_thread_state.cache[key]

		return do_cache

	return decorate
