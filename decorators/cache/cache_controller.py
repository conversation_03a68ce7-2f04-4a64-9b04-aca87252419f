import pickle
import threading
from datetime import datetime

import logging

from paraty_commons_3.decorators.cache.timebased_cache import timed_cache

try:
    from paraty_commons_3.redis.redis_communicator import build_redis_client
except ImportError:
    logging.warning("Redis not found. cache_controller will not be able to use it.")

from paraty_commons_3.utils.cache_seeker import cache_seeker_utils

cache_thread_state = threading.local()

try:
    from paraty import Config
    from paraty import app


    @app.before_request
    def init_tls():
        cache_thread_state.__dict__.clear()

except ImportError as e:
    # Expected when we are not in a flask app
    logging.warning(e)
    logging.warning("If you are in a flask environment, make sure that app is imported before this module")




MIN_TIMESTAMPS_KEY = 'cache_min_timestamps'
GLOBAL_KEY = 'global_timestamp'

USE_REDIS = False

def is_entry_valid(hotel_code, data_timestamp, entities=[]):
    '''
    We suppose everything to be valid unless stated otherwise
    If no information for hotel we start over and force everything to be updated
    '''

    timestamps = _get_min_validity_timestamps(hotel_code)

    # Playing it safe, we force all the cache to update from now
    if not timestamps:
        _create_default_timestamps(hotel_code)
        return False

    if not data_timestamp or data_timestamp == 'None':
        logging.info("Entity without timestamp, forcing update")
        return False

    if timestamps[GLOBAL_KEY] > data_timestamp:
        logging.info("ENTRY NOT VALID: Global timestamp for hotel %s is %s, data timestamp is %s" % (hotel_code, timestamps[GLOBAL_KEY], data_timestamp))
        return False

    for entity in entities:
        if entity in timestamps and timestamps[entity] > data_timestamp:
            logging.info("ENTRY NOT VALID: %s timestamp for hotel %s is %s, data timestamp is %s" % (entity, hotel_code, timestamps[entity], data_timestamp))
            return False

    return True


def invalidate_cache(hotel_code, entities=[]):
    '''
    If no entities are specified, we suppose we want to invalidate everything for the hotel-code
    '''

    utcnow = str(datetime.utcnow())

    timestamps = _get_min_validity_timestamps(hotel_code)

    if not timestamps:
        _create_default_timestamps(hotel_code)
        return

    if entities:
        for entity in entities:
            timestamps[entity] = utcnow
    else:
        timestamps[GLOBAL_KEY] = utcnow

    key = '%s_%s' % (MIN_TIMESTAMPS_KEY, hotel_code)

    # Note that we are currently clearing (to be changed in the future both caches as we might have multiple versions some in redis and some not
    if USE_REDIS:
        try:
            redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)
            redis_client.setex(key, 3600*72, pickle.dumps(timestamps))
        except Exception as e:
            logging.error(e)

    # else:
    try:
        cache_seeker_utils.set_data(key, timestamps, seconds=3600*72, persistent=True)
    except Exception as e:
        logging.error(e)


def _create_default_timestamps(hotel_code):

    utcnow = str(datetime.utcnow())
    new_timestamps = {
        GLOBAL_KEY: utcnow
    }
    key = '%s_%s' % (MIN_TIMESTAMPS_KEY, hotel_code)

    if USE_REDIS:
        redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)
        redis_client.setex(key, 3600*72, pickle.dumps(new_timestamps))
    else:
        cache_seeker_utils.set_data(key, new_timestamps, seconds=3600*72, persistent=True)

    cache_thread_state.min_timestamps = {hotel_code: new_timestamps}
    return new_timestamps


# In case there are multiple requests for the same hotel, we cache the result for a few seconds, no need to be 100% real time with the cache (30 seconds is more than enough)
@timed_cache(seconds=30)
def _get_timestamps_from_remote_cache(hotel_code):

    key = '%s_%s' % (MIN_TIMESTAMPS_KEY, hotel_code)
    if USE_REDIS:
        redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)
        object = redis_client.get(key)
        if object:
            return pickle.loads(object)
    else:
        return cache_seeker_utils.get_data(key)


def _get_min_validity_timestamps(hotel_code):

    if not hasattr(cache_thread_state, "min_timestamps"):
        cache_thread_state.min_timestamps = {}

    if not hotel_code in cache_thread_state.min_timestamps:

        min_timestamps = _get_timestamps_from_remote_cache(hotel_code)

        logging.info("Min timestamps for hotel %s: %s" % (hotel_code, min_timestamps))

        cache_thread_state.min_timestamps[hotel_code] = min_timestamps
        if not min_timestamps:
            # If no information available we play it safe by forcing all data to be renewed
            return _create_default_timestamps(hotel_code)

    return cache_thread_state.min_timestamps.get(hotel_code, {})


if __name__ == '__main__':
    USE_REDIS = True
    invalidate_cache('entremares-hotel')
    # print(_get_min_validity_timestamps('entremares-hotel'))
    # is_entry_valid("prueba")


