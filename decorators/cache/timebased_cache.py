'''
Based on solution proposed at http://www.willmcgugan.com/blog/tech/2007/10/14/timed-caching-decorator/
But has been deeply modified:
- Use TTLCache instead of dictionary, as we were causing a memory leak (expired entries were never removed)
- Support a thread expiration time for requests that need short lived data
- Support clearing the cache
- Support disabling the cache
- Set max size (200 by default)

NOTE that this method is synchronized per function by default
'''
import logging
import threading
from datetime import timedelta, datetime
from functools import wraps
from threading import Lock
from cachetools import TTLCache
from flask import request, has_request_context

from paraty_commons_3.utils.cache_utils import retrieve_all_cache

try:
	from paraty import app

	@app.before_request
	def init_tls():
		local_thread_state.__dict__.clear()

except ImportError:
	# Expected when we are not in a flask app
	logging.warning("If you are in a flask environment, make sure that app is imported before this module")


all_results = []
all_locks = []
all_functions = []


CACHE_DISABLED = False
MAX_CACHE_SIZE = 200


def disable_cache():
	global CACHE_DISABLED
	CACHE_DISABLED = True


def enable_cache():
	global CACHE_DISABLED
	CACHE_DISABLED = False


def clear_cache(name_regex=None):

	total_entries = 0
	cleaned_entries = []

	for i, result in enumerate(all_results):
		with all_locks[i]:
			total_entries += len(result)
			cleaned_entries.extend(list(result))
			result.clear()

	result = {
		'total_entries_cleaned':total_entries,
		'entries': cleaned_entries
	}
	return result

def clear_cache_all():
	#this is for test other way for clean cachee

	total_entries = 0
	cleaned_entries = []

	for result in all_results:
		for lock in all_locks:
			with lock:
				total_entries += len(result)
				cleaned_entries.extend(list(result))
				result.clear()

	for function in all_functions:
		with function._lock:
			if hasattr(function, "_result"):
				del function._result

	result = {
		'total_entries_cleaned':total_entries,
		'entries': cleaned_entries
	}
	return result

def clear_cache_selective(name_regex):
	'''
	To clear cache with key name that match with the regex given only

	IMPORTANT!
	This doesn't clear the cache, only update it, so the ttl will not be refreshed
	'''
	total_entries = 0
	cleaned_entries = []

	for i, result in enumerate(all_results):
		for key_cached in result:
			if name_regex in key_cached:
				with all_locks[i]:
					result.pop(key_cached)
					total_entries += 1
					cleaned_entries.append(key_cached)

	result = {
		'total_entries_cleaned':total_entries,
		'entries': cleaned_entries
	}
	return result




def retreive_all_timed_cache(name_regex=None, include_value=False, include_size=False):
	return retrieve_all_cache(all_results, all_locks, name_regex, include_value, include_size)


local_thread_state = threading.local()





# This is helpful for development environments that share the same server, for development we want caché to be short lived
def set_thread_max_expiration_time(seconds):
	local_thread_state.THREAD_MAX_EXPIRATION_TIME = seconds


def timed_cache(seconds=0, minutes=0, hours=0, days=0, key_builder=None, synchronize_calls=False, max_size=MAX_CACHE_SIZE, key_builder_with_optional=None):
	time_delta = timedelta(seconds=seconds,
	                       minutes=minutes,
	                       hours=hours,
	                       days=days)

	def decorate(f):

		f._lock = Lock()

		# Note that this caché is not thread safe so we need to make all operations under lock
		f._results = TTLCache(maxsize=max_size, ttl=time_delta.total_seconds())

		f._timestamp = {}

		# For global control of all methods using this decorator
		all_results.append(f._results)
		all_locks.append(f._lock)
		all_functions.append(f)

		@wraps(f)
		def do_cache(*args, **kwargs):

			force_refresh = kwargs.pop("_force_refresh", False) or (has_request_context() and request.values.get("force_refresh"))

			if CACHE_DISABLED:
				return f(*args, **kwargs)

			if key_builder:
				key = key_builder(args)

			elif key_builder_with_optional:
				key = key_builder_with_optional(args, kwargs)

			else:
				key = f.__name__ + str((args, tuple(sorted(list(kwargs.items()), key=lambda i: i[0]))))

			with f._lock:
				key_in_cache = key in f._results

			# Enforce thread local max time if defined
			if key_in_cache:
				now = datetime.now()
				is_expired_for_thread = hasattr(local_thread_state, "THREAD_MAX_EXPIRATION_TIME") and (now - f._timestamp[key]).seconds > local_thread_state.THREAD_MAX_EXPIRATION_TIME
				if is_expired_for_thread:
					key_in_cache = False  # we consider expired everything older than the thread max expiration time

			# Key not found in the cache
			if not key_in_cache or force_refresh:
				# Calculate
				result = f(*args, **kwargs)

				with f._lock:
					f._results[key] = result
					f._timestamp[key] = datetime.now()

				return result

			# The data was available in the cache
			with f._lock:
				return f._results[key]

		return do_cache

	return decorate