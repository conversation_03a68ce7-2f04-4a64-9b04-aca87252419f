import collections

try:
	from collections import Set, Mapping
except ImportError:
	from collections.abc import Set, Mapping


class LazyDict(dict):
	'''
	Dictionary that evaluates values lazily
	'''
	def __getitem__(self, y):
		val = dict.__getitem__(self, y)
		if callable(val):
			val = val()
			self[y] = val
		return val

class ImmutableDict(Mapping):
	'''
	The same as a dictionary, except that if you try to modify it once created it throws an exception
	'''
	def __init__(self, somedict):
		self._dict = dict(somedict)   # make a copy
		self._hash = None

	def __getitem__(self, key):
		return self._dict[key]

	def has_key(self, key):
		return key in self._dict

	def copy(self):
		return ImmutableDict(self._dict)

	def __len__(self):
		return len(self._dict)

	def __iter__(self):
		return iter(self._dict)

	def __hash__(self):
		if self._hash is None:
			self._hash = hash(frozenset(self._dict.items()))
		return self._hash

	def __eq__(self, other):
		return self._dict == other._dict


class AuditedDict(Mapping):
	'''
	A dictionary that can can provide information about who has read its properties
	'''
	def __init__(self, somedict):
		self._dict = dict(somedict)   # make a copy
		self._hash = None
		self._readProperties = {}
		self._writenProperties = Set()


	def __getitem__(self, key):

		if not key in self._readProperties:
			self._readProperties[key] = 0
		self._readProperties[key] += 1

		return self._dict[key]

	def copy(self):
		return ImmutableDict(self._dict)

	def __len__(self):
		return len(self._dict)

	def __iter__(self):
		return iter(self._dict)

	def __hash__(self):
		if self._hash is None:
			self._hash = hash(frozenset(self._dict.items()))
		return self._hash

	def __eq__(self, other):
		return self._dict == other._dict

	def propertiesRead(self):
		'''
		Returns a dictionary of Keys and the number of times they have been read
		'''
		return self._readProperties

	def unreadProperties(self):
		'''
		Returns a Set with all the keys that haven't been used
		'''
		my_list = [myKey for myKey in self._dict.keys() if not myKey in self._readProperties]
		result = set(my_list)
		return result