import io
import zipfile


def read_file(file_name):
	pass

def write_file(file_name, content):
	pass

def delete_file(file_name):
	pass


#
#
# import os
#
# def test_read_write_tmp(requests):
#     file_path = '/tmp/test'
#     content = 'Hello World'
#
#     #  make sure dir exist
#     os.makedirs(os.path.dirname(file_path), exist_ok=True)
#     with open(file_path, 'wb') as f:
#         f.write(content)
#
#     if os.path.exists(file_path):
#         with open(file_path, 'rb') as f:
#             content = f.read()
#
#     return content