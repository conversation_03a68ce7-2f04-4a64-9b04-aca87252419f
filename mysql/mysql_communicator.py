import numpy as np
import requests
from google.cloud.datastore import Entity

from datastore.datastore_communicator import build_key
from datastore.datastore_utils import id_to_entity_key


def _parse_filters(filters):
	parsed_filters = []
	for filter in filters:
		parsed_filters.append({
			'column': filter[0],
			'operator': filter[1],
			'value': filter[2]
		})

	return parsed_filters


def get_using_entity_and_params(entity_name, search_params=[], hotel_code=None, return_cursor=False, order_by=None, limit=None, transform_to_datastore_entity=True):
	'''
	i.e.
	get_using_entity_and_params('UserModel', [('timestamp','>', '2019')])

	Returns an iterator
	'''

	json = {
		"hotel_code": hotel_code,
		"entity": entity_name,
		"filters": _parse_filters(search_params)
	}

	entities = requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/get-entity-by-filters", timeout=60, json=json).json()

	if transform_to_datastore_entity:
		datastore_entities = []
		for entity in entities:
			datastore_entities.append(_convert_to_datastore_entity(entity_name, entity))

		return datastore_entities
	else:
		return entities


def delete_entity(class_name, entity_id, hotel_code=None):
	json = {
		"hotel_code": hotel_code,
		"entity": class_name,
		"filters": [{
			'column': 'id',
			'operator': '=',
			'value': entity_id
		}]
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/delete-entity-by-filters", timeout=60, json=json)


def get_entity_by_key(my_key, hotel_code, transform_to_datastore_entity=True):
	json = {
		"hotel_code": hotel_code,
		"entity": my_key.kind,
		"filters": [{
			'column': 'id',
			'operator': '=',
			'value': my_key.id
		}]
	}

	entity = requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/get-entity-by-filters", timeout=60, json=json).json()

	if entity and transform_to_datastore_entity:
		return _convert_to_datastore_entity(my_key.kind, entity[0])
	elif not transform_to_datastore_entity:
		return entity[0]
	else:
		return ""

def get_entity(class_name, entity_id, hotel_code=None, transform_to_datastore_entity=True):
	json = {
		"hotel_code": hotel_code,
		"entity": class_name,
		"filters": [{
			'column': 'id',
			'operator': '=',
			'value': entity_id
		}]
	}

	entities = requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/get-entity-by-filters", timeout=60, json=json).json()

	if entities and transform_to_datastore_entity:
		return _convert_to_datastore_entity(class_name, entities[0])

	return entities


def _convert_to_datastore_entity(entity_name, db_entity):
	new_key = build_key(entity_name, db_entity["id"], db_entity["hotel_code"])

	del db_entity["hotel_code"]
	del db_entity["id"]
	del db_entity["legacy_id"]
	datastore_entity = Entity(key=new_key)
	datastore_entity.update(db_entity)
	return datastore_entity


def delete_entity_multi(list_of_entity_keys, hotel_code=None):
	json = {
		"hotel_code": hotel_code,
		"entity": list_of_entity_keys[0].kind,
		"filters": [{
			'column': 'id',
			'operator': 'IN',
			'value': [my_key.id for my_key in list_of_entity_keys]
		}]
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/delete-entity-by-filters", timeout=60, json=json)


def save_to_datastore(entity_name, id, properties, hotel_code=None, exclude_from_indexes=None):
	_generate_id(hotel_code, entity_name, properties, id)
	json = {
		"hotel_code": hotel_code,
		"entity": entity_name,
		"items": [properties]
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/insert-entity", timeout=60, json=json).json()[0]


def save_multiple_entities(entity_name, ids, entities_properties, hotel_code=None):
	if not ids:
		return

	items = []
	for i, current_properties in enumerate(entities_properties):
		new_key = build_key(entity_name, ids[i], hotel_code)
		current_properties["id"] = new_key.id
		current_properties["legacy_id"] = id_to_entity_key(hotel_code, new_key)
		items.append(current_properties)

	json = {
		"hotel_code": hotel_code,
		"entity": entity_name,
		"items": items
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/insert-entity", timeout=60, json=json).json()


def save_entity(new_entity, hotel_code=None, force_excluded_from_index=None):
	_generate_id(hotel_code, new_entity.kind, new_entity, new_entity.key)
	properties = dict(new_entity)

	json = {
		"hotel_code": hotel_code,
		"entity": new_entity.kind,
		"items": [properties]
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/insert-entity", timeout=60, json=json).json()[0]


def save_entity_multi(multiple_entities, hotel_code=None):
	items = []
	for entity in multiple_entities:
		_generate_id(hotel_code, entity.kind, entity, entity.key)
		properties = dict(entity)
		items.append(properties)

	json = {
		"hotel_code": hotel_code,
		"entity": multiple_entities[0].kind,
		"items": items
	}

	return requests.post(f"https://europe-west1-webpageproperty-seeker.cloudfunctions.net/insert-entity", timeout=60, json=json).json()


def _generate_id(hotel_code, entity_name, entity, id):
	if "id" not in entity.keys():
		if isinstance(entity, Entity):
			id = entity.id
			legacy_id = id_to_entity_key(hotel_code, entity.key)
		else:
			id = id if id else np.random.randint(10**15, 10**16)
			legacy_id = id_to_entity_key(hotel_code, build_key(entity_name, id, hotel_code))

		entity['id'] = id
		entity['legacy_id'] = legacy_id
