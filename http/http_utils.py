import asyncio
import logging
import threading
import time
# from concurrent.futures import ThreadPoolExecutor

import aiohttp

from paraty import Config

async def fetch(url, session, headers=None):
    if Config.DEV:
        logging.info(f"Fetching url: {url}")
    try:
        async with session.get(url, headers=headers) as response:
            return await response.text()
    except Exception as e:
        logging.error(f"Error fetching url: {url}, type: {type(e)}, error: {e}")
        return None


def _build_session(timeout):
    if Config.DEV:
        # For local testing
        import ssl
        import certifi
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        return aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context), timeout=aiohttp.ClientTimeout(total=timeout))
    else:
        client_timeout = aiohttp.ClientTimeout(total=timeout, connect=timeout)
        return aiohttp.ClientSession(timeout=client_timeout)


async def call_urls_async2(urls, timeout=20, max_concurrency=5, headers=None, total_timeout=180):
    """ Async function that needs to run in a new thread with its own event loop. """
    async with _build_session(timeout) as session:
        tasks = []
        # semaphore = asyncio.Semaphore(max_concurrency)  # Limit concurrency to 5

        if not urls:
            return []

        for url in urls:
            # async with semaphore:
                tasks.append(asyncio.ensure_future(fetch(url, session, headers=headers)))

        # Wait for the tasks to complete with a timeout
        done, pending = await asyncio.wait(tasks, timeout=total_timeout)

        responses = []
        for task in tasks:
            if task.done() and task.exception() is None:
                responses.append(task.result())
            else:
                responses.append(None)

        # Optionally cancel all pending tasks after the timeout
        for task in pending:
            task.cancel()

    return responses


# my_thread_pool = ThreadPoolExecutor(max_workers=20)


def call_urls_async_in_new_thread(urls, headers=None, timeout=20, total_timeout=30, max_concurrency=7):
    """
    Set up and run an asyncio event loop in a new thread.
    If everything async is running in one single thread it will end up being blocked by other threads
    (i.e. 15 request threads + 1 async thread), so we create each async code in a new thread (1 async thread per request thread)
    """
    result = []

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    # loop = asyncio.get_event_loop()
    try:
        task = asyncio.wait_for(call_urls_async2(urls, timeout=timeout, max_concurrency=max_concurrency, headers=headers, total_timeout=total_timeout), timeout=30)
        result2 = loop.run_until_complete(task)
        result.extend(result2)

    except Exception as e:
        print(e)
    finally:
        loop.close()

    # Verify that the loop is closed
    # if loop.is_closed():
    #     print("Event loop closed successfully.")
    # else:
    #     print("Event loop was not closed.")

    # result = my_thread_pool.submit(start_loop)

    # thread = threading.Thread(target=start_loop)
    # thread.start()
    # thread.join()

    return result


async def call_urls_async(urls: list[str], max_concurrency=5, headers=None, timeout=20, total_timeout=180) -> list[str | BaseException]:
    """
    See examples of usage in the main function of this file
    Note: When calling from Flask, call_urls_async_in_new_thread provides better performance
    """

    async with _build_session(timeout) as session:
        tasks = []
        semaphore = asyncio.Semaphore(max_concurrency)  # Limit concurrency to 5

        start_time = time.time()

        if not urls:
            return []

        for url in urls:

            # if Config.DEV and 'marinas-de-nerja' in url:
            #     url = "https://httpstat.us/200?sleep=50000" # 50 seconds

            async with semaphore:
                tasks.append(asyncio.ensure_future(fetch(url, session, headers=headers)))

        end_time = time.time()  # capture the current time again
        elapsed_time = end_time - start_time  # find the difference in times
        if elapsed_time > 10:
            logging.info(f"Slow before wait: The code took {elapsed_time} seconds to execute: {urls}, timeout {timeout}, max_concurrency {max_concurrency}")

                # Wait for the tasks to complete with a timeout
        done, pending = await asyncio.wait(tasks, timeout=total_timeout)

        end_time = time.time()  # capture the current time again
        elapsed_time = end_time - start_time  # find the difference in times
        if elapsed_time > 10:
            logging.info(f"Slow wait: The code took {elapsed_time} seconds to execute: {urls}")

        responses = []
        for task in tasks:
            if task.done() and task.exception() is None:
                responses.append(task.result())
            else:
                responses.append(None)

        end_time = time.time()  # capture the current time again
        elapsed_time = end_time - start_time  # find the difference in times
        if elapsed_time > 10:
            logging.info(f"Slow before canceling: The code took {elapsed_time} seconds to execute: {urls}")


        # Optionally cancel all pending tasks after the timeout
        for task in pending:
            task.cancel()

        end_time = time.time()  # capture the current time again
        elapsed_time = end_time - start_time  # find the difference in times
        if elapsed_time > 10:
            logging.info(f"SLOW CALLS: The code took {elapsed_time} seconds to execute: {urls}")
        elif Config.DEV:
            logging.info(f"FAST CALLS: The code took {elapsed_time} seconds to execute: {urls}")

        return responses


def _example_of_simple_sync_call(urls):
    print("Executing example of simple sync call...")
    result = asyncio.run(call_urls_async(urls, timeout=20))
    time.sleep(2)
    print(f"Result: {type(result)}")


async def _example_of_async_call(urls):

    print("Executing example of async call...")
    fetch_future = asyncio.ensure_future(call_urls_async(urls, timeout=20))
    print(f"Result: {fetch_future}")
    print("Doing other stuff while waiting for the result...")
    await asyncio.sleep(2)
    result = await fetch_future
    print(f"Result: {type(result)}")


if __name__ == "__main__":

    urls = ['https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=casas-novas&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=casas-novas&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=mobile&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=mw-douro&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=mw-douro&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=mobile&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel.appspot.com/search/?applicationId=luna-serra&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel.appspot.com/search/?applicationId=luna-serra&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=mobile&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=valderrabanos&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel.appspot.com/search/?applicationId=ar-arcos&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=hotel-corregidor&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=checkin-madrid-mostoles&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=checkin-princesa&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=checkin-princesa&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=mobile&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel.appspot.com/search/?applicationId=ar-parquesur&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=', 'https://integrations-dot-admin-hotel3.appspot.com/search/?applicationId=oriente-hostal&countryCode=es&startDate=05%2F06%2F2024&endDate=06%2F06%2F2024&numRooms=1&adultsRoom1=2&adultsRoom2=0&adultsRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&ignoreStats=True&device=Web&source=HotelAds&promocode=']

    # # Synchronous code
    # time_start = time.time()
    # _example_of_simple_sync_call(urls)
    # time_end = time.time()
    # print(f"Synchronous Time taken: {time_end - time_start}")

    time_start = time.time()
    result = call_urls_async_in_new_thread(urls, headers={"Authorization": "Basic cGFjbzpwYWNv"}, timeout=20, total_timeout=30, max_concurrency=7)
    print(result)
    time_end = time.time()
    print(f"New thread asyncio Time taken: {time_end - time_start}")

    # Asynchronous code
    time_start = time.time()
    asyncio.run(call_urls_async(urls, headers={"Authorization": "Basic cGFjbzpwYWNv"}, timeout=20, total_timeout=30, max_concurrency=7))
    time_end = time.time()
    print(f"Asynchronous Time taken: {time_end - time_start}")

