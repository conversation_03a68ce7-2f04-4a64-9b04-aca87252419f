'''
Useful to render templates in any code (i.e. Google cloud functions)
'''

from jinja2 import Environment, FileSystemLoader, select_autoescape

from paraty import Config

env = None


def render_template(template_name, context_data, template_folder=Config.TEMPLATES_PATH):
	global env
	if not env:
		env = Environment(
			loader=FileSystemLoader(template_folder),
			autoescape=select_autoescape(['html', 'xml'])
		)

	template = env.get_template(template_name)
	html = template.render(**context_data)
	return html