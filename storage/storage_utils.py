import io
import ntpath
import uuid
import zipfile

from google.cloud import storage
from paraty import Config
from paraty_commons_3.datastore import datastore_communicator


def upload_to_storage(hotel_code, file_path, bucket_name):

	# Create a Cloud Storage client.
	gcs = storage.Client()

	# Get the bucket that the file will be uploaded to.
	bucket = gcs.get_bucket(bucket_name)

	with open(file_path) as f:
		file_content = f.read()

	obfuscated_url = str(uuid.uuid4()) + str(uuid.uuid4()) + "." + file_path.split(".")[1]

	# Create a new blob and upload the file's content.
	blob = bucket.blob(obfuscated_url)

	blob.upload_from_string(
		file_content,
		content_type="application/csv;filename=%s" % ntpath.basename(file_path)
	)

	datastore_communicator.save_to_datastore('UrlTranslation', id=obfuscated_url, properties={'filename': file_path, 'obfuscated': blob.public_url, 'hotel_code': hotel_code})

	# The public URL can be used to directly access the uploaded file via HTTP.
	return blob.public_url

def get_all_files_in_bucket(bucket_name):
	# List all the files in a bucket
	storage_client = storage.Client()

	blobs = storage_client.list_blobs(bucket_name)

	return blobs


def get_blob_in_path(path, bucket_name):
	storage_client = storage.Client(Config.PROJECT)
	bucket = storage_client.get_bucket(bucket_name)
	return bucket.get_blob(path)


def save_text_file(bucket_name, full_file_path, content, content_type='application/json'):

	storage_client = storage.Client()
	bucket = storage_client.bucket(bucket_name)
	thumbnail_blob = bucket.blob(full_file_path)
	thumbnail_blob.upload_from_string(content, content_type=content_type)


def save_in_zip_file(bucket_name, full_file_path, content):

	f = io.BytesIO()
	z = zipfile.ZipFile(f, 'w', zipfile.ZIP_DEFLATED)

	z.writestr(full_file_path.split("/")[-1], content)
	z.close()
	f.seek(0)

	storage_client = storage.Client()
	bucket = storage_client.bucket(bucket_name)
	thumbnail_blob = bucket.blob(full_file_path.replace(".json", ".zip"))
	thumbnail_blob.upload_from_file(f, content_type='application/zip')


if __name__ == '__main__':
	file_name = '/tmp/hotel-don-pancho_2022_03_03_18_46.csv'