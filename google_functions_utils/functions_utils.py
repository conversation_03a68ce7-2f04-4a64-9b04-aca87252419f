from datetime import datetime

import requests

from paraty_commons_3 import queue_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_all_hotels


def create_task_for_each_hotel(queue_name, url_creator, include_non_production):

	if include_non_production:
		hotels = get_all_hotels()
		all_valid_hotels = [x for x in list(hotels.values()) if x.get('enabled')]
	else:
		all_valid_hotels = get_all_valid_hotels()


	timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

	for hotel in all_valid_hotels:
		queue_utils.create_task_with_url_get_target(queue_name, url=url_creator(hotel['applicationId']), name='%s__%s' % (hotel['applicationId'], timestamp))

	return 'OK'