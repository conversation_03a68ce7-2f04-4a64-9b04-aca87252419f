import pickle
from google.cloud import ndb


class PicklePropertyCustom(ndb.PickleProperty):
    def _to_base_type(self, value):
        return pickle.dumps(value, protocol=2)

    def _from_base_type(self, value):
        if isinstance(value, dict):
            return value
        return pickle.loads(value, fix_imports=True)


class UserSession(ndb.Model):
    # Default
    content = PicklePropertyCustom(compressed=False)

    timestamp = ndb.DateTimeProperty(auto_now_add=True)

    # Pickle doesn't work the same out of standard GAE, so we need something more "pure"
    content2 = ndb.BlobProperty(indexed=False)

    # In case we need to save it compressed
    compressedContent = ndb.BlobProperty(indexed=False)

    # In case we need to save it compressed (not pickled)
    compressedContent2 = ndb.BlobProperty(indexed=False)

    def __getitem__(self, item):
        return getattr(self, item)

    def __setitem__(self, key, value):
        setattr(self, key, value)