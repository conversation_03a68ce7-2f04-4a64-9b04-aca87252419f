import os

import requests


def get_session_from_hotel(hotel_code, sid):
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
    target_endpoint = "https://europe-west1-build-tools-2.cloudfunctions.net/read_session_from_hotel_handler"
    target_endpoint = f'{target_endpoint}?sid={sid}&namespace={hotel_code}&source={project_id}'
    return requests.get(target_endpoint, timeout=20).json()


def write_session_to_hotel(hotel_code, sid, data):
    """Data use update so, will not overwrite existing data while the key name is different"""
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
    target_endpoint = "https://europe-west1-build-tools-2.cloudfunctions.net/write_session_from_hotel_handler"
    target_endpoint = f'{target_endpoint}?sid={sid}&namespace={hotel_code}&source={project_id}'
    return requests.post(target_endpoint, json=data, timeout=60)