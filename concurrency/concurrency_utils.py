import logging
import threading
try:
	from paraty import app
except Exception as e:
	logging.debug("Error import app")
	logging.debug(e)

__author__ = 'fmatheis'

def func_star( a_b, myFunction):
	"""Convert `f([1,2])` to `f(1,2)` call."""
	return myFunction(*a_b)


def auxiliary_function(*args, **kwargs):
	function_to_call = args[0]
	id = args[1]
	result = args[2]
	params = args[3:]
	try:
		with app.app_context():
			result[id] = function_to_call(*params)
	except Exception as e:
		logging.debug("Execute without context")
		logging.debug(e)
		result[id] = function_to_call(*params)

def execute_in_parallel(my_function, params, max_concurrency=10, max_waiting_time=60):
	'''
	Executes the given function in parallel with the provided params

	params = Tuple of tuples (each of them for one call to myFunction).

	i.e. concurrency_utils.execute_in_parallel(hotel_manager_utils.get_integration_configuration_of_hotel, params)
	where params is: ((hotel1, "X"), (hotel2, "X"), (hotel3, "X"))

	The function waits for the tasks to finish then it returns a list with the result for each call
	'''

	logging.info("Executing in parallel (Danger, In server it creates multiple threads): %s", my_function)

	all_threads = []

	result = {}

	counter = 0

	global_counter = 0

	for current_params in params:
		counter += 1
		global_counter += 1

		#We need to be able to return the value
		call_params = [my_function, global_counter, result]
		call_params.extend((current_params))

		myThread = threading.Thread(group=None, target=auxiliary_function, args=call_params)
		myThread.start()
		all_threads.append(myThread)

		if counter == max_concurrency:
			# We wait maximum 60 seconds for any hotel
			# logging.info("Performing %s calls", max_concurrency)
			list([x.join(max_waiting_time) for x in all_threads])

			all_threads = []

			counter = 0

	list([x.join(max_waiting_time) for x in all_threads])

	indexes = list(result.keys())
	indexes.sort()

	return [result[x] for x in indexes]



def a_b(a, b):
	print(('Called with: %s, %s' % (a, b)))
	return a + b




if __name__=="__main__":

	result = execute_in_parallel(a_b, [[1,2], [3,4], [3,4], [9,4], [3, 1]])

	print(result)










