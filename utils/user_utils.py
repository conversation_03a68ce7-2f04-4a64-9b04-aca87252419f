from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: 'admin-hotel', ttl_seconds=60*10)
def get_user_info(user_name: str) -> dict:
    if not user_name:
        return {}

    user = datastore_communicator.get_using_entity_and_params('ParatyUser', search_params=[("name", "=", user_name)], hotel_code='admin-hotel')

    if not user:
        return {}

    user = user[0]
    user_dict = {
        'name': user.get('name'),
        'permission': user.get('permission', []),
        'configuration_map': {}
    }

    for config in user.get("configurationMap", []):
        config_list = config.split(" @@ ")
        if len(config_list) == 2:
            user_dict['configuration_map'][config_list[0].strip()] = config_list[1].strip()

    return user_dict



if __name__ == '__main__':
    print(get_user_info('admin'))