import logging
import pickle
import requests

try:
	from paraty.config import Config
except ImportError:
	logging.exception("Could not import Config from paraty, cache_seeker_utils will not work")

CACHE_SEEKER_URL = 'https://cache-seeker.appspot.com'

SECURITY = {
	'Authorization': 'Basic cGFjb3BlY286Y2hpY29yaWNv'
}


def _build_key(key, binary=False):
	return '%s_%s' % (Config.PROJECT, key)


# Make sure that each key is unique (i.e. including the hotel_code)
def set_data(key, value, seconds=1800, persistent=False):

	url = '%s/set_value?key=%s&ttl=%s' % (CACHE_SEEKER_URL, _build_key(key), seconds)

	if persistent:
		url += "&persistent=True"

	body_to_send = value
	headers = dict(SECURITY)
	if isinstance(value, str):
		logging.info("setting Str in cache seekers: %s", url)

	else:
		body_to_send = pickle.dumps(value, 4)
		logging.info("setting Pickle Data in cache seekers: %s", url)
		headers['Content-Type'] = 'application/octet-stream'

	requests.post('%s/set_value?key=%s&ttl=%s' % (CACHE_SEEKER_URL, _build_key(key), seconds), data=body_to_send, headers=headers, verify=False, timeout=60)

	return key


def get_data(key):

	url = '%s/get_value?key=%s' % (CACHE_SEEKER_URL, _build_key(key))
	logging.info("getting from cache seekers: %s", url)
	try:
		response = requests.get(url, headers=SECURITY, verify=False, timeout=20)
	except Exception as e:
		logging.warning(e)
		return None

	if response.status_code != 200:
		return None

	content_type = response.headers.get('Content-Type', '')
	if content_type == 'application/octet-stream':
		try:
			return pickle.loads(response.content)
		except Exception as e:
			logging.exception(e)
			return None
	else:
		return response.content.decode('utf8')