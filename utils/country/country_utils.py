import json
import logging
import os

from paraty_commons_3 import audit_utils
from paraty_commons_3.language_utils import get_language_code
from paraty_commons_3.utils.country.countries.default_countries_dict import ALL_COUNTRIES_LIST


def get_countries_by_language(language, return_list=False):
	language_code = get_language_code(language)

	country_dict = ALL_COUNTRIES_LIST
	try:
		with open(os.path.dirname(os.path.abspath(__file__)) + '/countries/countries_%s.json' % language_code, 'r') as countries:
			country_dict = json.load(countries)

	except Exception as e:
		logging.warning("Something failed trying to load country list by language")
		logging.warning(e)
		message = audit_utils.make_traceback()
		logging.warning(message)

	country_dict = dict(sorted(country_dict.items(), key=lambda x: x[1]))

	if return_list:
		country_list = []
		for key, value in country_dict.items():
			country_list.append({"code": key, "name": value})
		return country_list
	else:
		return country_dict


def get_prefix_country_list(language):
	prefix_country_list = []
	try:
		with open(os.path.dirname(os.path.abspath(__file__)) + '/phone_codes/phone_country_codes.json', 'r') as phone_country_codes:
			prefix_country_list = list(json.load(phone_country_codes))

	except Exception as e:
		logging.warning("Something failed trying to load phone country list")
		message = audit_utils.make_traceback()
		logging.warning(message)

	if language:
		translated_countries = get_countries_by_language(language)
		for prefix_country in prefix_country_list:
			country_code = prefix_country.get('code')
			if translated_countries.get(country_code):
				prefix_country['name'] = translated_countries.get(country_code)

	prefix_country_list.sort(key=lambda x: x['name'])

	return prefix_country_list


def get_cities_by_country_code(country_code):
	country_code = country_code.upper()

	try:
		with open(os.path.dirname(os.path.abspath(__file__)) + '/cities/countries_cities.json', 'r') as countries_cities:
			countries_cities = json.load(countries_cities)

	except Exception as e:
		logging.warning("Something failed trying to load cities list")
		message = audit_utils.make_traceback()
		logging.warning(message)

	cities_list = countries_cities.get(country_code, {}).get("cities", [])

	return cities_list


def get_country_code_by_country_name(country_name, language):
	country_name = country_name.upper()

	for code, country in get_countries_by_language(language).items():
		if country.upper() == country_name:
			return code

	for code, country in ALL_COUNTRIES_LIST.items():
		if country.upper() == country_name:
			return code

	return ""


def get_country_code_by_phone_prefix(phone_prefix, language):
	for prefix in get_prefix_country_list(language):
		if phone_prefix in prefix['dial_code']:
			return prefix['code']

	return ""
