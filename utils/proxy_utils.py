import hashlib
import json
import logging

import requests
TIMEOUT = 30


PROXY_URL = 'http://***************:9999'
PROXY_URL_TLS_1_2 = 'http://************'
PROXY_URL_TLS_1_2_USA = 'http://************'

#CLOUDRUN USA: IP de salida: ***********
PROXY_URL_CLOUDRUN_USA = 'https://proxy-usa-399475283438.us-central1.run.app'

#CLOUDRUN EU: IP de salida: *************
PROXY_URL_CLOUDRUN_EUROPE = 'https://proxy-europe-399475283438.europe-west1.run.app'

# Datatrans data inline
PCI_TOKEN = "PCI_TOKEN"


DATATRANS_SANDBOX_MERCH_ID = '1100006518'
DATATATRANS_SANDBOX_SIGN = '180524170410126559'
DATATRANS_SANDBOX_PROXY_PULL = "https://sandbox.pci-proxy.com/v1/pull"

DATATATRANS_PRODUCTION_SIGN = '180629113427710606'
DATATRANS_PRODUCTION_MERCH_ID = '3000011516'
DATATRANS_PRODUCTION_PROXY_PULL = "https://api.pci-proxy.com/v1/pull"



#DATATRANS_INLINE_MERCH_ID = DATATRANS_SANDBOX_MERCH_ID
#DATATATRANS_SIGN = DATATATRANS_SANDBOX_SIGN
#DATATRANS_PROXY_PULL = DATATRANS_SANDBOX_PROXY_PULL


DATATRANS_INLINE_MERCH_ID = DATATRANS_PRODUCTION_MERCH_ID
DATATATRANS_SIGN = DATATATRANS_PRODUCTION_SIGN
DATATRANS_PROXY_PULL = DATATRANS_PRODUCTION_PROXY_PULL



def post_using_generic_proxy(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url
	result = requests.post(PROXY_URL, data=body, headers=headers, timeout=timeout)
	return result

def delete_using_generic_proxy(target_url, body, headers={}, timeout=TIMEOUT):
	headers['targetUrl'] = target_url
	result = requests.delete(PROXY_URL, data=body, headers=headers, timeout=timeout)
	return result

def post_using_generic_proxy_tls_1_2(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url

	if body and isinstance(body, str):
		body = body.encode('utf8')

	result = requests.post(PROXY_URL_TLS_1_2, data=body, headers=headers, timeout=timeout)
	return result


def post_using_cloudrun_proxy_USA(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url

	logging.info("post_using_cloudrun_proxy_USA: %s", PROXY_URL_CLOUDRUN_USA)
	logging.info("post_using_cloudrun_proxy_USA headers: %s", headers)

	if body and isinstance(body, str):
		body = body.encode('utf8')
		logging.info("post_using_cloudrun_proxy_USA encoding UTF8")


	result = requests.post(PROXY_URL_CLOUDRUN_USA, data=body, headers=headers, timeout=timeout)
	return result


def post_using_cloudrun_proxy_EUROPE(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url

	logging.info("post_using_cloudrun_proxy_EUROPE: %s", PROXY_URL_CLOUDRUN_EUROPE)
	logging.info("post_using_cloudrun_proxy_EUROPE headers: %s", headers)

	if body and isinstance(body, str):
		body = body.encode('utf8')
		logging.info("post_using_cloudrun_proxy_EUROPE encoding UTF8")

	result = requests.post(PROXY_URL_CLOUDRUN_EUROPE, data=body, headers=headers, timeout=timeout)
	return result

def post_using_generic_proxy_tls_1_2_USA(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url

	if body and isinstance(body, str):
		body = body.encode('utf8')

	result = requests.post(PROXY_URL_TLS_1_2_USA, data=body, headers=headers, timeout=timeout)
	return result

def delete_using_generic_proxy_tls_1_2(target_url, body, headers={}, timeout=TIMEOUT):
	headers['targetUrl'] = target_url
	result = requests.delete(PROXY_URL_TLS_1_2, data=body, headers=headers, timeout=timeout)
	return result

def get_datatrans_sign(cc_alias):

	return DATATATRANS_SIGN

def post_using_datatrans_proxy(body, headers={}, timeout=TIMEOUT):

	# Let's create a http session
	session = requests.session()

	#response = session.post("https://www.howsmyssl.com/a/check", "", headers={})

	logging.info("using DATATRANS_PROXY_PULL: %s", DATATRANS_PROXY_PULL)

	response = session.post(DATATRANS_PROXY_PULL, body.encode("utf-8"), headers=headers)

	return response