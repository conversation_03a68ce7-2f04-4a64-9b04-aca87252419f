import datetime
import logging
import threading
import time
import json
import uuid

from cachetools import TTL<PERSON>ache
from google.cloud import tasks_v2
from google.cloud.tasks_v2 import Task
from google.protobuf import timestamp_pb2
from google.auth import compute_engine
from google.protobuf import duration_pb2  # Import duration_pb2

from paraty import Config
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache

cache = TTLCache(maxsize=100, ttl=60)
SEMAPHORE_LOCAL_TESTING = threading.Semaphore(25)

@timed_cache(hours=24)
def _get_queue_client():

	if Config.DEV:
		return tasks_v2.CloudTasksClient()
	else:
		credentials = compute_engine.Credentials()
		return tasks_v2.CloudTasksClient(credentials=credentials)



def list_tasks(queue_name, project=Config.PROJECT, location=Config.LOCATION):

	# For unit tests
	if Config.TESTING:
		return cache.values()

	client = _get_queue_client()

	target_location = location or get_queue_location(queue_name, project) or Config.LOCATION
	parent = client.queue_path(project, target_location, queue_name)

	# Iterate over all results
	return list(client.list_tasks(parent=parent))


def get_task_by_name(task_name, queue_name, project=Config.PROJECT, target_location=Config.LOCATION):
	try:
		list_of_tasks = list_tasks(queue_name, project, target_location)
		return [x for x in list_of_tasks if task_name in x.name]
	except Exception as e:
		logging.warning(f"Error retrieving task data: {task_name}, {queue_name}")
		return []


def delete_task(task_name):
	client = _get_queue_client()
	client.delete_task(name=task_name)


@timed_cache(hours=24)
def get_queue_location(queue_name, project):
	"""Trick to get automatically the location of a queue"""
	location_odds = ['us-central1', 'europe-west1', 'asia-east1', 'us-east1']
	client = _get_queue_client()
	for location in location_odds:
		try:
			queue = client.get_queue(name=client.queue_path(project, location, queue_name))
			return location
		except:
			pass

	raise Exception("Queue %s not found at project %s" % (queue_name, project))


def create_task_with_url_get_target(queue_name, url, name='', in_seconds=None, project=Config.PROJECT, location=Config.LOCATION):

	if name:
		name = name.replace(".", "_").replace(" ", "_").replace("(", "_").replace(")", "_")

	client = _get_queue_client()
	parent = client.queue_path(project, location, queue_name)

	# Use the client to build and send the task.
	try:
		task_object = Task()
		task_object.name = 'projects/%s/locations/%s/queues/%s/tasks/%s' % (project, location, queue_name, name)
		task_object.http_request = {
			'http_method': tasks_v2.HttpMethod.GET,
			'url': url
		}

		if in_seconds is not None:
			# Convert "seconds from now" into an rfc3339 datetime string.
			d = datetime.datetime.utcnow() + datetime.timedelta(seconds=in_seconds)

			# Create Timestamp protobuf.
			timestamp = timestamp_pb2.Timestamp()
			timestamp.FromDatetime(d)

			# Add the timestamp to the tasks.
			task_object.schedule_time = timestamp

		response = client.create_task(parent=parent, task=task_object)

		logging.info('Created task {}'.format(response.name))

	except Exception as e:
		logging.error("Exception while creating task: %s", e)
		raise(e)

	return response


def create_task_with_url_post_target(queue_name, url, body: dict, headers_in=None, name='', in_seconds=None, timeout_seconds=30 * 60):

	if name:
		name = name.replace(".", "_").replace(" ", "_").replace("(", "_").replace(")", "_")

	client = _get_queue_client()
	parent = client.queue_path(Config.PROJECT, Config.LOCATION, queue_name)

	# Use the client to build and send the task.
	try:
		# Convert dict to JSON string
		payload = json.dumps(body)
		# The API expects a payload of type bytes.
		converted_payload = payload.encode()

		# Specify http content-type to application/json
		headers = {"Content-type": "application/json"}
		if headers_in:
			headers.update(headers_in)

		if name == '':
			name = str(uuid.uuid4())

		# ToDo probarlo cuando este en produccion
		task_object = Task()
		task_object.name = 'projects/%s/locations/%s/queues/%s/tasks/%s' % (Config.PROJECT, Config.LOCATION, queue_name, name)
		task_object.http_request = {
			'http_method': tasks_v2.HttpMethod.POST,
			'url': url,
			'headers': headers,
			'body': converted_payload
		}

		# Set dispatch deadline to 30 minutes
		task_object.dispatch_deadline = duration_pb2.Duration(seconds=timeout_seconds)  # 30 minutes by default

		if in_seconds is not None:
			# Convert "seconds from now" into an rfc3339 datetime string.
			d = datetime.datetime.utcnow() + datetime.timedelta(seconds=in_seconds)

			# Create Timestamp protobuf.
			timestamp = timestamp_pb2.Timestamp()
			timestamp.FromDatetime(d)

			# Add the timestamp to the tasks.
			task_object.schedule_time = timestamp

		response = client.create_task(parent=parent, task=task_object)
		logging.info('Created task {}'.format(response.name))

	except Exception as e:
		logging.error("Exception while creating task: %s", e)
		raise(e)

	return response



def create_task(function_name, payload, queue_name='default', in_seconds=None, task_name=None, project_id=None, location=None, custom_endpoint=None):

	if task_name:
		task_name = task_name.replace(".", "_").replace(" ", "_").replace("(", "_").replace(")", "_")

	# Create a client.
	client = _get_queue_client()

	# Construct the fully qualified queue name.
	target_project = project_id or Config.PROJECT
	target_location = location or get_queue_location(queue_name, target_project) or Config.LOCATION

	parent = client.queue_path(target_project, target_location, queue_name)

	target_request_type = 'app_engine_http_request'
	if custom_endpoint and 'http' in custom_endpoint:
		target_request_type = 'http_request'

	# Construct the request body.
	task = {
			target_request_type: {  # Specify the type of request.
				'http_method': tasks_v2.HttpMethod.POST,
				'relative_uri': '/execute_task/%s' % function_name
			},
			'dispatch_deadline': duration_pb2.Duration(seconds=30 * 60)  # Set default dispatch deadline to 30 minutes
	}

	if custom_endpoint:
		del task[target_request_type]['relative_uri']
		task[target_request_type]['url'] = custom_endpoint

	if task_name:
		task['name'] = 'projects/%s/locations/%s/queues/%s/tasks/%s' % (Config.PROJECT, Config.LOCATION, queue_name, task_name)

	logging.info("Adding task %s to: %s %s %s", task_name, Config.PROJECT, Config.LOCATION, queue_name)

	if payload is not None:
		# The API expects a payload of type bytes.
		if not type(payload) is bytes:
			converted_payload = payload.encode()
		else:
			converted_payload = payload

		# Add the payload to the request.
		task[target_request_type]['body'] = converted_payload

	#For unit tests
	if Config.TESTING:

		my_task = Task()
		my_task.name = task_name or ''
		cache[task_name] = my_task
		# _launch_test_task(task)

		x = threading.Thread(target=_launch_test_task, args=(task,))
		x.start()
		Config.TEST_THREADS.append(x)

		return

	if in_seconds is not None:
		# Convert "seconds from now" into an rfc3339 datetime string.
		d = datetime.datetime.utcnow() + datetime.timedelta(seconds=in_seconds)

		# Create Timestamp protobuf.
		timestamp = timestamp_pb2.Timestamp()
		timestamp.FromDatetime(d)

		# Add the timestamp to the tasks.
		task['schedule_time'] = timestamp

	# Use the client to build and send the task.
	try:
		task_object = Task()
		task_object.name = task.get('name', '')
		setattr(task_object, target_request_type, task[target_request_type])

		task_object.schedule_time = task.get('schedule_time')

		response = client.create_task(parent=parent, task=task_object)

		logging.info('Created task {}'.format(response.name))

	except Exception as e:
		log_level = logging.warning if "409" in str(e) else logging.error
		log_level("Exception while creating task: %s", e)
		log_level(make_traceback())
		raise e

	return response


def _launch_test_task(task):

	# Add semaphore to limit max number of threads
	global SEMAPHORE_LOCAL_TESTING
	SEMAPHORE_LOCAL_TESTING.acquire()
	logging.info("Semaphore acquired")

	#Note that we have to delay a bit to give time to the audit to be written
	time.sleep(3)

	if 'http_request' in task:
		import requests
		target_endpoint = task['http_request']['url']
		target_body = task['http_request']['body']
		result = requests.post(target_endpoint, data=target_body, timeout=600)
	else:
		result = Config.TESTING.post(task['app_engine_http_request']['relative_uri'], data=task['app_engine_http_request']['body'])

	logging.info(result.status_code)

	logging.info("Semaphore released")
	SEMAPHORE_LOCAL_TESTING.release()
