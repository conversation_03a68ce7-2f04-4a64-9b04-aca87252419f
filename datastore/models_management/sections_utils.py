import copy

from paraty_commons_3.common_data.common_data_provider import get_all_websections
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.cache_entry_refresher import refresh_entity_timestamps
from paraty_commons_3.language_utils import SPANISH, LANGUAGE_TITLES

WEBSECTION_MODEL_NAME = 'WebSection'
WPP_MODEL_NAME = 'WebPageProperty'
DEFAULT_SECTION_NAME = 'New section (paraty_common_3)'
WEB_SECTION_DEFAULT_MODEL = {
    'enabled': False,
    'adminOnly': False,
    'name': DEFAULT_SECTION_NAME,
    'subtitle': '',
    'description': '',
    'sectionType': 'Normal'
}


def create_section(hotel_code, add_to_all_languages=False, **kwargs):
    section_name = kwargs.get('name')
    if not section_name:
        raise Exception('Missing section name to create section')

    _create_web_section_entity(hotel_code, **kwargs)

    section_info = datastore_communicator.get_using_entity_and_params(WEBSECTION_MODEL_NAME, hotel_code=hotel_code, search_params=[('name', '=', section_name)])
    entity_key = id_to_entity_key(hotel_code, section_info[0].key)
    _create_web_page_properties_entities(add_to_all_languages, hotel_code, entity_key, **kwargs)

    refresh_entity_timestamps(WEBSECTION_MODEL_NAME, hotel_code)
    refresh_entity_timestamps(WPP_MODEL_NAME, hotel_code)


def _get_section_from_name(hotel_code, section_name):
    all_sections = copy.deepcopy(get_all_websections(hotel_code))
    return list(filter(lambda x: x.get('name') == section_name, all_sections))


def _get_wpp(hotel_code, entity_key, language, main_key):
    return datastore_communicator.get_using_entity_and_params(WPP_MODEL_NAME,
                                                              search_params=[
                                                                  ('entityKey', '=', entity_key),
                                                                  ('languageKey', '=', language),
                                                                  ('mainKey', '=', main_key)
                                                              ],
                                                              hotel_code=hotel_code)


def _create_web_section_entity(hotel_code, **kwargs):
    base_section_info = {}
    for property_name, default_value in WEB_SECTION_DEFAULT_MODEL.items():
        base_section_info[property_name] = kwargs.get(property_name) or default_value

    if kwargs.get('sectionGroup'):
        base_section_info['sectionGroup'] = kwargs.get('sectionGroup')

    exists_section = _get_section_from_name(hotel_code, base_section_info['name'])
    target_key = None
    if exists_section:
        target_key = exists_section[0].id

    return save_to_datastore(WEBSECTION_MODEL_NAME, target_key, base_section_info, hotel_code)


def _create_web_page_properties_entities(add_to_all_languages, hotel_code, section_key, **kwargs):
    available_languages = datastore_communicator.get_using_entity_and_params('ConfigurationProperty',
                                                                             search_params=[('mainKey', '=',
                                                                                             'Hotel Manager Languages')],
                                                                             hotel_code=hotel_code)

    if available_languages:
        available_languages = available_languages[0]['value'].split("-")
    else:
        available_languages = [SPANISH]

    # WPP Name, WPP Title, WPP Type, WebSection attribute
    properties = [('WebSectionName', 'Nombre', 'STRING', 'name'),
                  ('WebSectionDescription', 'Descripcion', 'HTML', 'description'),
                  ('WebSectionSubtitle', 'Subtitulo', 'STRING', 'subtitle')]

    concurrent_params = []
    for language_element in available_languages:
        for property_info in properties:
            wpp_model = {
                'entityKey': section_key,
                'languageKey': language_element,
                'mainKey': property_info[0],
                'title': property_info[1] + ' (%s)' % LANGUAGE_TITLES.get(language_element),
                'type': property_info[2],
                'value': kwargs.get(property_info[3])
            }

            if not add_to_all_languages and language_element != SPANISH:
                wpp_model['value'] = ''

            concurrent_params.append([hotel_code, language_element, property_info, section_key, wpp_model])

    execute_in_parallel(_save_wpp_property, concurrent_params)


def _save_wpp_property(hotel_code, language_element, property_info, section_key, wpp_model):
    exists_wpp = _get_wpp(hotel_code, section_key, language_element, property_info[0])
    target_id = None
    if exists_wpp:
        target_id = exists_wpp[0].id
    save_to_datastore(WPP_MODEL_NAME, target_id, wpp_model, hotel_code)