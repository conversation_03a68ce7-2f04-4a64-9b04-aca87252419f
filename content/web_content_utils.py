# -*- coding: utf-8 -*-
#FMATHEIS, COPIED (and adapted to python 3) FROM HOTEL-WEBS  (paraty/util/web/webContentUtils.py)

import logging
import re
import html.entities
import unicodedata

from paraty_commons_3.language_utils import CHINESE_TRADITIONAL


def get_text_version(html_version):
	if not html_version:
		return ''

	result = re.sub('<.*?>', '', html_version.replace('<br>', '\n'))

	result = result.replace("&lt;div&gt;", "")
	result = result.replace("&lt;/div&gt;", "")

	result = unescape(result)

	result = result.replace("\n", "")

	return result


def convertEntityWebPropertyListToMap(webPageProperties):
	"""
	Returns a map with 'entityKey' + 'mainKey' -> property 
	"""
	webPagePropertiesMap = {}
	for webProperty in webPageProperties:

		if not webProperty.entityKey or not webProperty.mainKey:
			continue

		if webProperty.value:
			fixedProperty = webProperty.value.replace(u'&lt;', u'<')
			fixedProperty = fixedProperty.replace(u'&lt;', u'<')
			fixedProperty = fixedProperty.replace(u'&gt;', u'>')
			fixedProperty = fixedProperty.replace(u'&amp;', u'&')
			fixedProperty = fixedProperty.replace(u'&quot;',u'"')
			fixedProperty = fixedProperty.replace(u'div&gt;',u'div>')
			fixedProperty = fixedProperty.replace(u'&lt;br&gt;',u'<br/>')
			webPagePropertiesMap[webProperty.entityKey + webProperty.mainKey] = fixedProperty
		else:
			# logging.warning("WebPageProperty: (" + webProperty.mainKey + ", " + webProperty.title + ") is none")
			webPagePropertiesMap[webProperty.entityKey + webProperty.mainKey] = ""

	return webPagePropertiesMap


def unescapeText(text):
	"""
	DEPRECATED
	use unescape
	"""
	if text:
		result = text.replace(u'&lt;', u'<')
		result = result.replace(u'&gt;',u'>')
		result = result.replace(u'&quot;',u'"')
		result = result.replace(u'&amp;', u'&')
		return result
	else:
		return None


def unescapeHtmlLetters(name):
	"""
	DEPRECATED
	use unescape
	"""
	if name:
		result = name.replace('&amp;', '&')
		result = result.replace('&aacute;', 'a')
		result = result.replace('&Aacute;', 'A')
		result = result.replace('&eacute;', 'e')
		result = result.replace('&Eacute;', 'E')
		result = result.replace('&iacute;', 'i')
		result = result.replace('&Iacute;', 'I')
		result = result.replace('&oacute;', 'o')
		result = result.replace('&Oacute;', 'O')
		result = result.replace('&uacute;', 'u')
		result = result.replace('&Uacute;', 'U')
		result = result.replace('&ntilde;', 'n')
		result = result.replace('&Ntilde;', 'N')
		result = result.replace('&euml;', 'e')
		result = result.replace('&nbsp;', ' ')
		result = result.replace('&ndash;', '').replace('&ldquo;', '').replace('&rdquo;', '')

		return result
	else:
		return None


##
# Removes HTML or XML character references and entities from a text string.
#
# @param text The HTML (or XML) source text.
# @return The plain text, as a Unicode string, if necessary.
#
# http://stackoverflow.com/questions/57708/convert-xml-html-entities-into-unicode-string-in-python
# Takek from http://effbot.org/zone/re-sub.htm#unescape-html
# This function works wonderfully. Long live Fredrik

def unescape(text):
	def fixup(m):
		text = m.group(0)
		if text[:2] == "&#":
			# character reference
			try:
				if text[:3] == "&#x":
					return chr(int(text[3:-1], 16))
				else:
					return chr(int(text[2:-1]))
			except ValueError:
				pass
		else:
			# named entity
			try:
				text = chr(html.entities.name2codepoint[text[1:-1]])
			except KeyError:
				pass
		return text # leave as is

	if not text:
		return ""

	return re.sub("&#?\w+;", fixup, text)

def escape_unicode(text):
	t = ""
	for i in text:
		if ord(i) in html.entities.codepoint2name:
			name = html.entities.codepoint2name.get(ord(i))
			t += "&" + name + ";"
		else:
			t += i
	return t


def buildFriendlyUrl(name,  extension=".html"):

	if not name:
		return ''

	replace_map = {
		'"': "",
		' ': '-',
		"'": '',
		",": "",
		"%": "",
		"*": "",
		"": "",
		")": "",
		"!": "",
		"&": "",
		":": "",
		"?": "",
		"/": ""
	}

	result = unescape(name)
	for key, value in replace_map.items():
		result = result.replace(key, value)

	try:
		symbols = (u"абвгдеёзийклмнопрстуфхъыьэАБВГДЕЁЗИЙКЛМНОПРСТУФХЪЫЬЭ",
						u"abvgdeezijklmnoprstufh-y-eABVGDEEZIJKLMNOPRSTUFH'Y'E")

		tr = {ord(a): ord(b) for a, b in zip(*symbols)}
		result = result.translate(tr)
		result = unicodedata.normalize('NFKD', result).encode('ASCII', 'ignore').lower()

	except Exception as e:
		logging.warning("Exception normalizing %s: %s" % (result,e))

	result = result.decode('utf-8')
	result += extension
	return result


