from paraty_commons_3.common_data.data_management.brute_data.web_configs import get_all_web_configuration_properties
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache

@managers_cache(hotel_code_provider=lambda f,a,k: a[1], entities='WebConfiguration', only_thread_local=True)
def get_web_configuration(web_configuration_name: str, hotel_code: str) -> dict:
    all_properties = get_all_web_configuration_properties(hotel_code)
    if all_properties:
        property_info = all_properties.get(web_configuration_name, {})
        return property_info

    return {}