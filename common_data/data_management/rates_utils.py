from paraty_commons_3.common_data.data_management.brute_data.rates import get_all_rates_raw
from paraty_commons_3.common_data.data_management.web_page_property_utils import get_all_properties_for_entity_key
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key


def get_all_rates(hotel_code: str, language: str|None = None, removed: bool = True) -> list[dict]:
    all_rates_list = get_all_rates_raw(hotel_code)
    all_rates_formatted = []

    for rate_element in all_rates_list:
        dict_rate = dict(rate_element)
        dict_rate['key'] = id_to_entity_key(hotel_code, rate_element.key)
        dict_rate['id'] = rate_element.key.id

        if not removed and rate_element['removed']:
            continue

        if language:
            entity_key = id_to_entity_key(hotel_code, rate_element.key)
            rate_properties = get_all_properties_for_entity_key(language, entity_key, hotel_code)

            dict_rate['rateName'] = unescape(rate_properties.get('rateName') or '')
            dict_rate['rateDescription'] = unescape(rate_properties.get('rateDescription') or '')

        all_rates_formatted.append(dict_rate)

    return all_rates_formatted
