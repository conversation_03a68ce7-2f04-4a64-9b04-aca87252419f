from paraty_commons_3.common_data.data_management.brute_data.pictures import get_all_pictures_grouped_by_main_key
from paraty_commons_3.common_data.data_management.section_utils import retrieve_key_from_section_name
from paraty_commons_3.common_data.data_management.web_page_property_utils import get_all_properties_for_entity_key
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key


def get_pictures_from_section_name(section_name: str, language: str, hotel_code: str) -> list[dict]:
    target_section_key = retrieve_key_from_section_name(section_name, hotel_code)
    return get_pictures_for_key(target_section_key, language, hotel_code)


def get_pictures_for_key(main_key: str, language: str, hotel_code: str) -> list[dict]:
    all_pictures = get_all_pictures_grouped_by_main_key(hotel_code)
    match_pictures = all_pictures.get(main_key, [])

    pictures_formatted = []
    for picture in match_pictures:
        picture_key = id_to_entity_key(hotel_code, picture.key)
        picture_properties = get_all_properties_for_entity_key(language, picture_key, hotel_code)

        if not picture.get('enabled', False):
            continue

        picture_url = picture.get('servingUrl') and picture['servingUrl'].replace('http:', 'https:')

        picture_dict = {
            'servingUrl': picture_url,
            'title': unescape(picture_properties.get('pictureTitle') or ''),
            'spanish_title': unescape(picture.get('name') or ''),
            'description': unescape(picture_properties.get('pictureDescription') or ''),
            'enabled': picture.get('enabled', False),
            'priority': picture.get('priorityInWeb') or '',
            'key': picture_key,
            'altText': unescape(picture_properties.get('pictureAlt') or '')
        }

        pictures_formatted.append(picture_dict)

    return pictures_formatted