from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebConfiguration')
def get_all_web_configuration_properties(hotel_code: str) -> dict:
    propertiesMap = {}

    all_configuration_properties = get_using_entity_and_params("WebConfiguration", search_params=[], hotel_code=hotel_code)
    for currentProperty in all_configuration_properties:
        propertiesMap[currentProperty.name] = dict([
            x.split(" @@ ") for x in currentProperty.configurations
        ])

    return propertiesMap
