import copy
import hashlib

from google.cloud.datastore import Entity

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


def _cache_key_builder(f, a, k):
    filters_joined = ''
    copied_filters = copy.deepcopy(a[1])
    sorter_filters = sorted(copied_filters, key=lambda x: x[0])

    for filter_element in sorter_filters:
        filters_joined += f'{filter_element[0]}{filter_element[1]}{filter_element[2]}'

    filters_joined = hashlib.md5(filters_joined.encode()).hexdigest()

    return f'get_reservations_with_filter_{a[0]}_{filters_joined}'


@managers_cache(
    hotel_code_provider=lambda f, a, k: a[0],
    key_generator=_cache_key_builder,
    entities='Reservation',
    ttl_seconds=60 * 60 * 24 * 3,  # 3 days
    background_refresh=False
)
def get_reservations_with_filter(hotel_code: str, filters: list[tuple]) -> list[Entity]:
    reservations = get_using_entity_and_params('Reservation', search_params=filters, hotel_code=hotel_code)
    return reservations


def get_reservations_with_filter_no_cache(hotel_code: str, filters: list[tuple]) -> list[Entity]:
    reservations = get_using_entity_and_params('Reservation', search_params=filters, hotel_code=hotel_code)
    return reservations

