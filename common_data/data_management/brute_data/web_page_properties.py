import logging, requests

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[1], entities='WebPageProperty', requires_compression=True)
def get_all_raw_web_page_properties(language: str, hotel_code: str) -> dict[str, list[dict]]:
	try:
		target_url = f"https://wpp-utils-flask-399475283438.europe-west1.run.app/get-wpp-by-language?hotel_code={hotel_code}&language={language}"
		logging.info(f"Getting all web page properties from cloud function: {target_url}")
		results = requests.get(target_url, timeout=5)
		logging.info("Finished call to cloud function of all web page properties")

		if results.status_code == 200:
			result = results.json()
			retrocompatibility_format = {}
			for i, property_element in enumerate(result['result']):
				del property_element['namespace']
				del property_element['project']
				del property_element['id']
				retrocompatibility_format.setdefault(property_element['entityKey'], []).append(property_element)

			return retrocompatibility_format

		raise Exception(f"Bad status_code from response. [{results.status_code}]")
	except Exception as e:
		logging.warning(f"Failed to get web page properties for language from cloudfunction {language}. Please, check hotel_code and language")
		logging.warning(e)

	all_wpp_properties = get_using_entity_and_params('WebPageProperty', search_params=[('languageKey', '=', language)], hotel_code=hotel_code)
	all_web_page_properties = {}
	for current_property in all_wpp_properties:
		if current_property['value'] and current_property['entityKey']:
			if current_property['entityKey'] not in all_web_page_properties:
				all_web_page_properties[current_property['entityKey']] = []

			all_web_page_properties[current_property['entityKey']].append(dict(current_property))

	return all_web_page_properties
