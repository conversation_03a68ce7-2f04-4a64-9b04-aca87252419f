from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='IntegrationConfiguration')
def get_all_integration_configuration(hotel_code: str):
    all_integrations_configurations = get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel_code)
    return all_integrations_configurations
