from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='ConfigurationProperty')
def get_all_configuration_properties(hotel_code: str) -> dict:
    properties_map = {}
    all_configuration_properties = get_using_entity_and_params("ConfigurationProperty", search_params=[], hotel_code=hotel_code)

    for currentProperty in all_configuration_properties:
        current_key = currentProperty.key
        target_value = currentProperty['value']
        target_name = currentProperty['mainKey']

        properties_map[target_name] = {"key": current_key, "value": target_value}

    return properties_map
