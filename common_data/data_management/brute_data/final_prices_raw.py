from google.cloud.datastore import Entity

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='FinalPriceDay', background_refresh=True)
def get_final_price_days_raw(hotel_code: str, **kwargs) -> list[Entity]:
    filters = []

    if 'room_key' in kwargs:
        filters.append(('roomKey', '=', kwargs['room_key']))

    if 'date' in kwargs:
        filters.append(('date', '=', kwargs['date']))

    if not filters:
        raise Exception("You must provide at least one filter, this method is too expensive to be called without filters")

    return get_using_entity_and_params("FinalPriceDay", search_params=filters, hotel_code=hotel_code)
