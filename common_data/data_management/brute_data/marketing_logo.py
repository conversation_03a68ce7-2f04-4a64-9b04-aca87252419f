from google.cloud.datastore import Entity

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='MarketingLogo')
def get_all_raw_marketing_logos(hotel_code: str) -> list[Entity]:
    return get_using_entity_and_params("MarketingLogo", search_params=[], hotel_code=hotel_code)
