from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebSection')
def get_all_sections_raw(hotel_code: str):
    all_sections = get_using_entity_and_params('WebSection', search_params=[], hotel_code=hotel_code)
    return all_sections
