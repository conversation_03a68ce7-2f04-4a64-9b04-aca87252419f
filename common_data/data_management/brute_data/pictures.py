from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='Picture')
def get_all_pictures_grouped_by_main_key(hotel_code: str):
    all_pictures = get_using_entity_and_params('Picture', search_params=[], hotel_code=hotel_code)
    grouped_pictures = {}
    for picture in all_pictures:
        main_key = picture['mainKey']
        if main_key not in grouped_pictures:
            grouped_pictures[main_key] = []
        grouped_pictures[main_key].append(picture)

    return grouped_pictures
