from paraty_commons_3.common_data.data_management.brute_data.boards import get_all_boards_raw
from paraty_commons_3.common_data.data_management.web_page_property_utils import get_all_properties_for_entity_key
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key


def get_all_boards(hotel_code: str, language: str, removed: bool = True) -> list[dict]:
    all_boards = get_all_boards_raw(hotel_code)
    results = []

    for board_element in all_boards:
        if not removed and board_element['removed']:
            continue

        entity_key = id_to_entity_key(hotel_code, board_element.key)
        board_properties = get_all_properties_for_entity_key(language, entity_key, hotel_code)

        results.append({
            'key': entity_key,
            'id': board_element.key.id,
            'name': unescape(board_properties.get('regimenName') or ''),
            'description': unescape(board_properties.get('regimenDescription') or '')
        })

    return results