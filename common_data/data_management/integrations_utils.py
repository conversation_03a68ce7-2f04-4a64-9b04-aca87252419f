from paraty_commons_3.common_data.data_management.brute_data.integration_configuration import \
	get_all_integration_configuration
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id


def get_integration_configuration(integration_name: str, hotel_code: str):
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if not integration_config:
		return {}

	integration_dict = {
		'configurations': {},
		'roomMap': {},
		'rateMap': {},
		'boardMap': {},
		'extraMap': {},
		'packageMap': {}
	}

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		integration_dict['configurations'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('configurations', [])}
		integration_dict['roomMap'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('roomMap', [])}
		integration_dict['rateMap'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('rateMap', [])}
		integration_dict['boardMap'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('boardMap', [])}
		integration_dict['extraMap'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('extraMap', [])}
		integration_dict['packageMap'] = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('packageMap', [])}

		return integration_dict

	return integration_dict


def get_integration_configuration_properties(integration_name: str, hotel_code: str) -> dict:
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integrationConfig = [x for x in all_integration_configs if x['name'] == integration_name]

	if integrationConfig and len(integrationConfig) > 0:
		result = integrationConfig[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('configurations', [])}
		return properties

	return {}



def get_integration_room_map_properties(integration_name: str, hotel_code: str) -> dict[str, str]:
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('roomMap', [])}
		return properties

	return {}


def get_integration_rate_map_properties(integration_name: str, hotel_code: str, get_ids : bool = False) -> dict[str, str]:
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('rateMap', [])}

		if get_ids:
			properties = {str(alphanumeric_to_id(k)): v for k, v in properties.items()}

		return properties

	return {}


def get_integration_board_map_properties(integration_name: str, hotel_code: str) -> dict[str, str]:
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		if 'boardMap' not in result:
			return {}

		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result['boardMap']}
		return properties

	return {}

def get_integration_package_map_properties(integration_name: str, hotel_code: str) -> dict[str, str]:
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		if 'packageMap' not in result:
			return {}

		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result['packageMap']}
		return properties

	return {}


def get_integration_extra_map_properties(integration_name: str, hotel_code: str):
	all_integration_configs = get_all_integration_configuration(hotel_code)
	integration_config = [x for x in all_integration_configs if x['name'] == integration_name]

	if integration_config and len(integration_config) > 0:
		result = integration_config[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('extraMap')}
		return properties

	return {}
