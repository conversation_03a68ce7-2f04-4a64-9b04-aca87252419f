from typing import Optional

from paraty_commons_3.common_data.data_management.brute_data.reservations import get_reservations_with_filter, get_reservations_with_filter_no_cache
from paraty_commons_3.common_data.data_management.interfaces.reservation import ReservationDict


def get_reservation_by_identifier(identifier: str, hotel_code: str) -> ReservationDict|None:
	target_reservation = get_reservations_with_filter(hotel_code, [('identifier', '=', identifier)])
	if target_reservation:
		return target_reservation[0]

	return None


def is_reservation_cancelled(reservation: ReservationDict) -> bool:
	if type(reservation['cancelled']) is bool:
		return reservation['cancelled']
	else:
		return reservation['cancelled'] == 'true'


def get_reservations_of_hotel(hotel: dict, from_datetime: Optional[str], to_datetime: Optional[str], **kwargs) -> list[ReservationDict]:
	"""
	Retrieve reservations for a specific hotel within a given date range, with optional filters.

	Args:
		hotel (dict): The hotel information containing at least the 'applicationId'.
		from_datetime (str): The start date and time for the reservation search.
		to_datetime (str): The end date and time for the reservation search.
		**kwargs: Additional optional filters:
			- reservation_id (str): Filter by a specific reservation identifier.
			- include_end_date (bool): Include the end date in the search range.
			- include_cancelled_reservations (bool): Include cancelled reservations.
			- include_modified_reservations (bool): Include modified reservations.
			- discard_test_reservations (bool): Exclude test reservations.

	Returns:
		list: A list of reservations matching the specified criteria.
	"""
	filters_list = []
	if kwargs.get('reservation_id'):
		filters_list.append(('identifier', '=', kwargs.get('reservation_id')))
		return get_reservations_with_filter(hotel['applicationId'], filters_list)

	if kwargs.get('include_end_date'):
		to_datetime = to_datetime + " 23:59:59"

	reservations = get_reservations_with_filter(hotel['applicationId'], [('timestamp', '>=', from_datetime), ('timestamp', '<=', to_datetime)])

	if kwargs.get('include_cancelled_reservations'):
		cancelled_reservations = get_reservations_with_filter(hotel['applicationId'], [('cancellationTimestamp', '>=', from_datetime), ('cancellationTimestamp', '<=', to_datetime)])
		reservations.extend(cancelled_reservations)

	if kwargs.get('include_modified_reservations'):
		modified_reservations = get_reservations_with_filter(hotel['applicationId'], [('modificationTimestamp', '>=', from_datetime), ('modificationTimestamp', '<=', to_datetime)])
		reservations.extend(modified_reservations)

	if kwargs.get('discard_test_reservations'):
		reservations = [x for x in reservations if not x.get('comments') or '@@@TEST@@@' not in x.get('comments')]

	# Filter duplicated bookings
	reservations = list({reservation['identifier']: reservation for reservation in reservations}.values())

	return reservations


def get_reservations_of_hotel_no_cache(hotel: dict, from_datetime: Optional[str], to_datetime: Optional[str], **kwargs) -> list[ReservationDict]:
	filters_list = []
	if kwargs.get('reservation_id'):
		filters_list.append(('identifier', '=', kwargs.get('reservation_id')))
		return get_reservations_with_filter_no_cache(hotel['applicationId'], filters_list)

	if kwargs.get('include_end_date'):
		to_datetime = to_datetime + " 23:59:59"

	reservations = get_reservations_with_filter_no_cache(hotel['applicationId'], [('timestamp', '>=', from_datetime), ('timestamp', '<=', to_datetime)])

	if kwargs.get('include_cancelled_reservations'):
		cancelled_reservations = get_reservations_with_filter_no_cache(hotel['applicationId'], [('cancellationTimestamp', '>=', from_datetime), ('cancellationTimestamp', '<=', to_datetime)])
		reservations.extend(cancelled_reservations)

	if kwargs.get('include_modified_reservations'):
		modified_reservations = get_reservations_with_filter_no_cache(hotel['applicationId'], [('modificationTimestamp', '>=', from_datetime), ('modificationTimestamp', '<=', to_datetime)])
		reservations.extend(modified_reservations)

	if kwargs.get('discard_test_reservations'):
		reservations = [x for x in reservations if not x.get('comments') or '@@@TEST@@@' not in x.get('comments')]

	# Filter duplicated bookings
	reservations = list({reservation['identifier']: reservation for reservation in reservations}.values())

	return reservations
