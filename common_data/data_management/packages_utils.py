from paraty_commons_3.common_data.data_management.brute_data.packages_raw import get_all_packages_raw
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key


def get_all_packages(hotel_code: str) -> dict[str, dict]:
    """
    Return: dict[package_key, package_dict]
    """
    filtered_packages = {}
    for package_element in get_all_packages_raw(hotel_code):
        if package_element['mode'] == 1:
            package_dict = dict(package_element)
            package_dict['key'] = id_to_entity_key(hotel_code, package_element.key)
            package_dict['id'] = package_element.key.id
            filtered_packages[package_dict['key']] = package_dict

    return filtered_packages
