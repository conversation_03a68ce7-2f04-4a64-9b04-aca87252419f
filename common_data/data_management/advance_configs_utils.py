from paraty_commons_3.common_data.data_management.brute_data.advance_configs import get_all_configuration_properties
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[1], entities='ConfigurationProperty', only_thread_local=True)
def get_config_property_value(main_key: str, hotel_code: str) -> str:
    all_configs = get_all_configuration_properties(hotel_code)
    return all_configs.get(main_key, {}).get("value", "")
