from google.cloud.datastore import Entity

from paraty_commons_3.common_data.data_management.brute_data.marketing_logo import get_all_raw_marketing_logos


def get_all_marketing_logos(hotel_code: str, only_enabled: bool = False) -> list[Entity]:
    all_marketing_logos = get_all_raw_marketing_logos(hotel_code)
    if only_enabled:
        return [logo for logo in all_marketing_logos if logo['enabled']]

    return all_marketing_logos
