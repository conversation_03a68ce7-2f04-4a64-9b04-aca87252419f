import copy
from typing import Optional

from paraty_commons_3.common_data.data_management.brute_data.rooms import get_all_rooms_raw
from paraty_commons_3.common_data.data_management.pictures_utils import get_pictures_for_key
from paraty_commons_3.common_data.data_management.web_page_property_utils import get_all_properties_for_entity_key
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key


def get_all_rooms(hotel_code: str, language: Optional[str] = None, removed: bool = True) -> list[dict]:
    all_rooms = copy.deepcopy(get_all_rooms_raw(hotel_code))
    rooms_dict_list = []
    
    for room_element in all_rooms:
        room_dict = dict(room_element)
        
        if not removed and room_element['removed']:
            continue

        room_dict['key'] = id_to_entity_key(hotel_code, room_element.key)
        room_dict['id'] = room_element.key.id
        
        if language:
            # If we need rooms translated, use the language to get the translated room name and description
            entity_key = id_to_entity_key(hotel_code, room_element.key)
            wpp_room_element = get_all_properties_for_entity_key(language, entity_key, hotel_code)
            avoided_keys = {'roomName', 'roomDescription'}
            if room_name := wpp_room_element.get('roomName'):
                room_dict['name'] = unescape(room_name)

            if room_description := wpp_room_element.get('roomDescription'):
                room_dict['description'] = unescape(room_description)

            room_dict.update({k: v for k, v in wpp_room_element.items() if k not in avoided_keys})

        room_dict['pictures_with_properties'] = get_pictures_for_key(room_dict['key'], language, hotel_code)
        rooms_dict_list.append(room_dict)

    return rooms_dict_list
