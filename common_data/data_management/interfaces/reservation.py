from typing import TypedDict, List, Optional

class ReservationDict(TypedDict, total=False):
    # Times and identifiers
    timestamp: str
    identifier: str
    startDate: str
    endDate: str
    promocode: str
    numRooms: int

    # Room details
    adults1: int
    kids1: int
    babies1: int

    adults2: int
    kids2: int
    babies2: int

    adults3: int
    kids3: int
    babies3: int

    # User comments and room selections
    comments: str
    roomType1: str
    roomType2: str
    roomType3: str
    regimen: str
    rate: str
    priceIncrease: str
    promotions: str

    # Price details
    price: str
    priceSupplements: str
    amendedPrice: str

    # User details
    prefix: str
    name: str
    lastName: str
    address: str
    country: str
    geolocation: str
    email: str
    telephone: str
    creditCard: str
    invalidCreditCard: str

    # Reservation status and services
    cancelled: bool
    additionalServices: str
    additionalServices2: str
    incidents: str

    # Language and other metadata
    language: str
    source: str
    priceByDay: List[str]
    discountByDay: List[str]
    media: str
    birthday: str

    # Timestamps and agent details
    cancellationTimestamp: str
    modificationTimestamp: str
    agent: str
    extraInfo: str
