from paraty_commons_3.common_data.data_management.brute_data.sections import get_all_sections_raw
from paraty_commons_3.common_data.data_management.web_page_property_utils import get_all_properties_for_entity_key
from paraty_commons_3.common_data.links_utils import build_friendly_url
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebSection', only_thread_local=True)
def _generate_map_link_and_section(hotel_code):
    all_sections = get_all_sections_raw(hotel_code)
    all_enabled_sections = [section for section in all_sections if section['enabled']]

    mapped_sections = {}
    for section in all_enabled_sections:
        section_url = build_friendly_url(section['name'])
        section_key = id_to_entity_key(hotel_code, section.key)
        mapped_sections[section_url] = section_key

    return mapped_sections


def retrieve_key_from_section_name(section_name: str, hotel_code: str) -> str:
    target_url = build_friendly_url(section_name)
    return _generate_map_link_and_section(hotel_code).get(target_url)


def get_section_from_spanish_name(section_name: str, language: str, hotel_code: str) -> dict:
    section_key = retrieve_key_from_section_name(section_name, hotel_code)

    all_sections = get_all_sections_raw(hotel_code)
    match_section = [section for section in all_sections if id_to_entity_key(hotel_code, section.key) == section_key]

    if not match_section or not match_section[0]['enabled']:
        return {}

    target_section = match_section[0]
    section_key = id_to_entity_key(hotel_code, target_section.key)
    section_properties = get_all_properties_for_entity_key(language, section_key, hotel_code)

    translated_section_name = unescape(section_properties.get('WebSectionName') or '')

    section_dict = {
        'key': id_to_entity_key(hotel_code, target_section.key),
        'sectionName': unescape(target_section['name']),
        'title': translated_section_name,
        'subtitle': unescape(section_properties.get('WebSectionSubtitle') or ''),
        'content': unescape(section_properties.get('WebSectionDescription') or ''),
        'friendlyUrl': build_friendly_url(target_section['name']),
        'friendlyUrlInternational': build_friendly_url(translated_section_name),
        'section_type': target_section['sectionType'],
        'private': target_section['adminOnly']
    }

    properties_to_avoid = {'WebSectionName', 'WebSectionSubtitle', 'WebSectionDescription'}

    properties_filtered = {key: value for key, value in section_properties.items() if key not in properties_to_avoid}
    section_dict.update(properties_filtered)

    return section_dict