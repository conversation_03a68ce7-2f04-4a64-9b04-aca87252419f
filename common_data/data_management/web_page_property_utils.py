from paraty_commons_3.common_data.data_management.brute_data.web_page_properties import get_all_raw_web_page_properties


def get_all_properties_for_entity_key(language: str, entity_key: str, hotel_code: str) -> dict[str, str]:
    all_web_properties = get_all_raw_web_page_properties(language, hotel_code)
    matched_properties = all_web_properties.get(entity_key, [])
    formatted_properties = {}
    for web_page_property_element in matched_properties:
        formatted_properties[web_page_property_element['mainKey']] = web_page_property_element['value']

    return formatted_properties
