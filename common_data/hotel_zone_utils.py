from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_all_hotel_metadata


def get_hotels_in_region(prefix):
    all_hotels = {x['applicationId']: True for x in get_all_valid_hotels()}
    all_metadata = get_all_hotel_metadata()
    result = [x['applicationId'] for x in all_metadata.values() if x.get('location_prefix') and x['location_prefix'] == prefix]

    valid_european_hotels = [x for x in result if x in all_hotels]

    hotels_for_test = [x for x in valid_european_hotels]

    return hotels_for_test

@timed_cache()
def get_eu_hotels() -> list[str]:
    return get_hotels_in_region('e~')

@timed_cache()
def get_usa_hotels() -> list[str]:
    return get_hotels_in_region('s~')
