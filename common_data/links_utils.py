import logging
import unicodedata

from paraty_commons_3.content.web_content_utils import unescape


def build_friendly_url(name, extension=".html"):
	if not name:
		return ''

	result = unescape(name)

	remove_rules = ['"', "'", ',', '%', '*', '(', ')', '!', ';', '&', ':', '?', '/']
	replacement_rules = {
		' ': '-'
	}

	result = result.lower()

	for char_to_delete in remove_rules:
		result = result.replace(char_to_delete, '')

	for old_value, new_value in list(replacement_rules.items()):
		result = result.replace(old_value, new_value)

	try:
		if isinstance(result, str):
			pass
			# result = str(result, 'utf-8')
		else:
			result = str(result)

		symbols = ("абвгдеёзийклмнопрстуфхъыьэАБВГДЕЁЗИЙКЛМНОПРСТУФХЪЫЬЭ",
				   "abvgdeezijklmnoprstufh-y-eABVGDEEZIJKLMNOPRSTUFH'Y'E")

		tr = {ord(a): ord(b) for a, b in zip(*symbols)}
		result = result.translate(tr)
		result = unicodedata.normalize('NFKD', result).encode('ASCII', 'ignore').lower()
		result = str(result, 'utf-8')

	except Exception as e:
		logging.warning("Exception normalizing %s: %s" % (result, e))

	result += extension
	return result
