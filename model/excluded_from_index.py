"""
fmatheis, This file needs to be synchroned with REST project, with those entities who have a db.Text()
"""


EXCLUDED_FROM_INDEX_PER_ENTITY = {
	'WebPageProperty': ('value',),
	'WebSection': ('description',),
	'Rate': ('description',),
	'RateCondition': ('description',),
	'RoomType': ('description', 'extraInfo'),
	'Regimen': ('description',),
	'Supplement': ('description',),
	'Picture': ('description',),
	'Agencies': ('password',),
	'FinalPriceDay': ('content',),
	'RoomTypeStatus': ('closedRegimen2', 'closedRate2', 'closedRateBoard', 'availabilityRateBoard',),
	'ConfigurationProperty': (),
	'BonoConfigs': (),
	'PrecheckinConfigs': ('fields_to_ask', 'ws_url'),
	'PrecheckinReservation': ('rooms',),
	'UserSession': ('content',),
	'HotelMetadata': (),
	'Wysiwyg': ('components_list', 'extra_info', 'insertable_components', 'slave_start_data'),
	'WysiwygHistory': ('info',),
	'WysiwygStyles': ('data',),
	'WebConfiguration': ('configurations',),
	'PaymentConfiguration': (),
	'HotelApplication': (),
	'PaymentsReservation': (),
	'PaymentsReservationExtraInfo': (),
	'RatePeriod': (),
	'IntegrationConfiguration': ('boardMap', 'rateMap', 'roomMap', 'promotionMap', 'extraMap', 'configurations'),
	'Reservation': ("extraInfo", "additionalServices2"),
	'ClubUser': ("extraInfo", "comments"),
	'Banner': ('description', 'content'),
	'PriceIncrease': ('description',),
	'Promotion': ('description', 'rate', 'roomType2', ),
	'PreBooking': ('calculated',),
	'News': ('description',),
	'PriceTimer': ('description',),
	'SatisfactionForm': ('answers',),
	'YieldModification': (),
	'PageToRefresh': (),
	'Currency': (),
	'GiftBono': ('extra_info', 'rates', 'rooms'),
	'MarketingLogo': (),
	'MultiRate': ('description', ),
	'PromotionDayStatus': (),
	'Redirection':(),
	'ReportConfiguration':(),
	'Restriction':(),
	'RestrictionDayStatus':(),
	'SupplementDayStatus': (),
	'CacheManager': (),
	'Promocode': (),
	'SourceChange': (),
	'AgenciesBooking': (),
	'AuditResponse': ('response',),
	'UrlTranslation': (),
	'UserClub': ('extraInfo',),
	'transactionsClub': (),
	'IntegrationUser': (),
	'promocodesClub': (),
	'MultiRateRoomsMonth': ('content',),
	'Payment': (),
	'Consent': (),
	'SearchHistoryAudit': (),
	'Kpi': (),
	'ReservationMetadata': ('extraInfo',),
	'LocalRoomMapping': (),
	'LocalRoomMapping2': ('matching_rooms',),
	'PmsResponses': ('pms_response',),
	'Emails': ('email_structure', 'customizations',),
	'PendingReservation': ('personal_details', 'reservation_data'),
	'paymentAudit':("response",),
	'ParatyUser': (),
	'UserInvite': (),
	'EndpointCallAuditEvent': (),
	'BeneficiaryEmailData': (),
	'ExternalIdentifiers': (),
	'SIBStransactions': (),
	'BookingRequestLog': (),
	'BookingHtml': ('compressed_content',),
	'Transfer': (),
	"SearchLogEntry": (),
	'Newsletter': (),
	'IpsWhitelist': (),
	'RoomsMappingBI': (),
	'PmsMetadata': (),
	'PendingRefunds': ('extraInfo',)
}


def get_excluded_from_index(entity_name):

	# Just to make sure we don't forget any entity
	if entity_name not in EXCLUDED_FROM_INDEX_PER_ENTITY:
		raise Exception("Missing configuration for entity: %s", entity_name)

	return EXCLUDED_FROM_INDEX_PER_ENTITY.get(entity_name, ())
