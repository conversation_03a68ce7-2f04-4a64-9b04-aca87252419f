import time
import unittest
from datetime import datetime

from paraty_commons_3.decorators.cache import timebased_cache
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache


@timed_cache(hours=24)
def very_cached_method():
    return datetime.now()

@timed_cache(hours=24)
def very_cached_method_2(test):
    return datetime.now()


class TestDecorators(unittest.TestCase):

    def test_force_max_t(self):

        value1 = very_cached_method()

        timebased_cache.set_thread_max_expiration_time(0)

        time.sleep(1)

        value2 = very_cached_method()

        assert value1 != value2

        timebased_cache.set_thread_max_expiration_time(10)

        time.sleep(1)

        value3 = very_cached_method()

        assert value3 == value2

        value4 = very_cached_method()

        assert value4 == value2

