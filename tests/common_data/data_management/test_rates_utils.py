import unittest
from unittest.mock import patch

from paraty_commons_3.common_data.data_management.rates_utils import get_all_rates
from paraty_commons_3.tests.patch_utils.cache_patch import patch_cache


class TestRatesUtils(unittest.TestCase):
    @patch_cache
    @patch('paraty_commons_3.common_data.data_management.brute_data.rates.get_using_entity_and_params')
    def test_get_all_rates(self, *args):
        get_all_rates('demo5')
        self.assertEqual(args[0].call_count, 1)
