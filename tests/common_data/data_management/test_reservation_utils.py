import unittest
import uuid
from unittest.mock import patch

from paraty_commons_3.common_data.data_management.reservation_utils import get_reservations_of_hotel
from paraty_commons_3.tests.patch_utils.cache_patch import patch_cache


class TestReservationUtils(unittest.TestCase):
    @patch_cache
    @patch('paraty_commons_3.common_data.data_management.brute_data.reservations.get_using_entity_and_params')
    def test_get_reservations_of_hotel(self, *args):
        get_reservations_of_hotel({'applicationId': 'example-test'}, '2024-10-01', '2024-12-01')
        self.assertEqual(args[0].call_count, 1)


    @patch('paraty_commons_3.common_data.data_management.brute_data.reservations.get_using_entity_and_params')
    def test_that_cache_works_correctly(self, *args):
        args[0].return_value = []
        identifier_simulation = uuid.uuid4().hex
        get_reservations_of_hotel({'applicationId': 'example-test'}, '2024-10-01', '2024-12-01', reservation_id=identifier_simulation)
        self.assertEqual(args[0].call_count, 1)

        get_reservations_of_hotel({'applicationId': 'example-test'}, '2024-10-01', '2024-12-01', reservation_id=identifier_simulation)
        self.assertEqual(args[0].call_count, 1)
