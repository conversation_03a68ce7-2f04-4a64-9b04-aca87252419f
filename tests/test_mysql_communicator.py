import unittest

from google.cloud.datastore import Entity

from datastore.datastore_communicator import build_key
from mysql.mysql_communicator import get_using_entity_and_params, get_entity_by_key, get_entity, save_to_datastore, delete_entity, save_multiple_entities, delete_entity_multi, \
	save_entity, save_entity_multi


class TestMysqlCommunicator(unittest.TestCase):

	TEST_WPP = {
		"entityKey": "a123",
		"languageKey": "SPANISH",
		"mainKey": "b123",
		"title": "c123",
		"type": "STRING",
		"value": "d123"
	}
	TEST_WPP_ID = 1234567890123

	TEST_WPP2 = {
		"entityKey": "2123",
		"languageKey": "SPANISH",
		"mainKey": "2123",
		"title": "2123",
		"type": "STRING",
		"value": "2123"
	}
	TEST_WPP2_ID = 2234567890123

	TEST_HOTEL = "best-tenerife"

	def test_get_using_entity_and_params(self):
		entities = get_using_entity_and_params("WebPageProperty", search_params=[], hotel_code=self.TEST_HOTEL)
		assert len(entities) > 0
		assert isinstance(entities[0], Entity)

	def test_get_entity_by_key(self):
		key = build_key("WebPageProperty", 4903652653793280, self.TEST_HOTEL)
		entity = get_entity_by_key(key, self.TEST_HOTEL)
		assert isinstance(entity, Entity)
		assert entity.id == 4903652653793280

	def test_get_entity(self):
		entity = get_entity("WebPageProperty", 4903652653793280, self.TEST_HOTEL)
		assert isinstance(entity, Entity)

	def test_save_to_datastore(self):
		id = save_to_datastore("WebPageProperty", self.TEST_WPP_ID, self.TEST_WPP, hotel_code=self.TEST_HOTEL)
		entity = get_entity("WebPageProperty", id, self.TEST_HOTEL)

		assert id == self.TEST_WPP_ID
		assert isinstance(entity, Entity)

		delete_entity("WebPageProperty", self.TEST_WPP_ID, hotel_code=self.TEST_HOTEL)

	def test_save_multiple_entities(self):
		ids = [self.TEST_WPP_ID, self.TEST_WPP2_ID]
		entities_properties = [self.TEST_WPP, self.TEST_WPP2]
		saved_ids = save_multiple_entities("WebPageProperty", ids, entities_properties, hotel_code=self.TEST_HOTEL)

		assert len(saved_ids) == len(ids)
		assert self.TEST_WPP_ID in saved_ids
		assert self.TEST_WPP2_ID in saved_ids
		key1 = build_key("WebPageProperty", self.TEST_WPP_ID, self.TEST_HOTEL)
		key2 = build_key("WebPageProperty", self.TEST_WPP2_ID, self.TEST_HOTEL)

		delete_entity_multi([key1, key2], hotel_code=self.TEST_HOTEL)

	def test_save_entity(self):
		properties = self.TEST_WPP
		id = self.TEST_WPP_ID
		key = build_key("WebPageProperty", id, self.TEST_HOTEL)
		datastore_entity = Entity(key=key)
		datastore_entity.update(properties)
		id_inserted = save_entity(datastore_entity, self.TEST_HOTEL)
		assert id == id_inserted

		delete_entity("WebPageProperty", self.TEST_WPP_ID, hotel_code=self.TEST_HOTEL)

	def test_save_entity_multi(self):
		properties1 = self.TEST_WPP
		id1 = self.TEST_WPP_ID
		key1 = build_key("WebPageProperty", id1, self.TEST_HOTEL)
		datastore_entity1 = Entity(key=key1)
		datastore_entity1.update(properties1)

		properties2 = self.TEST_WPP2
		id2 = self.TEST_WPP2_ID
		key2 = build_key("WebPageProperty", id2, self.TEST_HOTEL)
		datastore_entity2 = Entity(key=key2)
		datastore_entity2.update(properties2)

		entities = [datastore_entity1, datastore_entity2]

		ids_inserted = save_entity_multi(entities, self.TEST_HOTEL)
		assert len(ids_inserted) == 2
		assert self.TEST_WPP_ID in ids_inserted
		assert self.TEST_WPP2_ID in ids_inserted

		delete_entity_multi([key1, key2], hotel_code=self.TEST_HOTEL)

	def test_get_using_entity_and_params_2(self):
		properties = self.TEST_WPP
		id = self.TEST_WPP_ID
		key = build_key("WebPageProperty", id, self.TEST_HOTEL)
		datastore_entity = Entity(key=key)
		datastore_entity.update(properties)
		save_entity(datastore_entity, self.TEST_HOTEL)

		search_params = [("id", "=", self.TEST_WPP_ID)]
		entities = get_using_entity_and_params("WebPageProperty", search_params=search_params, hotel_code=self.TEST_HOTEL)
		assert len(entities) == 1
		assert isinstance(entities[0], Entity)
		assert entities[0].id == self.TEST_WPP_ID

		delete_entity("WebPageProperty", self.TEST_WPP_ID, hotel_code=self.TEST_HOTEL)

		entities = get_using_entity_and_params("WebPageProperty", search_params=search_params, hotel_code=self.TEST_HOTEL)
		assert len(entities) == 0
