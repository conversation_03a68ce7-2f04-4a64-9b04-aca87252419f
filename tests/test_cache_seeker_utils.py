import unittest

from paraty_commons_3.utils.cache_seeker.cache_seeker_utils import set_data, get_data


class Pepe:
    def __init__(self):
        self.name = "pepito"


class MyTestCase(unittest.TestCase):

    def test_str_cache(self):

        data = 'hello my <PERSON>!'

        set_data('prueba', data)

        result = get_data('prueba')

        assert result == data

    def test_pickle_cache(self):

        set_data('pepe', Pepe())
        new_pepe = get_data('pepe')

        assert new_pepe
