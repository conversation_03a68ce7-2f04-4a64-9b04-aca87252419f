import logging
from unittest.mock import MagicMock

from google.cloud import ndb

from paraty import Config
from paraty_commons_3.decorators.cache import cache_controller
from paraty_commons_3.decorators.cache.cache_controller import invalidate_cache
from paraty_commons_3.decorators.cache.distributed_strong_cache import distributed_cache, DistributedCacheEntry
import pytest

calls1 = None
calls2 = None


def add_ndb_context(func):

	def test_wrapper(*args, **kwargs):
		logging.info("Initializing context for project: %s and namespace:%s", Config.PROJECT, Config.NAMESPACE)
		client = ndb.Client(project='test-hotel', namespace='test')
		with client.context():
			result = func(*args, **kwargs)
			return result
	return test_wrapper


@pytest.fixture(autouse=True)
@add_ndb_context
def run_around_tests():
	global calls1, calls2
	calls1 = MagicMock(return_value="1")
	calls2 = MagicMock(return_value="2")

	invalidate_cache(hotel_code='test-hotel')

	ndb.delete_multi(
		DistributedCacheEntry.query().fetch(keys_only=True)
	)


def test_happy_flow():

	function1()
	function1()

	assert calls1.call_count == 1


def test_remove_all_entries():

	function1()
	cache_controller.invalidate_cache('test-hotel')
	function1()

	assert calls1.call_count == 2


def test_remove_entry():

	function2()
	cache_controller.invalidate_cache('test-hotel', entities=['Rate'])
	function2()

	assert calls2.call_count == 2


def test_remove_entry_does_not_remove_other_entries():

	function1()
	function2()
	cache_controller.invalidate_cache('test-hotel', entities=['Rate'])
	function2()
	function1()

	assert calls2.call_count == 2
	assert calls1.call_count == 1


@add_ndb_context
@distributed_cache(hotel_code_generator=lambda x: 'test-hotel')
def function1():
	return calls1()


@add_ndb_context
@distributed_cache(hotel_code_generator=lambda x: 'test-hotel', entities=['Rate'])
def function2():
	return calls2()





