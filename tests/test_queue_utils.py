import logging
import unittest
import uuid

from paraty.utils import interface_to_implement
from tests.test_specific_integration.test_integration_interface import TestIntegrationInterface
interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = TestIntegrationInterface()

from paraty_commons_3 import queue_utils



class BaseTest(unittest.TestCase):

	def test_add_tasks(self):
		logging.info(queue_utils.create_task('test_task', None, queue_name='processor6', in_seconds=60, task_name='test_task_%s' % str(uuid.uuid4())))

		task_list = queue_utils.list_tasks('processor6')
		for task in task_list:
			logging.info("Task Name: %s", task.name)

			if 'test' in task.app_engine_http_request.relative_uri:
				queue_utils.delete_task(task.name)

	def test_list_queue(self):

		task_list = queue_utils.list_tasks('processor5')
		for task in task_list:
			logging.info("Task Name: %s", task.name)
