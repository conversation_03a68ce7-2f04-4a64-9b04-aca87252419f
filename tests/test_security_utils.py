import unittest

from paraty import Config
from paraty_commons_3.security_utils import get_secret, SECURITY_PROJECT, safe_hash
import bcrypt


class BaseTest(unittest.TestCase):

	def test_get_secret(self):
		project_secret = get_secret(SECURITY_PROJECT, Config.PROJECT)
		self.assertTrue(project_secret)

	def test_safe_hash(self):
		# Test password hashing
		password = "test_password123"
		hashed_password = safe_hash(password)

		print(hashed_password)
		
		# Verify the hash is a string
		self.assertIsInstance(hashed_password, str)
		
		# Verify the hash can be used to check the password
		password_bytes = password.encode('utf-8')
		hashed_password_bytes = hashed_password.encode('utf-8')
		self.assertTrue(bcrypt.checkpw(password_bytes, hashed_password_bytes))
		
		# Verify different passwords produce different hashes
		password2 = "different_password"
		hashed_password2 = safe_hash(password2)
		print(hashed_password2)
		self.assertNotEqual(hashed_password, hashed_password2)
		
		# Verify the same password produces different hashes (due to different salts)
		hashed_password_again = safe_hash(password2)
		print(hashed_password_again)
		self.assertNotEqual(hashed_password, hashed_password_again)

		# We can use the new hash to check the password
		password2_bytes = password2.encode('utf-8')
		hashed_password2_bytes = hashed_password2.encode('utf-8')
		self.assertTrue(bcrypt.checkpw(password2_bytes, hashed_password2_bytes))

	def decrypt_text(self):
		pass
