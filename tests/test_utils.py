import logging

from google.cloud import ndb


from paraty import Config

# from xmldiff import main

def format_xml(xml_str):
	'''
	Useful when trying to compare Xml Files in unit tests
	'''
	from lxml import etree
	parser = etree.XMLParser(remove_blank_text=True)
	elem = etree.XML(xml_str, parser=parser)
	return etree.tostring(elem, pretty_print=True).decode('utf-8')


def dict_diff_recursive(dict1, dict2, path=""):
	differences = {}

	# Check for keys in dict1 but not in dict2 and keys with different values
	for key, value in dict1.items():
		if key not in dict2:
			differences[key] = (value, None)
		elif value != dict2[key]:
			differences[key] = (value, dict2[key])

	# Check for keys in dict2 but not in dict1
	for key, value in dict2.items():
		if key not in dict1:
			differences[key] = (None, value)

	return differences




def compare_xml(xml_1, xml_2):
	result = main.diff_texts(xml_1, xml_2)
	return result


def add_ndb_context(func):

	def test_wrapper(*args, **kwargs):
		logging.info("Initializing context for project: %s and namespace:%s", Config.PROJECT, Config.NAMESPACE)
		client = ndb.Client(project=Config.PROJECT, namespace=Config.NAMESPACE)
		with client.context():
			result = func(*args, **kwargs)
			return result
	return test_wrapper