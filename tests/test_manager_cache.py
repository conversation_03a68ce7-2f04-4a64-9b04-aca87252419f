import unittest
from paraty import app
from flask import g
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache

try:
    from test.test_base import TestBase  # old path. Not removed in case it is still valid in some other project
except ImportError:
    from tests.test_base import TestBase  # hotel-webs


class TestManagerCache(TestBase):

    def test_happy_flow(self):
        with app.app_context():
            @managers_cache(hotel_code_provider=lambda f,a,k: 'test-hotel', key_generator=lambda f, a, k: 'unit_test_1', entities='Rate,WebPageProperty')
            def my_test(a, b, c="optional1", d="optional2"):
                print("Inside Test")
                my_test2(3, 4)
                return f"{a} {b} {c} {d}"

            @managers_cache(hotel_code_provider=lambda f,a,k: 'test-hotel', key_generator=lambda f, a, k: 'unit_test_1', entities='Rate,WebPageProperty')
            def my_test2(a, b, c="optional1", d="optional2"):
                print("Inside Test")
                return f"{a} {b} {c} {d}"

            # To be found in redis
            print("\n\nFound in redis?")
            print(my_test(1, 2, c="optional1", d="optional2"))

            # To be found in thread local
            print("\n\nFound in thread local?")
            print(my_test(1, 2, c="optional1", d="optional2"))

            # Clear g to emulate a new thread
            g.requests_cache = {}

            # This should found in memory
            print("\n\nFound in memory?")
            print(my_test(1, 2, c="optional1", d="optional2"))


if __name__ == '__main__':
    unittest.main()
