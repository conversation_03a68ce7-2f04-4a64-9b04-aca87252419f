import logging
import unittest
from datetime import datetime
from unittest.mock import patch

from google.cloud import ndb

from paraty_commons_3.common_data.common_data_provider import CACHE_HOURS
from paraty_commons_3.decorators.cache.cache_controller import is_entry_valid, invalidate_cache, cache_thread_state
from paraty_commons_3.decorators.cache.distributed_strong_cache import distributed_cache


class TestDistributedCache(unittest.TestCase):

    @patch('paraty_commons_3.decorators.cache.cache_controller._get_timestamps_from_remote_cache')
    @patch('paraty_commons_3.decorators.cache.distributed_strong_cache.Config')
    @patch('paraty_commons_3.decorators.cache.distributed_strong_cache.USE_REDIS')
    @patch('paraty_commons_3.decorators.cache.distributed_strong_cache.USE_PERSISTENCE')
    def test_cache_refresh_correctly_with_redis(self, *args):
        args[2].REDIS_HOST = "*************"
        args[2].REDIS_PORT = 6666
        args[2].REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"
        times_called = 0

        @distributed_cache(hotel_code_generator=lambda x: 'demo5', compressed=False, entities=['TestEntityParatyCommon3'], memory_life_seconds=3600 * CACHE_HOURS)
        def _demo_function():
            nonlocal times_called
            times_called += 1
            return times_called

        create_timestamp = lambda: datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S.%f')

        ndb_connection = ndb.Client(project='test-hotel', namespace='demo5')
        with ndb_connection.context():

            args[3].return_value = {'global_timestamp': create_timestamp()}
            result = _demo_function()
            self.assertEquals(result, 1)

            args[3].return_value = {'global_timestamp': create_timestamp()}
            cache_thread_state.__dict__.clear() # Simulate a new request, otherwise is normal to be cached
            result2 = _demo_function()
            self.assertEquals(result2, 2)




def test_happy_flow():

    hotel_code = 'test-hotel'
    entities = ['Roomtype']

    utcnow = str(datetime.utcnow())

    # No information, by default the cache is not valid and we create a default timestamp
    invalidate_cache(hotel_code)
    result = is_entry_valid(hotel_code, utcnow, entities)
    assert not result

    utcnow = str(datetime.utcnow())
    result = is_entry_valid(hotel_code, utcnow, entities)
    assert result


def test_entities_invalidated():
    hotel_code = 'test-hotel'
    entities = ['RoomType', 'Rate']

    utcnow = str(datetime.utcnow())

    # No information, by default the cache is not valid and we create a default timestamp
    invalidate_cache(hotel_code)

    utcnow = str(datetime.utcnow())
    result = is_entry_valid(hotel_code, utcnow, entities)
    assert result

    invalidate_cache(hotel_code, ['RoomType'])

    result = is_entry_valid(hotel_code, utcnow, entities)
    assert not result

    utcnow = str(datetime.utcnow())
    result = is_entry_valid(hotel_code, utcnow, entities)
    assert result