import sys
import threading

'''
Useful class to redirect the stdout to a file, in a thread per thread version
'''

class SysRedirect(object):
	def __init__(self):
		self.terminal = sys.stdout                  # To continue writing to terminal
		self.log={}                                 # A dictionary of file pointers for file logging

	def register(self,filename):                    # To start redirecting to filename
		ident = threading.currentThread().ident     # Get thread ident (thanks @michscoots)
		if ident in self.log:                       # If already in dictionary :
			self.log[ident].close()                 # Closing current file pointer
		self.log[ident] = open(filename, "a")       # Creating a new file pointed associated with thread id

	def write(self, message):
		self.terminal.write(message)                # Write in terminal (comment this line to remove terminal logging)
		ident = threading.currentThread().ident     # Get Thread id
		if ident in self.log:                       # Check if file pointer exists
			self.log[ident].write(message)          # write in file
		else:                                       # if no file pointer
			for ident in self.log:                  # write in all thread (this can be replaced by a Write in terminal)
				self.log[ident].write(message)

	def flush(self):
		pass

if __name__ == '__main__':
	sys.stdout = SysRedirect()

	sys.stdout.register('threadX.log')

	print("Hello World")

	sys.stdout.register('threadX2.log')

	print("Hello World2")

