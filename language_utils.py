# Note, this needs to be synchronized with com.paraty.common.services.shared.configuration.Languages
SPANISH = 'SPANISH'
BASQUE = 'BASQUE'
ENGLISH = 'ENGLISH'
GERMAN = 'GERMAN'
PORTUGUESE = 'PORTUGUESE'
ITALIAN = 'ITALIAN'
FRENCH = 'FRENCH'
DUTCH = 'DUTCH'
RUSSIAN = 'RUSSIAN'
CATALAN = 'CATALAN'
FINNISH = 'FINNISH'
POLISH = 'POLISH'
SWEDISH = 'SWEDISH'
JAPANESE = 'JAPANESE'
KOREAN = 'KOREAN'
CHINESE_TRADITIONAL = 'CHINESE_TRADITIONAL'
CHINESE_SIMPLIFIED = 'CHINESE_SIMPLIFIED'


LANGUAGE_TITLES = {
    SPANISH: u"Español",
    BASQUE: u"Euskara",
    ENGLISH: u"English",
    GERMAN: u"Deutsch",
    PORTUGUESE: u"Português",
    ITALIAN: u"Italiano",
    FRENCH: u"Français",
    DUTCH: u"Dutch",
    RUSSIAN: u"Russian",
    POLISH: u"Polski",
    CATALAN: u"Català",
    FINNISH: u"Suomi",
    SWEDISH: u"Swedish",
    CHINESE_TRADITIONAL: u'Traditional Chinese',
    CHINESE_SIMPLIFIED: u'Simplified Chinese'
}

ISO_TO_LANGUAGE_CODES = {
    'it': ITALIAN,
    'es': SPANISH,
    'eu': BASQUE,
    'en': ENGLISH,
    'de': GERMAN,
    'fr': FRENCH,
    'pt': PORTUGUESE,
    'pl': POLISH,
    'ca': CATALAN,
    'ru': RUSSIAN,
    'nl': DUTCH,
    'fi': FINNISH,
    'zh-hant': CHINESE_TRADITIONAL,
    'zh-hans': CHINESE_SIMPLIFIED
}

LANGUAGE_CODES = {
    SPANISH: 'es',
    BASQUE: 'eu',
    ENGLISH: 'en',
    GERMAN: 'de',
    PORTUGUESE: 'pt',
    POLISH: 'pl',
    ITALIAN: 'it',
    FRENCH: 'fr',
    FINNISH: 'fi',
    SWEDISH: 'sv',
    CATALAN: 'ca',
    DUTCH: 'nl',
    RUSSIAN: 'ru',
    CHINESE_TRADITIONAL: 'zh-hant',
    CHINESE_SIMPLIFIED: 'zh-hans'
}

LOCALE_CODES = {
    SPANISH: 'es_ES',
    BASQUE: 'eu_ES',
    ENGLISH: 'en_US',
    GERMAN: 'de_DE',
    FRENCH: 'fr_FR',
    PORTUGUESE: 'pt_PT',
    POLISH: 'pl_PL',
    CATALAN: 'ca_ES',
    RUSSIAN: 'ru_RU',
    DUTCH: 'nl_NL',
    FINNISH: 'fi_FI',
    ITALIAN: 'it_IT',
    SWEDISH: 'sv_SE',
    JAPANESE: 'ja_JP',
    KOREAN: 'ko_KR',
    CHINESE_TRADITIONAL: 'zh_HANT',
    CHINESE_SIMPLIFIED: 'zh_HANS'
}

NON_EUROPEAN_CHARS_LANGUAGES = [CHINESE_TRADITIONAL]


def get_language_code(language):
    return LANGUAGE_CODES.get(language, 'en')

def get_language_locale(language):
    return LOCALE_CODES.get(language, 'en_US')


def get_language_from_code(code_to_search):
    for lang_full, lang_shortcode in LANGUAGE_CODES.items():
        if lang_shortcode == code_to_search:
            return lang_full

    return SPANISH


def get_language_in_manager_based_on_locale(locale):

    if not locale:
        return SPANISH
    else:
        dictionary = {
            'es': SPANISH,
            'eu': BASQUE,
            'en': ENGLISH,
            'de': GERMAN,
            'ca': CATALAN,
            'fr': FRENCH,
            'pt': PORTUGUESE,
            'it': ITALIAN,
            'es_ES': SPANISH,
            "en_EN": ENGLISH,
            "pt_PT": PORTUGUESE,
            'it_IT': ITALIAN,
            'de_DE': GERMAN,
        }
        return dictionary.get(locale, SPANISH)


def get_all_languages():
    return SPANISH, ENGLISH, GERMAN, PORTUGUESE, ITALIAN


def get_language_title(languageCode):
    return LANGUAGE_TITLES.get(languageCode, "")

def get_language_locale(language):
    return LOCALE_CODES.get(language, 'en_US')
