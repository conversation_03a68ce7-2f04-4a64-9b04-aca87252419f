from typing import Optional, TypedDict


class ManagerUser(TypedDict):
    configurationMap: list[str]
    enabled: bool
    lastPasswordChange: str
    name: str
    permission: str

    accesibleApplications: list[int]
    description: Optional[str]

class UserSeekerLoginResponse(TypedDict):
    success: bool
    requires_2fa: bool
    message: str
    user: ManagerUser | None


class UserSeekerResponseRequest(TypedDict):
    success: bool
    status: str

    cookie: Optional[str]
    user: Optional[ManagerUser]
