import subprocess

from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils


def _get_all_hotel_projects():
    all_hotels = hotel_manager_utils.get_all_valid_hotels()

    result = set()

    for hotel in all_hotels:
        project, namespace = datastore_utils.get_project_and_namespace_from_hotel(hotel)
        if project:  # and 'ona-' in project:
            result.add(project)

    return result


def execute_command(cmd, print_it=True):
    if print_it:
        print(cmd)

    subprocess.check_call(cmd, shell=True)


def update_cron_job(cron_file_path, project_name):
    my_command = 'gcloud app deploy %s --project %s  --quiet' % (cron_file_path, project_name)
    execute_command(my_command)


if __name__ == '__main__':

    # fmatheis, replace path with your own path
    CRON_LOCATION = '/Users/<USER>/projects/hotel-webs/source/cron.yaml'

    all_projects = _get_all_hotel_projects()

    print('Updating cron jobs for %s projects' % len(all_projects))

    for i, project in enumerate(all_projects):
        print('Updating project %s of %s' % (i, len(all_projects)))
        try:
            update_cron_job(f'{CRON_LOCATION}', project)
        except Exception as e:
            print(e)
            print('Could not update cron job for project %s' % project)
