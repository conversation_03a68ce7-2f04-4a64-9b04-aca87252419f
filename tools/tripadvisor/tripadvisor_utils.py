from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def find_value(my_value):
    all_valid_hotels = get_all_valid_hotels()
    params = []
    for hotel in all_valid_hotels:
        params.append((hotel, my_value))

    concurrency_utils.execute_in_parallel(find_partner_id_in_hotel, params, max_concurrency=20)


def find_partner_id_in_hotel(hotel, my_value):
    tripadvisor_config = get_integration_configuration_of_hotel(hotel, 'tripadvisor')
    if not tripadvisor_config:
        return

    configurations = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in tripadvisor_config[0].get('configurations')}

    if my_value in configurations.values():
        print("FOUND: " + hotel['name'])



if __name__ == '__main__':
    find_value('https://www.onahotels.com/en/apart-hotel-marbella-inn.html')
