import json

import requests

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_entity

AFFECTED_BOOKINGS = ["R2399B8897", "RF8393C515", "R5756BD72C", "R9729646B2", "R0F39B20E3",
                     "R5B9D2818D", "R30CB0CDD6", "R2247CB4CF", "R9698C6716", "R977F83850", "R9698C6716", "RF65739045",
                     "R8F82FCDF2", "C8620B0D2", "R0288F00A0", "R826D0C53A", "RC95FFBBC7", "R17888C202", "R67A55F32C",
                     "RDF43FC82F", "RF650E2634", "R2CFE0A013", "R1AA0FD2C8", "R6A07D9D6D", "R675E6E95B", "R237F51792",
                     "R0074A9A2B", "R35806705F", "RB207D2B82", "RAA4D02776", "RB8EF7ACF8"]

AVAILABLE_HOTELS = ["casual-demo", "casual-vintage-valencia", "casual-deportes", "casual-duende",
                    "casual-apartments-malaga", "casual-letras-sevilla", "casual-incas", "casual-artes-valencia",
                    "casual-belle-epoque", "casual-cine-valencia", "casual-civilizaciones", "casual-don-juan-tenorio",
                    "casual-gurea-bilbao", "casual-jazz-sansebastian", "casual-mardones", "casual-mar-malaga",
                    "casual-musica-valencia", "casual-olas-sansebastian", "casual-serantes-bilbao", "casual-socarrat",
                    "casual-teatro-madrid", "casual-colours", "casual-raizes", "casual-rinascimiento-florencia",
                    "casual-corporativa", "casual-bianco-firenze", "casual-corporativa", "casual-pop-art",
                    "casual-blue", 'casual-arriaga']


def get_booking(booking_id):
    for hotel in AVAILABLE_HOTELS:
        booking = get_using_entity_and_params('Reservation', search_params=[('identifier', '=', booking_id)], hotel_code=hotel)
        if booking:
            return booking[0], hotel


def get_user_category(user_id):
    post_params = {
        'action': 'get_category',
        'idmember': user_id,
        'namespace': 'casual-corporativa'
    }

    target_endpoint = 'https://loyalty-seeker.appspot.com/transactions/'
    response = requests.post(target_endpoint, data=post_params)
    return response.json()


def get_user_info(email):
    user_info = get_using_entity_and_params('UserClub', search_params=[('email', '=', email)],
                                            hotel_code='casual-corporativa')
    if user_info:
        return user_info[0]


def update_booking_extra_info(booking, hotel_code):
    actual_extra_info = booking['extraInfo']
    if actual_extra_info:
        actual_extra_info = json.loads(actual_extra_info)
    else:
        actual_extra_info = {}

    actual_user_info = get_user_info(booking['email'])
    if not actual_user_info:
        print("Este usuario no existe en el club: {}".format(booking['email']))
        return

    actual_user_category = get_user_category(actual_user_info['idmember'])

    target_user_club_data = dict(actual_user_info)
    target_user_club_data['category_data'] = actual_user_category

    booking['extraInfo'] = json.dumps(actual_extra_info)
    # TODO: Cuando esté todo bien se puede descomentar para que actualice lo necesario
    # save_entity(booking, hotel_code=hotel_code)
    return actual_user_info['idmember']


def transfer_user_transaction(booking_id, idmember):
    user_transaction = get_using_entity_and_params('transactionsClub', search_params=[('booking_id', '=', booking_id)], hotel_code='loyalty-seeker:casual-corporativa')
    if user_transaction:
        pass


# TODO: Pendiente de revisar
# user_transaction[0]['idmember'] = idmember
# save_entity(user_transaction[0], hotel_code='loyalty-seeker:casual-corporativa')

def add_user_transaction(idmember, booking_id, hotel_namespace):
    params = {
        'action': 'add_booking_transaction',
        'idmember': idmember,
        'amount': True,  # TODO: 10% del total de la reserva,
        'booking_id': booking_id,
        'namespace': 'casual-corporativa',
        'creator': hotel_namespace
    }

    requests.post('https://loyalty-seeker.appspot.com/bookings/', data=params)


def run_script():
    for booking_id in AFFECTED_BOOKINGS:
        booking, hotel_code = get_booking(booking_id)
        idmember = update_booking_extra_info(booking, hotel_code)
        if idmember:
            if True:  # TODO: Si el startDate de la reserva es pasada se debe pasar la transaccion existente a otro idmember
                if not transfer_user_transaction(booking_id, idmember):
                    # TODO: Si no existe la transaccion se debe crear
                    add_user_transaction(idmember, booking_id, hotel_code)

            else:
                # no hacemos nada porque se le sumara la transaccion al usuario el dia de la entrada
                pass


if __name__ == '__main__':
    run_script()
