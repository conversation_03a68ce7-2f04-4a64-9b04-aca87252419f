from paraty_commons_3.common_data.common_data_provider import get_promotions_of_hotel, get_rates_of_hotel
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, flush_entity_cache


def remove_rates_of_promotions(hotel_code):
    all_promotions = get_promotions_of_hotel({'applicationId': hotel_code})
    valid_promotions = [x for x in all_promotions if x.get('promocode')]

    all_rates = get_rates_of_hotel({'applicationId': hotel_code})
    club_rates = [x for x in all_rates if 'club' in x['localName'].lower()]

    for promotion in valid_promotions:
        promotion['rate'] = "-%s" % ";".join([x['key'] for x in club_rates])

    for promotion in valid_promotions:
        promotion_key = promotion.pop('key')
        promotion_id = promotion['id']
        save_to_datastore('Promotion', promotion_id, promotion, hotel_code=hotel_code)

    flush_entity_cache(hotel_code, 'Promotion')


if __name__ == '__main__':

    all_valid_hotels = [x['applicationId'] for x in get_all_valid_hotels() if 'applicationId' in x]

    for hotel_code in all_valid_hotels:

        if not 'itaca-' in hotel_code:
            continue

        remove_rates_of_promotions(hotel_code)
