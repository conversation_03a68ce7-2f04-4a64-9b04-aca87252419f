<?xml version="1.0" encoding="utf-8"?>
<OTA_ResRetrieveRS TimeStamp="2024-06-07T17:31:53-05:00">
    <ReservationList>
        <HotelReservation CreateDateTime="2024-06-07T12:30:51-05:00" ResStatus="Book">
            <POS>
                <Source>
                    <BookingChannel>
                        <CompanyName Code="PARATYWEB MXN"/>
                    </BookingChannel>
                </Source>
            </POS>
            <UniqueId ID="40206023" Type="16"/>
            <RoomStays>
                <RoomStay RoomStayReference="1">
                    <TimeSpan Start="2024-07-11T01:00:00-05:00" End="2024-07-14T01:00:00-05:00"/>
                    <RoomTypes>
                        <RoomType RoomTypeCode="PYRST"/>
                    </RoomTypes>
                    <RatePlans>
                        <RatePlan RatePlanCode="AI">
                            <AdditionalDetails>
                                <AdditionalDetail Type="43" Code="AI"/>
                            </AdditionalDetails>
                        </RatePlan>
                    </RatePlans>
                    <GuestCounts>
                        <GuestCount AgeQualifyingCode="10" Count="2"/>
                    </GuestCounts>
                    <Comments>
                        <Comment>
                            <Text>b&#39;| PP- Pagado 18173.67 restan 0.0| Total 44402.7 | Ref. stripe.client:
                                cus_QFbjcyIcLOkONe| Ref. stripe: pi_3PP6aOHSmBgT71vI0O1L5xx7| 41% de descuento&#39;
                            </Text>
                        </Comment>
                    </Comments>
                    <ResGuestRPHs>
                        <ResGuestRPH RPH="1"/>
                        <ResGuestRPH RPH="2"/>
                    </ResGuestRPHs>
                    <RoomRates>
                        <RoomRate>
                            <Rates>
                                <Rate EffectiveDate="2024-07-11" UnitMultiplier="1">
                                    <Total AmountAfterTax="5705.6900000000005" CurrencyCode="MXN"/>
                                </Rate>
                                <Rate EffectiveDate="2024-07-12" UnitMultiplier="1">
                                    <Total AmountAfterTax="6233.990000000001" CurrencyCode="MXN"/>
                                </Rate>
                                <Rate EffectiveDate="2024-07-13" UnitMultiplier="1">
                                    <Total AmountAfterTax="6233.990000000001" CurrencyCode="MXN"/>
                                </Rate>
                            </Rates>
                        </RoomRate>
                    </RoomRates>
                    <Total AmountAfterTax="18173.67" CurrencyCode="MXN"/>
                    <ServiceRPHs>
                        <ServiceRPH RPH="1"/>
                    </ServiceRPHs>
                </RoomStay>
                <RoomStay RoomStayReference="2">
                    <TimeSpan Start="2024-07-11T01:00:00-05:00" End="2024-07-14T01:00:00-05:00"/>
                    <RoomTypes>
                        <RoomType RoomTypeCode="PYRST"/>
                    </RoomTypes>
                    <RatePlans>
                        <RatePlan RatePlanCode="AI">
                            <AdditionalDetails>
                                <AdditionalDetail Type="43" Code="AI"/>
                            </AdditionalDetails>
                        </RatePlan>
                    </RatePlans>
                    <GuestCounts>
                        <GuestCount AgeQualifyingCode="10" Count="3"/>
                    </GuestCounts>
                    <Comments>
                        <Comment>
                            <Text>b&#39;| PP- Pagado 26229.03 restan 0.0| Total 44402.7 | Ref. stripe.client:
                                cus_QFbjcyIcLOkONe| Ref. stripe: pi_3PP6aOHSmBgT71vI0O1L5xx7| 41% de descuento&#39;
                            </Text>
                        </Comment>
                    </Comments>
                    <ResGuestRPHs>
                        <ResGuestRPH RPH="1"/>
                        <ResGuestRPH RPH="2"/>
                        <ResGuestRPH RPH="3"/>
                    </ResGuestRPHs>
                    <RoomRates>
                        <RoomRate>
                            <Rates>
                                <Rate EffectiveDate="2024-07-11" UnitMultiplier="1">
                                    <Total AmountAfterTax="8234.699999999999" CurrencyCode="MXN"/>
                                </Rate>
                                <Rate EffectiveDate="2024-07-12" UnitMultiplier="1">
                                    <Total AmountAfterTax="8997.17" CurrencyCode="MXN"/>
                                </Rate>
                                <Rate EffectiveDate="2024-07-13" UnitMultiplier="1">
                                    <Total AmountAfterTax="8997.17" CurrencyCode="MXN"/>
                                </Rate>
                            </Rates>
                        </RoomRate>
                    </RoomRates>
                    <Total AmountAfterTax="26229.03" CurrencyCode="MXN"/>
                    <ServiceRPHs></ServiceRPHs>
                </RoomStay>
            </RoomStays>
            <ResGuests>
                <ResGuest PrimaryIndicator="true" ResGuestRPH="1">
                    <Profiles>
                        <ProfileInfo>
                            <Profile>
                                <Customer>
                                    <PersonName>
                                        <NamePrefix/>
                                        <GivenName>Rodrigo</GivenName>
                                        <Surname>Valdez Rodriguez</Surname>
                                    </PersonName>
                                    <Email><EMAIL></Email>
                                </Customer>
                            </Profile>
                        </ProfileInfo>
                    </Profiles>
                </ResGuest>
                <ResGuest PrimaryIndicator="false" ResGuestRPH="2">
                    <Profiles>
                        <ProfileInfo>
                            <Profile>
                                <Customer>
                                    <PersonName>
                                        <NamePrefix/>
                                        <GivenName>Rodrigo2</GivenName>
                                        <Surname>Valdez Rodriguez2</Surname>
                                    </PersonName>
                                    <Email><EMAIL></Email>
                                </Customer>
                            </Profile>
                        </ProfileInfo>
                    </Profiles>
                </ResGuest>
                <ResGuest PrimaryIndicator="false" ResGuestRPH="3">
                    <Profiles>
                        <ProfileInfo>
                            <Profile>
                                <Customer>
                                    <PersonName>
                                        <NamePrefix/>
                                        <GivenName>Rodrigo3</GivenName>
                                        <Surname>Valdez Rodriguez3</Surname>
                                    </PersonName>
                                    <Email><EMAIL></Email>
                                </Customer>
                            </Profile>
                        </ProfileInfo>
                    </Profiles>
                </ResGuest>
                <ResGuest PrimaryIndicator="false" ResGuestRPH="4">
                    <Profiles>
                        <ProfileInfo>
                            <Profile>
                                <Customer>
                                    <PersonName>
                                        <NamePrefix/>
                                        <GivenName>Rodrigo4</GivenName>
                                        <Surname>Valdez Rodriguez4</Surname>
                                    </PersonName>
                                    <Email><EMAIL></Email>
                                </Customer>
                            </Profile>
                        </ProfileInfo>
                    </Profiles>
                </ResGuest>
                <ResGuest PrimaryIndicator="false" ResGuestRPH="5">
                    <Profiles>
                        <ProfileInfo>
                            <Profile>
                                <Customer>
                                    <PersonName>
                                        <NamePrefix/>
                                        <GivenName>Rodrigo5</GivenName>
                                        <Surname>Valdez Rodriguez5</Surname>
                                    </PersonName>
                                    <Email><EMAIL></Email>
                                </Customer>
                            </Profile>
                        </ProfileInfo>
                    </Profiles>
                </ResGuest>
            </ResGuests>
            <ResGlobalInfo>
                <HotelReservationIDs>
                    <HotelReservationID ResID_Type="14" ResID_Value="40206023"/>
                </HotelReservationIDs>
                <Profiles>
                    <ProfilesInfo>
                        <Profile ProfileType="1">
                            <Customer>
                                <PersonName>
                                    <NamePrefix>SR</NamePrefix>
                                    <GivenName>Rodrigo</GivenName>
                                    <Surname>Valdez Rodriguez</Surname>
                                </PersonName>
                                <Telephone PhoneNumber="+52 7711317281"/>
                                <Email><EMAIL></Email>
                                <Address/>
                            </Customer>
                        </Profile>
                        <Profile ProfileType="0">
                            <Customer>
                                <PersonName>
                                    <NamePrefix>SR</NamePrefix>
                                    <GivenName>Rodrigo2</GivenName>
                                    <Surname>Valdez Rodriguez2</Surname>
                                </PersonName>
                                <Telephone PhoneNumber="+52 7711317281"/>
                                <Email><EMAIL></Email>
                                <Address/>
                            </Customer>
                        </Profile>
                        <Profile ProfileType="0">
                            <Customer>
                                <PersonName>
                                    <NamePrefix>SR</NamePrefix>
                                    <GivenName>Rodrigo3</GivenName>
                                    <Surname>Valdez Rodriguez3</Surname>
                                </PersonName>
                                <Telephone PhoneNumber="+52 7711317281"/>
                                <Email><EMAIL></Email>
                                <Address/>
                            </Customer>
                        </Profile>
                        <Profile ProfileType="0">
                            <Customer>
                                <PersonName>
                                    <NamePrefix>SR</NamePrefix>
                                    <GivenName>Rodrigo4</GivenName>
                                    <Surname>Valdez Rodriguez4</Surname>
                                </PersonName>
                                <Telephone PhoneNumber="+52 7711317281"/>
                                <Email><EMAIL></Email>
                                <Address/>
                            </Customer>
                        </Profile>
                        <Profile ProfileType="0">
                            <Customer>
                                <PersonName>
                                    <NamePrefix>SR</NamePrefix>
                                    <GivenName>Rodrigo5</GivenName>
                                    <Surname>Valdez Rodriguez5</Surname>
                                </PersonName>
                                <Telephone PhoneNumber="+52 7711317281"/>
                                <Email><EMAIL></Email>
                                <Address/>
                            </Customer>
                        </Profile>
                    </ProfilesInfo>
                </Profiles>
                <BasicPropertyInfo HotelCode="HT01"/>
                <Total AmountAfterTax="44402.7" CurrencyCode="MXN"/>
                <DepositPayments/>
            </ResGlobalInfo>
            <Services>
                <Service Quantity="1" ServiceInventoryCode="TRASPORTACIONES" ServiceRPH="1" Start="2024-07-11"
                         End="2024-07-14">
                    <Price>
                        <Total AmountAfterTax="0.0" CurrencyCode="MXN"/>
                    </Price>
                </Service>
            </Services>
        </HotelReservation>
    </ReservationList>
</OTA_ResRetrieveRS>