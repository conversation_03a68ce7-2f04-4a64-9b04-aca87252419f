from paraty_commons_3 import date_utils
from paraty_commons_3.datastore import datastore_communicator


def get_organized_user_entries(hotel_code: str, from_date_str: str, entry_filter):
    '''
    Returns a List, sorted by IP appearances
    Each element of the list is a dictionary, showing the paths and number of appearances each
    '''

    from_date = date_utils.string_to_date(from_date_str)

    all_entries = datastore_communicator.get_using_entity_and_params("UserEntry", search_params=[('timestamp', '>', from_date)], hotel_code=hotel_code)

    all_valid_user_entries = filter(entry_filter, all_entries)

    result = {}

    for entry in all_valid_user_entries:
        ip = entry['ip']
        if not ip in result:
            result[ip] = 0

        result[ip] += 1

    list_ips = []
    for k,v in result.items():
        list_ips.append((k,v))

    list_ips.sort(key=lambda x: x[1])

    return list_ips




def filter_booking1(entry):
    return True

if __name__ == '__main__':
    result = get_organized_user_entries('entremares-hotel', '2022-02-20', filter_booking1)
    print(result)