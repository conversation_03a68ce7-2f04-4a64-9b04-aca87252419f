import csv

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def create_excel_file():


    with open('hotel_information.csv', 'w') as csvfile:
        fieldnames = ['name', 'hotel_code', 'id']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        all_hotels = get_all_valid_hotels()
        all_hotels.sort(key=lambda x: x['name'])

        for hotel in all_hotels:

            writer.writerow({'name': hotel['name'], 'hotel_code': hotel['applicationId'], 'id': hotel['id']})


if __name__ == '__main__':
    create_excel_file()