import logging

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item
from paraty_commons_3.common_data.hotel_zone_utils import get_eu_hotels, get_usa_hotels
from paraty_commons_3.datastore.datastore_communicator import save_entity, delete_entity, save_to_datastore
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotel_metadata, get_all_valid_hotels
from tools.configuration.hotel_in_zone_checker import MANAGER_CONFIG, EU_MANAGER_SERVER_PATH, CALENDAR_CONFIG, EU_MANAGER_BACKGROUND_PATH, SPINACH_CONFIG


# def get_project_number(project_id) -> Optional[str]:
#     """Given a project id, return the project number"""
#     # Create a client
#     client = resourcemanager_v3.ProjectsClient()
#     # Initialize request argument(s)
#     request = resourcemanager_v3.SearchProjectsRequest(query=f"id:{project_id}")
#     # Make the request
#     page_result = client.search_projects(request=request)
#     # Handle the response
#     for response in page_result:
#         if response.project_id == project_id:
#             project = response.name
#             return project.replace('projects/', '')


def complete_missing_location_prefix():
    all_metadata = get_all_hotel_metadata()


    for application_id, metadata in all_metadata.items():
        if metadata['location_prefix'] == '' and 'axis' in application_id:
            metadata['location_prefix'] = 'e~'


def remove_unused_spinach_configs():
    pass


def _fix_config(hotel_code, config_name, eu_config, usa_config=None, create_if_missing=True):

    is_hotel_eu = hotel_code in get_eu_hotels()
    hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
    configuration = get_hotel_advance_config_item(hotel, config_name)

    commented_configuration = get_hotel_advance_config_item(hotel,  "_" + config_name)

    logging.info(f"Checking {hotel_code} {config_name}")

    if configuration:
        configuration = configuration[0]

        # USA is by default, so if in USA and no config, remove the existing one
        if not is_hotel_eu and not usa_config:
            user_input = input(f"Change {config_name} -> {configuration['value']}: {configuration['value']} to point to EU? (y/n): ").lower()
            if user_input == 'y':
                delete_entity('ConfigurationProperty', configuration, hotel_code=hotel_code)

        # USA and usa but with different value
        elif not is_hotel_eu and usa_config and configuration['value'] != usa_config:
            user_input = input(f"Change {config_name} -> {configuration['value']}: {configuration['value']} to point to EU? (y/n): ").lower()
            if user_input == 'y':
                configuration['value'] = usa_config
                save_entity(configuration, hotel_code=hotel_code)

        # EU and eu but with different value
        elif is_hotel_eu and configuration['value'] != eu_config:
            user_input = input(f"Change {config_name} -> {configuration['value']}: {configuration['value']} to point to EU? (y/n): ").lower()
            if user_input == 'y':
                configuration['value'] = eu_config
                save_entity(configuration, hotel_code=hotel_code)

        else:
            return

    elif create_if_missing:

        expected_value = eu_config if is_hotel_eu else usa_config
        if not expected_value:
            return

        if commented_configuration:
            configuration = commented_configuration[0]
            configuration['mainKey'] = config_name
            configuration['value'] = expected_value
            save_entity(configuration, hotel_code=hotel_code)

        else:
            new_configuration = {
                'mainKey': config_name,
                'value': expected_value
            }

            save_to_datastore('ConfigurationProperty', None, new_configuration, hotel_code=hotel_code)

    else:
        return

    if is_hotel_eu:
        logging.info(f"Updated {hotel_code} {config_name} to {eu_config}")
    else:
        logging.info(f"Updated {hotel_code} {config_name} to {usa_config}")

    hotel_manager_utils.flush_entity_cache(hotel_code, "ConfigurationProperty")


def _point_hotel_to_expected_location(hotel_code):

    _fix_config(hotel_code, MANAGER_CONFIG, EU_MANAGER_SERVER_PATH)
    _fix_config(hotel_code, CALENDAR_CONFIG, EU_MANAGER_BACKGROUND_PATH)




def _point_eu_hotels_to_eu_manager():
    for hotel_code in get_eu_hotels():

        logging.info(f"Pointing hotel {hotel_code} to EU manager")
        _point_hotel_to_expected_location(hotel_code)


'''
Note that this script will ask for permission to change existing configurations, in case there is any special configuration.
'''

if __name__ == '__main__':
    # _disable_spinach()
    # _fix_eu_spinach_if_configured()
    # _fix_us_spinach_if_configured()
    _point_eu_hotels_to_eu_manager()
    # complete_missing_location_prefix()