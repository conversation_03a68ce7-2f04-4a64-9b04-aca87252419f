import csv

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.common_data.hotel_zone_utils import get_eu_hotels, get_usa_hotels
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

SPINACH_CONFIG = 'Use Spinach'
MANAGER_CONFIG = 'Use alternative manager'
CALENDAR_CONFIG = 'Use alternative background manager'
DEFAULT_MANAGER = 'https://admin-hotel.appspot.com'
EU_MANAGER_SERVER_PATH = 'https://europe-4fpduq6apq-ew.a.run.app'
EU_MANAGER_BACKGROUND_PATH = 'https://background-eu-4fpduq6apq-ew.a.run.app'
EU_MANAGER = 'admin-hotel3'
USA_MANAGER = 'admin-hotel'


def _get_all_hotels_with_config_with_content(configuration_name, exact_match, expected_content) -> list[str]:
    all_valid_hotels = get_all_valid_hotels()
    results = {}
    params = [(x, configuration_name, exact_match, results) for x in all_valid_hotels]
    concurrency_utils.execute_in_parallel(_get_configuration_containing_text_in_key, params)

    result = []

    for hotel_code, configurations in results.items():
        if not configurations:
            continue

        if expected_content in configurations[0]['value']:
            result.append(hotel_code)

    return result


def _get_configuration_containing_text_in_key(hotel, text, require_exact_match, all_results):

    all_configs = common_data_provider.get_hotel_all_advance_configs(hotel)

    result = []
    for config in all_configs:
        current_key = config.get('mainKey')
        if require_exact_match:
            if current_key == text:
                result.append(config)
        elif text in current_key:
            result.append(config)

    all_results[hotel['applicationId']] = result


def get_hotels_using_eu_manager() -> list[str]:

    return _get_all_hotels_with_config_with_content(MANAGER_CONFIG, True, EU_MANAGER)


def get_hotels_using_eu_calendar() -> list[str]:
    return _get_all_hotels_with_config_with_content(CALENDAR_CONFIG, True, EU_MANAGER)



def create_excel_file():
    '''
    Create csv file with columns: hotel_code, zone, manager, spinach, calendar
    '''
    # Create CSV file

    europe_hotels = get_eu_hotels()
    usa_hotels = get_usa_hotels()

    eu_manager = get_hotels_using_eu_manager()
    eu_calendar = get_hotels_using_eu_calendar()

    with open('hotel_zone_configuration.csv', 'w') as csvfile:
        fieldnames = ['hotel_code', 'zone', 'manager', 'spinach', 'calendar']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        all_hotels = get_all_valid_hotels()
        all_hotels.sort(key=lambda x: x['applicationId'])

        for hotel in all_hotels:

            if hotel['applicationId'] in europe_hotels:
                zone = 'Europe'
            elif hotel['applicationId'] in usa_hotels:
                zone = 'USA'
            else:
                zone = 'Unknown'

            if hotel['applicationId'] in eu_manager:
                manager = 'Europe'
            else:
                manager = 'USA'

            if hotel['applicationId'] in eu_calendar:
                calendar = 'Europe'
            else:
                calendar = 'USA'

            writer.writerow({'hotel_code': hotel['applicationId'], 'zone': zone, 'manager': manager, 'calendar': calendar})


if __name__ == '__main__':
    create_excel_file()

    # print(get_hotels_using_eu_manager())
    # print(get_hotels_using_eu_spinach())


