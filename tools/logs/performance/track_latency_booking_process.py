import time
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime, timedelta

import google.cloud.logging

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

SPECIFIC_MODULE = None
TARGET_PROJECT = 'secure-booking-co3'
NAME_REGEX_OF_HOTEL = 'virrey'

def search_logs_project(project_name):
    NOW_DATE = datetime.now() - timedelta(hours=4)
    NOW_DATE = NOW_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')


    # if project not given, it will be inferred from the environment
    client = google.cloud.logging.Client(project=project_name, _use_grpc=False)

    # Filter to get only last 3 hours
    target_filter = f'"{NAME_REGEX_OF_HOTEL}" AND (httpRequest.requestMethod="POST" AND "/booking") OR (httpRequest.requestMethod="GET" AND "/booking3")'
    target_filter += f' AND NOT httpRequest.requestUrl:"/static_1"'
    target_filter += f' AND NOT httpRequest.requestUrl:"/utils"'
    target_filter += f' AND (httpRequest.latency>"1ms" OR jsonPayload.latency>"1ms")'
    target_filter += f' AND timestamp >= "{NOW_DATE}"'

    if SPECIFIC_MODULE:
        target_filter += f' AND resource.labels.module_id:"{SPECIFIC_MODULE}"'

    found_links = {}
    for i, entry in enumerate(client.list_entries(page_size=1000, filter_=target_filter)):
        latency = float(entry.payload['latencySeconds'])
        request_url = entry.http_request['requestUrl']
        request_url = request_url.split('?')[0]

        found_links.setdefault(request_url, []).append(latency)

    print("Finished", project_name, len(found_links))
    return found_links
    # Save on a file the found links



all_hotels = get_all_hotels()
available_projects = set()
for hotel in all_hotels.values():
    rest_url = hotel['url']
    project = rest_url.split('.')[0].split('-dot-')[-1]
    available_projects.add(project)

# filtered_project = list(filter(lambda x: 'secure-booking35' in x, available_projects))

resuls = search_logs_project(TARGET_PROJECT)

for url, latencies in resuls.items():
    print(f'{url} {len(latencies)} {round(sum(latencies) / len(latencies), 2)}')
