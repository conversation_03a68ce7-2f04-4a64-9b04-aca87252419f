import logging
import time
from datetime import datetime, timedelta

import google.cloud.logging
import pandas as pd
import matplotlib.pyplot as plt

############################################################################################################
# TO USE THIS SCRIPT, YOU NEED TO ENABLE AUDIT LOGS OF DATASTORE
############################################################################################################

PROJECT_NAME = "park-royal-hotels"
NOW_DATE = datetime.now() - timedelta(hours=9)
# NOW_DATE = datetime.now() - (timedelta(hours=24) + timedelta(hours=17))
NOW_DATE = NOW_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')
FINISH_DATE = datetime.now() - timedelta(hours=17)
FINISH_DATE = FINISH_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')
# FINISH_DATE = None


# if project not given, it will be inferred from the environment
client = google.cloud.logging.Client(project=PROJECT_NAME, _use_grpc=False)

requests_formatted = []

# Get all logs
target_filter = f'''
resource.type="audited_resource"
'''
# Filter to get only last 3 hours
target_filter += f' AND timestamp >= "{NOW_DATE}"'
if FINISH_DATE:
    target_filter += f' AND timestamp <= "{FINISH_DATE}"'

print(target_filter)
grouped = {}
owner_grouped = {}


for i, entry in enumerate(client.list_entries(page_size=5000, filter_=target_filter)):
    try:
        operation_type = entry.payload['methodName']
    except Exception:
        continue

    try:
        caller = entry.payload['authenticationInfo']['principalEmail']
    except:
        caller = 'unknown'

    if entry.payload.get('metadata') and entry.payload['metadata'].get('keys'):
        datastore_key = entry.payload['metadata']['keys'][0]
        method_type = entry.payload['request']['@type']

        translate = {
            'type.googleapis.com/google.datastore.v1.CommitRequest': 'Write',
            'type.googleapis.com/google.datastore.v1.LookupRequest': 'Read',
            'type.googleapis.com/apphosting.datastore.v3.GetRequest': 'Read',
            'type.googleapis.com/apphosting.datastore.v3.PutRequest': 'Write',
        }

        if not method_type in translate:
            print(method_type)
        else:
            method_type = translate[method_type]

        datastore_key = f'[{method_type}] {caller} {datastore_key}'
        timestamp = entry.timestamp
        grouped[datastore_key] = grouped.get(datastore_key, 0) + 1

    elif 'Datastore' in operation_type and entry.payload['request'].get('query'):
        datastore_key = entry.payload['request']['query']['kind'][0]['name']

        if 'UserSession' in datastore_key:
            pass

        filters_formmated = 'No filters defined '
        if entry.payload['request']['query'].get('filter', {}).get('compositeFilter'):
            filters = entry.payload['request']['query']['filter']['compositeFilter']['filters']
            filters_formmated = ""
            for filter in filters:
                if 'propertyFilter' in filter:
                    property_name = filter['propertyFilter']['property']['name']
                    property_value = list(filter['propertyFilter']['value'].values())[0]
                    filters_formmated += f'{property_name}={property_value} '
                else:
                    for inner_filter in filter['compositeFilter']['filters']:
                        property_name = inner_filter['propertyFilter']['property']['name']
                        property_value = list(inner_filter['propertyFilter']['value'].values())[0]
                        filters_formmated += f'{property_name}={property_value} '

        method_type = entry.payload['methodName']
        translate = {
            'google.datastore.v1.Datastore.RunQuery': 'Read'
        }

        if not method_type in translate:
            print(method_type)
        else:
            method_type = translate[method_type]

        datastore_key = f'[{method_type}] {caller} {datastore_key}  ({filters_formmated})'
        grouped[datastore_key] = grouped.get(datastore_key, 0) + 1



# Show plot of the most accessed keys
df = pd.DataFrame(grouped.items(), columns=['datastore_key', 'count'])
df['datastore_key'] = df['datastore_key'].astype(str)

# Show plot of the most accessed keys
df = df.sort_values('count', ascending=False)
df = df.head(500)
df.plot(kind='bar', x='datastore_key', y='count')
plt.show()

# Save on csv
df.to_csv('top_datastore_keys.csv', index=False)