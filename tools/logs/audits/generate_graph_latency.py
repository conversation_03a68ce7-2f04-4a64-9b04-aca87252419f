import pandas as pd
import matplotlib.pyplot as plt
import json

# Paso 1: Leer los datos
all_data = []
with open('../logs6.json', 'r') as f:
    for line in f:
        all_data.append(json.loads(line))

df = pd.DataFrame(all_data)

# Paso 2: Convertir la columna `latency` a numérico, asumiendo que está en formato de cadena con 's' al final
df['latency'] = df['latency'].str.replace('s', '').astype(float)

# Paso 3: Convertir `day` y `hour` a datetime y combinarlas
df['datetime'] = pd.to_datetime(df['day'] + ' ' + df['hour'] + ':00')

# Paso 4: Agrupar por la nueva columna datetime y calcular la media de las latencias
grouped = df.groupby('datetime')['latency'].mean().reset_index()

# Paso 5: Crear la gráfica
plt.figure(figsize=(15, 7))
plt.plot(grouped['datetime'], grouped['latency'], marker='o', linestyle='-')
plt.xticks(rotation=45)
plt.xlabel('Fecha y Hora')
plt.ylabel('Media de Latencia (ms)')
plt.title('Media de Latencia por Fecha y Hora')
plt.tight_layout()
plt.show()