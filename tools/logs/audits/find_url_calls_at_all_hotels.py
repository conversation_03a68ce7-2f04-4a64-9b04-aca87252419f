from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import google.cloud.logging
import pandas as pd
import matplotlib.pyplot as plt

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

URLS_TO_FIND = [
    '/payment-tpv',
    '/payment-ok',
    '/payment-failure',
    '/payment-responses',
    '/pasarela-tpv',
    '/pasarela-ok',
    '/pasarela-failure',
    '/pasarela-responses'
]

found_links = []

def search_logs_project(project_name):
    print("Searching logs for project", project_name)
    NOW_DATE = datetime.now() - timedelta(days=10)
    NOW_DATE = NOW_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')


    # if project not given, it will be inferred from the environment
    client = google.cloud.logging.Client(project=project_name, _use_grpc=False)

    # Filter to get only last 3 hours
    target_filter = ""
    for i, url in enumerate(URLS_TO_FIND):
        if i == 0:
            target_filter += f'protoPayload.resource:"{url}"'
        else:
            target_filter += f' OR protoPayload.resource:"{url}"'

    target_filter += f' AND timestamp >= "{NOW_DATE}"'

    global found_links
    for i, entry in enumerate(client.list_entries(page_size=1000, filter_=target_filter)):
        target_url = entry.payload['resource']
        found_links.append(target_url + f'   {project_name}')

    # Save on a file the found links



all_hotels = get_all_hotels()
available_projects = set()
for hotel in all_hotels.values():
    rest_url = hotel['url']
    project = rest_url.split('.')[0].split('-dot-')[-1]
    available_projects.add(project)


with ThreadPoolExecutor() as executor:
    executor.map(search_logs_project, available_projects)

with open('found_links2.txt', 'w') as f:
    for link in found_links:
        f.write(f'{link}\n')