from datetime import datetime, timedelta

import google.cloud.logging
import pandas as pd
import matplotlib.pyplot as plt

############################################################################################################
# TO USE THIS SCRIPT, YOU NEED TO ENABLE AUDIT LOGS OF DATASTORE
############################################################################################################



PROJECT_NAME = "secure-booking31"
NOW_DATE = datetime.now() - timedelta(hours=2, minutes=30)
NOW_DATE = NOW_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')


# if project not given, it will be inferred from the environment
client = google.cloud.logging.Client(project=PROJECT_NAME, _use_grpc=False)

requests_formatted = []

# Get all logs
target_filter = f'logName:"cloudaudit.googleapis.com"'
# Filter to get only last 3 hours
target_filter += f' AND timestamp >= "{NOW_DATE}"'

grouped = {}
owner_grouped = {}

for i, entry in enumerate(client.list_entries(page_size=1000, filter_=target_filter)):
    try:
        operation_type = entry.payload['methodName']
    except Exception:
        continue
    caller = entry.payload['authenticationInfo']['principalEmail']
    owner_grouped[caller] = owner_grouped.get(caller, 0) + 1

    if entry.payload.get('metadata') and entry.payload['metadata'].get('keys'):
        datastore_key = entry.payload['metadata']['keys'][0]
        timestamp = entry.timestamp
        grouped[datastore_key] = grouped.get(datastore_key, 0) + 1



# Show plot of the most accessed keys
df = pd.DataFrame(owner_grouped.items(), columns=['caller', 'count'])

# Show plot of the most accessed keys
df = df.sort_values('count', ascending=False)
df = df.head(100)
df.plot(kind='bar', x='caller', y='count')
plt.show()

# Save on csv
df.to_csv('top_users_queries.csv', index=False)