import json
import logging
import time
from datetime import datetime, timedelta

import google.cloud.logging
import pandas as pd
import matplotlib.pyplot as plt

############################################################################################################
# TO USE THIS SCRIPT, YOU NEED TO ENABLE AUDIT LOGS OF DATASTORE
############################################################################################################

PROJECT_NAME = "web-seeker"

FROM_DATE = datetime.strptime('2024-08-05 11:00:00', '%Y-%m-%d %H:%M:%S')
FROM_DATE = FROM_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')

TO_DATE = datetime.strptime('2024-08-05 18:00:00', '%Y-%m-%d %H:%M:%S')
TO_DATE = TO_DATE.strftime('%Y-%m-%dT%H:%M:%SZ')


# if project not given, it will be inferred from the environment
client = google.cloud.logging.Client(project=PROJECT_NAME, _use_grpc=False)

requests_formatted = []

# Get all logs
target_filter = f'''
(httpRequest.latency>"1ms" OR
jsonPayload.latency>"1ms")
resource.type="gae_app"
resource.labels.module_id="default"
'''

# Filter to get only last 3 hours
target_filter += f' AND timestamp >= "{FROM_DATE}"'
if TO_DATE:
    target_filter += f' AND timestamp <= "{TO_DATE}"'

print(target_filter)
grouped = {}
owner_grouped = {}

entries_info = []
for i, entry in enumerate(client.list_entries(page_size=5000, filter_=target_filter)):
    entries_info.append(entry.http_request)


with open('requests.json', 'w') as f:
    f.write(json.dumps(entries_info))


url_counts = {}
for entry in entries_info:
    request_url = entry['requestUrl']

    if request_url in url_counts:
        url_counts[request_url] += 1
    else:
        url_counts[request_url] = 1

print("Building graph")
# Show plot of the most accessed keys
df = pd.DataFrame(list(url_counts.items()), columns=['requestUrl', 'count'])
df = df.sort_values('count', ascending=False)
df = df.head(20)
plt.figure(figsize=(15, 7))
df.plot(kind='bar', x='requestUrl', y='count')
plt.xlabel('Request URL')

# Save on csv
df.to_csv('top_requests.csv', index=False)

plt.show()
