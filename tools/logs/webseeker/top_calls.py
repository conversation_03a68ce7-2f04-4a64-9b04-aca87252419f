import json

import pandas as pd

all_data = open('requests.json', 'r').read()
all_data = json.loads(all_data)

for data in all_data:
    url = data['requestUrl']
    try:
        if 'render' in url and 'web_components' not in url:
            target = url.split('/render')[0]
            target = target.split('builder/')[1]
            data['hotel_code'] = target
        elif 'get_section_from_wysiwyg' in url:
            data['hotel_code'] = 'get_section_from_wysiwyg'
        elif 'static_1' in url:
            data['hotel_code'] = 'static_1'
        elif 'healthcheck' in url:
            data['hotel_code'] = 'healthcheck'
        elif 'clear_cache' in url:
            data['hotel_code'] = url.split('hotel_code=')[1]
        elif '/web_components/render' in url:
            data['hotel_code'] = '/web_components/render'
        elif '/web_components/get_all_fonts/' in url:
            data['hotel_code'] = '/web_components/get_all_fonts/'
        else:
            data['hotel_code'] = url

    except Exception as e:
        print(url)

df = pd.DataFrame(all_data)

df = df.groupby(['hotel_code']).size().reset_index(name='count')
df = df.sort_values(by='count', ascending=False)


df.to_csv('requests.csv', index=False)
print(df)
