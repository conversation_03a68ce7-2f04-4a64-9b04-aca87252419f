import json

import google.cloud.logging
import pandas as pd

start_datetime = '2024-07-01T00:00:00Z'
end_datetime = '2024-07-09T00:00:00Z'


PROJECT_NAME = "ona-hotels"
FILE_NAME = "logs6.json"

# if project not given, it will be inferred from the environment
client = google.cloud.logging.Client(project=PROJECT_NAME, _use_grpc=False)

# Open the file in write mode
with open(FILE_NAME, 'w') as f:
    # Get all logs
    all_data = []
    target_filter = '(httpRequest.latency>"1ms" OR jsonPayload.latency>"1ms") AND "/booking1"'
    target_filter += f' AND timestamp >= "{start_datetime}" AND timestamp < "{end_datetime}"'
    target_filter += f' -"static_1"'

    print(target_filter)

    for i, entry in enumerate(client.list_entries(page_size=1000, filter_=target_filter)):
        # If entry has some latency value, it is a request log
        try:
            data = {
                'ip': entry.http_request['remoteIp'],
                'resource': entry.http_request['requestUrl'].split('?')[0],
                'day': entry.timestamp.strftime('%Y-%m-%d'),
                'hour': entry.timestamp.strftime('%H'),
                'latency': entry.http_request['latency'],
            }

            # Write the entry as a JSON line in the file
            f.write(json.dumps(data) + '\n')
        except:
            print("error")
            pass


# Now, read the file line by line and convert each line into a JSON object
# all_data = []
# with open(FILE_NAME, 'r') as f:
#     for line in f:
#         all_data.append(json.loads(line))
