import json
import random

import requests

from paraty_commons_3.decorators.timerdecorator import timeit

FUERTE_SERVER = 'https://fuerte-adapter.appspot.com'
LOGIN_ENDPOINT = '/login2'

@timeit
def call_login(user, password):

    url = FUERTE_SERVER + LOGIN_ENDPOINT
    body = {'hotel': 'amare-marbella',
            'user': user,
            'password': password}

    response = requests.post(url, data=json.dumps(body))
    print(response.status_code)


def _get_all_user_passwords_from_file():
    import pandas
    data = pandas.read_csv("Users.csv", header=1, sep=';')
    result = []
    for row in data.values:
        result.append((row[0], row[1]))

    return result

MAX_CALLS = 10000


def launch_login_test():
    valid_users = _get_all_user_passwords_from_file()

    for x in range(0, MAX_CALLS):
        index = random.randint(0, len(valid_users))

        current_user = valid_users[index]
        call_login(current_user[0], current_user[1])



if __name__ == '__main__':
    # call_login('<EMAIL>', '58675c5')
    launch_login_test()