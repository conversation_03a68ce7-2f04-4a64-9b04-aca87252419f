import csv

from paraty_commons_3.datastore import datastore_communicator


def get_prebookings(hotel_code, start_date, end_date):
    prebookings = datastore_communicator.get_using_entity_and_params('PreBooking', [ ('creation_timestamp', '>=', start_date), ('creation_timestamp', '<=', end_date)], hotel_code=hotel_code)
    return prebookings


def _create_excel_report(prebookings):
    headers = ['hotel', 'identificador', 'timestamp']

    # Create a new CSV file and write the headers
    with open('hotels.csv', 'w', newline='') as csvfile:
        csv_writer = csv.writer(csvfile)
        csv_writer.writerow(headers)
        for hotel_code, prebookings in prebookings.items():
            for prebooking in prebookings:
                csv_writer.writerow([hotel_code, prebooking.get('booking_identifier', ''), prebooking['creation_timestamp']])

    print("CSV file created with headers: hotel, identificador, timestamp")



if __name__ == '__main__':
    prebookings = {}

    hoteles = ['landmar-gigantes', 'landmar-arena']

    for hotel_code in hoteles:
        prebookings[hotel_code] = get_prebookings(hotel_code, '2021-01-01', '2024-01-31')
        print(len(prebookings[hotel_code]))

    _create_excel_report(prebookings)
