import logging
import os
import re
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels


def _build_url(hotel_code, application_id):
    results = get_using_entity_and_params('ConfigurationProperty', search_params=[('mainKey', '=', 'Dominio asociado')],
                                          hotel_code=hotel_code)
    return results[0]['value']


def open_website_hotel(target):
    link = None

    all_hotels = get_all_hotels()
    if all_hotels.get(target):
        target_url = all_hotels[target]['url']
        project_id = re.search(r"dot-(.*)\.appspot", target_url).group(1)
        link = _build_url(target, project_id)

    else:
        for hotel_code, hotel_data in all_hotels.items():
            if target in hotel_data['applicationId'] or target.lower() in hotel_data['name'].lower():
                target_app = hotel_data['url'].split("-dot-")[1].split(".")[0]
                link = _build_url(hotel_code, target_app)
                break

    if link:
        logging.info(link)
        os.system(f"open '{link}'")

    else:
        # Notify not found hotel on mac popup
        os.system(f"osascript -e 'display notification \"Hotel not found\" with title \"Website Link\"'")


if __name__ == '__main__':
    target_hotel = sys.argv[1]
    open_website_hotel(target_hotel)
