import concurrent
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_reservations(from_date, hotel_code):
    bookings = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('startDate', '>', from_date)], hotel_code=hotel_code)
    return bookings


def get_relevant_bookings(bookings):
    relevant_bookings = []
    for booking in bookings:
        source_not_callcenter = not booking.get('source') or (booking.get('source').lower() != 'callcenter')
        is_callcenter = booking.get('agent')

        if is_callcenter and source_not_callcenter:
            relevant_bookings.append(booking)

    return relevant_bookings

def worker(f, hotel_code):
    bookings = get_reservations('2023', hotel_code)
    relevant_bookings = get_relevant_bookings(bookings)

    for booking in relevant_bookings:
        f.write(f'{hotel_code}, {booking.get('identifier')}\n')


if __name__ == '__main__':

    with open('bookings.csv', 'w') as f:
        f.write('Hotel code, identificador\n')
        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all tasks and get future objects
            all_hotels = get_all_valid_hotels()

            futures = [executor.submit(worker, f, x['applicationId']) for x in all_hotels]

            # Use tqdm to create a progress bar
            for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
                pass  # We don't need to do anything here, tqdm updates automatically


