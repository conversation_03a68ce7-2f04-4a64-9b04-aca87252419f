import multiprocessing

'''
See https://github.com/benoitc/gunicorn/blob/master/examples/example_config.py
See http://docs.gunicorn.org/en/0.17.2/configure.html


'''
# Check VM CPU usage, the number of workers is high because it is only a proxy
# See https://console.cloud.google.com/compute/instancesDetail/zones/us-central1-a/instances/generic-proxy-usa-4?project=build-tools-2&tab=monitoring&pageState=(%22timeRange%22:(%22duration%22:%22P7D%22),%22observabilityTab%22:(%22mainContent%22:%22metrics%22,%22section%22:%22overview%22))
workers = multiprocessing.cpu_count() * 2 + 1

threads = 25

timeout = 60