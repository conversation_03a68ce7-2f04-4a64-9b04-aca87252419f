
DEPRECATED: GO TO proxy-seeker instead.

##Note that if you have permissions issues you might have to get IAM permissions for:
##Artifact Registry Administrator
##Cloud Build Service Account
##See https://stackoverflow.com/questions/********/cant-push-image-to-google-container-registry-caller-does-not-have-permission
#
## If you have problems you can do ssh to the machine and check the logs using the following commands:
## $ docker ps     (This will show you the container id)
## $ docker logs -f containerIdXXXX
## You also need to make sure that the traffic is enabled for http and https in the firewall rules
#
#gcloud builds submit -t  eu.gcr.io/build-tools-2/generic-proxy --project build-tools-2
#
##Use create-with-container if creating a new instance
## Use update-container if updating an existing instance
#gcloud compute instances create-with-container generic-proxy-usa-5 --container-image eu.gcr.io/build-tools-2/generic-proxy --project build-tools-2
