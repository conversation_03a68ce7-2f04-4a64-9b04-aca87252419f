# Use an official Python runtime as a parent image
FROM python:3.9-slim

# Copy the current directory contents into the container at /proxy
COPY . /proxy

# Set the working directory to /proxy
WORKDIR /proxy

## Install any needed packages specified in requirements.txt
RUN pip install --trusted-host pypi.python.org -r requirements.txt

## Run app.py when the container launches
CMD ["gunicorn", "-c", "gunicorn.conf.py", "-b", ":80", "main:app"]
