import logging

from flask import Flask
import requests
from flask import request
from flask import Response
from flask_compress import Compress

compress = Compress()


def create_app():
    app = Flask(__name__)
    return app


app = create_app()
compress.init_app(app)


def _get_target_url():
    return request.headers.get("targetUrl")


def _post_in_target(myBody):
    targetUrl = _get_target_url()

    headers = dict(request.headers)
    if "Host" in headers:
        headers.pop('Host')

    logging.info("------------------------------------")
    logging.info("REQUEST:")
    logging.info("TargetUrl: %s" % targetUrl)
    logging.info("Body: %s" % myBody)
    logging.info("headers:")
    for key in headers:
        logging.info("%s - %s" % (key, headers[key]))
    logging.info("------------------------------------")

    response = requests.post(targetUrl, data=myBody, headers=headers, verify=False, timeout=60)

    logging.info("------------------------------------")
    logging.info("RESPONSE")
    logging.info("Headers: %s" % response.headers)
    logging.info("Body: %s" % response.content)
    logging.info("------------------------------------")
    return response.content, response.status_code, response.headers


def _get_in_target():
    targetUrl = _get_target_url()

    headers = dict(request.headers)
    if "Host" in headers:
        headers.pop('Host')

    logging.info("------------------------------------")
    logging.info("REQUEST:")
    logging.info("TargetUrl: %s" % targetUrl)
    logging.info("headers:")
    for key in headers:
        logging.info("%s - %s" % (key, headers[key]))
    logging.info("------------------------------------")

    response = requests.get(targetUrl, headers=headers, verify=False, timeout=60)

    logging.info("------------------------------------")
    logging.info("RESPONSE")
    logging.info("Headers: %s" % response.headers)
    logging.info("Body: %s" % response.content)
    logging.info("------------------------------------")
    return response.content, response.status_code, response.headers


def _delete_in_target():
    targetUrl = _get_target_url()

    headers = dict(request.headers)
    if "Host" in headers:
        headers.pop('Host')

    logging.info("------------------------------------")
    logging.info("DELETE REQUEST:")
    logging.info("TargetUrl: %s" % targetUrl)
    logging.info("headers:")
    for key in headers:
        logging.info("%s - %s" % (key, headers[key]))
    logging.info("------------------------------------")
    headers.pop("Content-Length", None)

    response = requests.delete(targetUrl, headers=headers, verify=False, timeout=60)

    logging.info("------------------------------------")
    logging.info("RESPONSE")
    logging.info("Headers: %s" % response.headers)
    logging.info("Body: %s" % response.content)
    logging.info("------------------------------------")
    return response.content, response.status_code, response.headers


def _set_response(self):
    self.send_response(200)
    self.send_header('Content-type', 'text/html')
    self.end_headers()


def _clean_header(headers):
    headers.pop("Content-Length", None)
    headers.pop("Content-Encoding", None)
    headers.pop("Transfer-encoding", None)
    return headers


@app.route("/", methods=['GET'])
def do_GET():
    try:
        response_content, status_code, headers = _get_in_target()
        headers = dict(_clean_header(headers))
        response = Response(response=response_content)

        for key in headers:
            if key not in response.headers:
                response.headers[key] = headers[key]

        response.status_code = status_code
        return response

    except Exception as e:
        logging.error("Exception during call: %s" % e)
        return str(e), 500


@app.route("/", methods=['POST'])
def do_POST():
    try:
        response_content, status_code, headers = _post_in_target(request.data)
        response = Response(response=response_content)
        response.headers = dict(_clean_header(headers))
        response.status_code = status_code
        return response
    except Exception as e:
        logging.error("Exception during call: %s" % e)
        return str(e), 500


@app.route("/", methods=['DELETE'])
def do_DELETE():
    try:
        response_content, status_code, headers = _delete_in_target()
        response = Response(response=response_content)
        response.headers = dict(_clean_header(headers))
        response.status_code = status_code
        return response
    except Exception as e:
        logging.error("Exception during call: %s" % e)
        return str(e), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=80, debug=True)
