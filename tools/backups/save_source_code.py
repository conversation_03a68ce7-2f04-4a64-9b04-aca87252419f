import json
import datetime
import os
import subprocess
import requests

from paraty_commons_3 import security_utils
from paraty_commons_3.date_utils import get_timestamp


def print_and_execute_command(cmd):
	print(cmd)
	subprocess.check_call(cmd, shell=True)


def download_projects(projects):

	temp_folder = "/tmp/%s" % get_timestamp(format="%Y_%m_%d")

	os.system("rm -rf %s" % temp_folder)
	os.system("mkdir %s" % temp_folder)

	for project in projects:
		command = 'cd %s;git clone %s' % (temp_folder, project)
		print_and_execute_command(command)

	return temp_folder


def create_zip_file(temp_folder, zipped_filename):

	# Create a zip file with the data

	zip_password = security_utils.get_secret("security-seeker", "backup_source_code_password")

	print('Zipping folder %s to %s' % (temp_folder, zipped_filename))
	command = 'zip -r %s %s -P %s' % (zipped_filename, temp_folder, zip_password)
	print_and_execute_command(command)


def save_source_code(modified_repositories):

	temp_folder = download_projects(modified_repositories)

	zipped_filename = 'sourceCode%s.zip' % get_timestamp(format="%Y-%m-%d")
	zipped_file = "/tmp/%s" % zipped_filename

	create_zip_file(temp_folder, zipped_file)

	# TODO Upload to amazon or other cloud storage


# Authorization is obtained using bacic authentication (user:app_password), the easiest way to obtain it is using postman and it always looks like 'Basic xxxxx', use the user, not the email (i.e. fmatheis).
def get_full_list_of_repositories(updated_in_days):

	next_entry = "https://api.bitbucket.org/2.0/repositories/paraty?page=1"
	result = []

	bitbucket_password = security_utils.get_secret("security-seeker", "bitbucket_for_source_backups")

	while next_entry:
		response = requests.get(next_entry, headers={'Authorization': bitbucket_password})
		page = json.loads(response.content)
		next_entry = page.get('next')
		result.extend(page.get('values'))

	# Filter only those updated in the last month
	if updated_in_days:
		now = datetime.datetime.now()
		recently_updated = []
		for repo in result:
			updated_timestamp = datetime.datetime.strptime(repo['updated_on'], "%Y-%m-%dT%H:%M:%S.%f+00:00")
			days = (now - updated_timestamp).days
			if days < updated_in_days:
				recently_updated.append(repo)
		result = recently_updated

	# 1 for ssh, 0 for html cloning
	url_only = [x['links']['clone'][1]['href'] for x in result]

	for current in url_only:
		print("'" + current + "',")

	return url_only


if __name__ == "__main__":

	all_repos = get_full_list_of_repositories(30)

	# Note that this is not owned by paraty, so we need to add it manually
	all_repos.append("*****************:fmatheis/hotel-manager.git")

	print(len(all_repos))

	save_source_code(all_repos)
