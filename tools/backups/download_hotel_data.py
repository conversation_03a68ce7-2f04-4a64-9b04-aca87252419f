import os
import tempfile
import zipfile
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from google.cloud import storage
from tqdm import tqdm

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

OUTPUT_DIR = "downloaded_hotel_data.zip"
# OUTPUT_DIR = "/Users/<USER>/Projects/build-tools-2/downloaded_hotel_data.zip"

def download_backups():
    storage_client = storage.Client("build-tools-2")

    current_date = datetime.now()
    previous_month_date = (current_date.replace(day=1) - timedelta(days=1)).replace(day=1)

    hotel_blobs = {}

    hotel_codes = _get_all_hotel_codes()

    for hotel_code in hotel_codes:
        current_month_prefix = f"hotels/{hotel_code}/{current_date.strftime('%Y-%m')}"
        previous_month_prefix = f"hotels/{hotel_code}/{previous_month_date.strftime('%Y-%m')}"

        all_blobs = list(storage_client.list_blobs("paraty_backups", prefix=current_month_prefix))

        if len(all_blobs) == 0:
            all_blobs = storage_client.list_blobs("paraty_backups", prefix=previous_month_prefix)

        if all_blobs:
            try:
                all_blobs.sort(key=lambda blob: blob.name, reverse=True)
                most_recent_date = all_blobs[0].name.split('/')[2]
                recent_blobs = [blob for blob in all_blobs if blob.name.split('/')[2] == most_recent_date]

                hotel_blobs[hotel_code] = recent_blobs
            except Exception as e:
                print(f"error in hotel: {hotel_code}")

    with tempfile.TemporaryDirectory() as temp_dir:
        zip_filename = OUTPUT_DIR
        with zipfile.ZipFile(zip_filename, mode='w', compression=zipfile.ZIP_DEFLATED) as zip_file:
            total_blobs = sum(len(blobs) for blobs in hotel_blobs.values())

            with tqdm(total=total_blobs, desc="Downloading and zipping blobs") as pbar:
                with ThreadPoolExecutor(max_workers=10) as executor:
                    future_to_blob = {executor.submit(download_blob_to_file, blob, temp_dir): blob for blobs in hotel_blobs.values() for blob in blobs}
                    for future in as_completed(future_to_blob):
                        blob_name, temp_file_path = future.result()
                        pbar.update(1)

                        if temp_file_path is None:
                            print(f"Error downloading blob: {blob_name}")
                            continue
                        hotel_name = blob_name.split('/')[1]
                        arcname = f"{hotel_name}/{blob_name.split('/')[-1]}"
                        zip_file.write(temp_file_path, arcname)
                        os.remove(temp_file_path)

        print(f"ZIP file created: {os.path.abspath(zip_filename)}")


def download_blob_to_file(blob, temp_dir):
    try:
        temp_file_path = os.path.join(temp_dir, blob.name.replace('/', '_'))
        with open(temp_file_path, 'wb') as temp_file:
            blob.download_to_file(temp_file)
        return blob.name, temp_file_path
    except Exception as e:
        return None, e


def _get_all_hotel_codes():
    return [x['applicationId'] for x in get_all_valid_hotels() if 'applicationId' in x]


if __name__ == '__main__':
    download_backups()
