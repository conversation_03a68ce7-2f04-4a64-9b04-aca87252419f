from concurrent.futures import ThreadPoolExecutor

import requests

from paraty_commons_3.datastore import datastore_communicator

CLUB_USER_MODEL = 'UserClub'

def refresh_loyalty_level_user(hotel_code, user):
    response = requests.get(f"https://loyalty-seeker.appspot.com/users/refresh-user-category/?namespace={hotel_code}&idmember={user['idmember']}")
    return f"User {user['idmember']}. Response: {response.content}"

def refresh_loyalty_level_all_users(hotel_code):
    users = datastore_communicator.get_using_entity_and_params(CLUB_USER_MODEL, search_params=[], hotel_code=hotel_code, return_cursor=True)

    # Futures concurrent 5 tasks
    with ThreadPoolExecutor(max_workers=10) as executor:
        for user in users:
            response = executor.submit(refresh_loyalty_level_user, hotel_code, user)
            print(response.result())


if __name__ == '__main__':
    target_hotel = 'casual-corporativa'
    refresh_loyalty_level_all_users(target_hotel)
