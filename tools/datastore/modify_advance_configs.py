####################
### Script to modify advance configs by match in all hotels
####################
import concurrent
import sys
from concurrent.futures.thread import ThreadPoolExecutor

from tqdm import tqdm

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_all_valid_hotels


def update_config_of_all_hotels(config_name, config_value, config_new_value, hotel_code):
    try:
        actual_advance_configs = get_using_entity_and_params(
            'ConfigurationProperty',
            [('mainKey', '=', config_name)],
            hotel_code=hotel_code
        )
    except Exception as e:
        print(f'Error getting advance config for hotel {hotel_code}: {e}')
        actual_advance_configs = []

    if actual_advance_configs and actual_advance_configs[0]['value'] == config_value:
        target_element = actual_advance_configs[0]
        target_element['value'] = config_new_value
        save_entity(target_element, hotel_code)



if __name__ == '__main__':
    config_name = sys.argv[1]
    config_value = sys.argv[2]
    config_new_value = sys.argv[3]

    if all([config_name, config_value, config_new_value]):
        hotels = get_all_hotels()
        threads = []

        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all tasks and get future objects
            all_hotels = get_all_hotels()
            all_hotels = [x for x in all_hotels]
            futures = [executor.submit(update_config_of_all_hotels, config_name, config_value, config_new_value, x) for x in all_hotels]
            # Use tqdm to create a progress bar
            for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
                pass  # We don't need to do anything here, tqdm updates automatically



    else:
        raise ValueError('You must provide the config name, the config value and the new config value')