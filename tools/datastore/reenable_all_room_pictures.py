from concurrent.futures.thread import ThreadPoolExecutor

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_entity
from paraty_commons_3.datastore.datastore_utils import entity_id_to_alphanumeric_key, id_to_entity_key
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_room_pictures_hotel(hotel_code):
    all_rooms = get_using_entity_and_params('RoomType', hotel_code=hotel_code)
    all_pictures = get_using_entity_and_params('Picture', hotel_code=hotel_code, return_cursor=True)

    pictures_by_room = {}
    for picture in all_pictures:
        parent_key = picture['mainKey']
        pictures_by_room.setdefault(parent_key, [])
        pictures_by_room[parent_key].append(picture)

    found_pictures = []
    for room in all_rooms:
        room_key = id_to_entity_key(hotel_code, room.key)
        room_pictures = pictures_by_room.get(room_key)
        if room_pictures:
            found_pictures.extend(room_pictures)

    return found_pictures


def enable_and_save_room_pictures_hotel(pictures_to_enable, hotel_code):
    for picture in pictures_to_enable:
        picture['enabled'] = True
        save_entity(picture, hotel_code)


def reenable_room_pictures_hotel(hotel_code):
    target_pictures = get_room_pictures_hotel(hotel_code)
    enable_and_save_room_pictures_hotel(target_pictures, hotel_code)


if __name__ == '__main__':
    all_enabled_hotels = get_all_valid_hotels()
    all_applications = [hotel['applicationId'] for hotel in all_enabled_hotels]

    total_hotels = len(all_applications)
    print("Hotels to update: ", total_hotels)

    # reenable_room_pictures_hotel('hotel-zen')

    with ThreadPoolExecutor(max_workers=20) as executor:
        for _ in executor.map(reenable_room_pictures_hotel, all_applications):
            total_hotels -= 1
            print("Hotels left: ", total_hotels)
