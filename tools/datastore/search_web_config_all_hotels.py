from concurrent.futures.thread import ThreadPoolExecutor
from functools import partial

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

FOUND_CONFIGS = []

def search_config(config_name, hotel_code):
    try:
        actual_advance_configs = get_using_entity_and_params(
            'ConfigurationProperty',
            [('mainKey', '=', config_name)],
            hotel_code=hotel_code
        )
    except Exception as e:
        actual_advance_configs = []

    if actual_advance_configs:
        FOUND_CONFIGS.append((hotel_code, actual_advance_configs[0]["value"]))


config_name = 'Alternative search params'  # Define the config_name you want to search for

with ThreadPoolExecutor(max_workers=100) as executor:
    all_hotels = get_all_valid_hotels()
    all_hotels = [x['applicationId'] for x in all_hotels]
    search_config_partial = partial(search_config, config_name)
    executor.map(search_config_partial, all_hotels)

for hotel_code, config_value in FOUND_CONFIGS:
    print(f'Hotel code: {hotel_code}, Config value: {config_value}')