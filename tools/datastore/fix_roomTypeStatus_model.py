################
### <PERSON>ript to fix datetime fields in RoomTypeStatus model
### Will check the fields: timestampUpdate, timestampCreation
################


from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_entity_multi
from datetime import datetime

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels


def fix_datetime_roomTypeStsatus(hotel_code):
    elements_to_update = []
    all_room_type_status = get_using_entity_and_params('RoomTypeStatus', hotel_code=hotel_code, return_cursor=True)
    for room_type_status in all_room_type_status:
        updated = False
        fields_to_check = ['timestampUpdate', 'timestampCreation']

        for field_name in fields_to_check:
            if room_type_status.get(field_name) and type(room_type_status.get(field_name)) == str:
                target_datetime = datetime.strptime(room_type_status[field_name][:26], '%Y-%m-%dT%H:%M:%S.%f')
                room_type_status[field_name] = target_datetime
                updated = True

        if updated:
            elements_to_update.append(room_type_status)

    if elements_to_update:
        print(hotel_code)
        print(f"Updating {len(elements_to_update)} elements")
        for element in elements_to_update:
            print(element.id)
        save_entity_multi(elements_to_update, hotel_code=hotel_code)
        print()
        print()
        print()


if __name__ == '__main__':
    target_hotel_code = input("Enter the hotel code: ")
    all_hotels = get_all_hotels()
    target_hotels = [hotel for hotel in all_hotels if target_hotel_code in hotel]

    if not target_hotel_code or not target_hotels:
        print("No hotel code found")
        exit(1)

    if len(target_hotels) > 1:
        # Request user to confirm if its ok, print the hotels
        print("Multiple hotels found")
        for hotel in target_hotels:
            print(hotel)
        print()
        confirm = input("Do you want to continue? (y/n): ")
        if confirm.lower() != 'y':
            print("Exiting")
            exit(1)

    for target_hotel_code in target_hotels:
        fix_datetime_roomTypeStsatus(target_hotel_code)
