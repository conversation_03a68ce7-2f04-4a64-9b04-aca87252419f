####################
### Script to modify advance configs by match in all hotels
####################
import concurrent
import sys
import threading
from concurrent.futures.thread import ThreadPoolExecutor

import requests
from flask import request
from tqdm import tqdm

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_all_valid_hotels


def flush_hotel_with_config(config_name, hotel_info):
    try:
        actual_advance_configs = get_using_entity_and_params(
            'ConfigurationProperty',
            [('mainKey', '=', config_name)],
            hotel_code=hotel_info['applicationId']
        )
    except Exception as e:
        actual_advance_configs = []

    if actual_advance_configs:
        rest_url = hotel_info.get('url', '')
        application_name = rest_url.split("-dot-")[-1].split(".appspot.com")[0]
        hotel_code = hotel_info['applicationId']
        target_url = f'https://{hotel_code}-dot-{application_name}.appspot.com'
        target_url += f'/flush_entity?entity=ConfigurationProperty&namespace={hotel_code}'
        requests.get(target_url)



if __name__ == '__main__':
    config_name = sys.argv[1]

    if config_name:
        hotels = get_all_hotels()
        threads = []

        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all tasks and get future objects
            all_hotels = get_all_hotels()
            futures = [executor.submit(flush_hotel_with_config, config_name, all_hotels[x]) for x in all_hotels]
            # Use tqdm to create a progress bar
            for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
                pass  # We don't need to do anything here, tqdm updates automatically



    else:
        raise ValueError('You must provide the config name, the config value and the new config value')