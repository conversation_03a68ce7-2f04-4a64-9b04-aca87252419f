import json

import requests

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_utils

HOTEL_CODE = 'best-indalo'
INTEGRATION = 'dingus'
HUMAN_READABLE = True


'''
HOW TO USE THIS SCRIPT:

1. Go to the Audit and obtain the XML you want to try
2. Copy the XML to a file called test_request.xml
3. Change the HOTEL_CODE and INTEGRATION variables to match the ones you want to use
4. Run this script

'''


def _get_availability_items():
    # Read content of test_request.xml
    with open('test_request.xml') as f:
        xml_to_analyze = f.read()

    params = {
        'hotel_code': HOTEL_CODE,
        'xml_files': [('test', xml_to_analyze)]
    }
    endpoint = f'https://{INTEGRATION}-adapter.appspot.com/get_availability_items'
    headers = {'Content-Type': 'application/json'}
    response = requests.post(endpoint, json=params, headers=headers)

    availability_items = json.loads(response.content)[0][1]

    if HUMAN_READABLE:
        _convert_to_human_names({'applicationId': HOTEL_CODE}, availability_items, 'es')

    availability_items = list(filter(lambda x: x['quantity_rate_board'], availability_items))

    print(json.dumps(availability_items, indent=4))


def _convert_to_human_names(hotel, availability_items, language):
    all_rates = {datastore_utils.alphanumeric_to_id(x['key']): x for x in common_data_provider.get_rates_of_hotel(hotel, language=language, include_removed=True)}
    all_boards = {datastore_utils.alphanumeric_to_id(x['key']): x for x in common_data_provider.get_boards_of_hotel(hotel, language=language, include_removed=True)}
    all_rooms = {datastore_utils.alphanumeric_to_id(x['key']): x for x in common_data_provider.get_rooms_of_hotel(hotel, language=language, include_removed=True)}

    for item in availability_items:

        room_name = ''
        if item.get('roomId', 0) is not None:
            room_name = all_rooms.get(int(item.get('roomId', 0)), {}).get('name', '').lower()

        rate_name = ''
        if item.get('rateId', 0) is not None:
            rate_name = all_rates.get(int(item.get('rateId', 0)), {}).get('localName', '').lower()

        board_name = ''
        if item.get('boardId', 0) is not None:
            board_name = all_boards.get(int(item.get('boardId', 0)), {}).get('name', '').lower()

        if isinstance(room_name, str):
            room_name = room_name

        if isinstance(rate_name, str):
            rate_name = rate_name

        if isinstance(board_name, str):
            board_name = board_name

        item['room_name'] = room_name
        item['rate_name'] = rate_name
        item['board_name'] = board_name


if __name__ == '__main__':
    _get_availability_items()
