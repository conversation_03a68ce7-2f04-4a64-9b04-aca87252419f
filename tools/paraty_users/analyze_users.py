from paraty_commons_3.datastore import datastore_communicator


def _filter_call_center(user):
    current_configuration = user.get('permission', '')
    if 'admin' in current_configuration:
        return True
    return False


def get_users(filter_to_use):

    result = datastore_communicator.get_using_entity_and_params("ParatyUser", hotel_code='admin-hotel')
    return list(filter(filter_to_use, result))


if __name__ == '__main__':
    result = get_users(_filter_call_center)
    print([x['name'] for x in result])
