import concurrent
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_reservations(from_date, hotel_code):
    bookings = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('timestamp', '>', from_date)], hotel_code=hotel_code)
    return bookings

def get_relevant_bookings(bookings):
    relevant_bookings = []
    for booking in bookings:
        source_not_callcenter = True  # not booking.get('source') or (booking.get('source').lower() != 'callcenter')
        is_callcenter = booking.get('agent')

        if is_callcenter and source_not_callcenter:
            relevant_bookings.append(booking)

    return relevant_bookings

def worker(hotel_code):
    bookings = get_reservations('2024-07-23', hotel_code)

    results = {}

    for booking in bookings:

        if booking.get('cancelled'):
            continue

        if not booking['email'] in results:
            results[booking['email']] = []

        results[booking['email']].append(booking)

    has_duplicated = [k for k, v in results.items() if len(v) > 1]

    if has_duplicated:

        print()

        with open(f'./duplicated/{hotel_code}.csv', 'w') as f:
            for k, v in results.items():

                if len(v) > 1:
                    print("===============")
                    f.write(f'{hotel_code}, {k}, {v[0].get('telephone')} {[x.get('identifier') + " - "+ x.get('startDate') for x in v]}\n')
                    for booking in v:
                        payed = '"payed": 0' not in booking.get('extraInfo')
                        nacho = 'created_by' in booking.get('extraInfo')
                        print(f'{hotel_code}, {k}, {booking.get("identifier")}, {payed}, {booking.get("timestamp")}, {booking.get('rate')}, {'nacho' if nacho else 'OK'}')


if __name__ == '__main__':

    with ThreadPoolExecutor(max_workers=5) as executor:
        # Submit all tasks and get future objects
        all_hotels = get_all_valid_hotels()

        all_hotels = [x for x in all_hotels if 'dreamland-' in x['applicationId']]

        for hotel in all_hotels:
            worker(hotel['applicationId'])

