'''
This script adds missing identifiers to the bookings that are using SiteMinder integration
We need to make sure all bookings appear in the Audit or we might run into duplicated identifiers which siteminder will not accept
'''

import os
import json

import flask
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_all_hotels
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm


def check_hotel_siteminder(hotel_code):
    """
    Check if a hotel uses SiteMinder integration
    """
    try:
        hotel = get_hotel_by_application_id(hotel_code)
        if hotel:
            with flask.Flask(__name__).test_request_context():
                siteminder_config = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', search_params=[('name', '=', 'siteminder')], keys_only=True, hotel_code=hotel['applicationId'])
                if siteminder_config:
                    return hotel_code
        return None
    except Exception as e:
        return None

def add_missing_identifiers_in_siteminder(hotel_code):

    try:
        all_bookings = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[], projections="identifier", hotel_code=hotel_code)

        audits_to_create = []
        for current_booking in tqdm(all_bookings):
            audit = datastore_communicator.get_using_entity_and_params('EndpointCallAuditEvent', search_params=[('request_id', '=', current_booking['identifier'])], keys_only=True, hotel_code="siteminder-adapter:")
            if not audit:

                booking_request_log = datastore_communicator.get_using_entity_and_params('BookingRequestLog', search_params=[('identifier', '=', current_booking['identifier'])], keys_only=True, hotel_code="admin-hotel:")
                if not booking_request_log:
                    audits_to_create.append({"identifier": current_booking['identifier'], "hotelCode": hotel_code})

        print(f"Processing hotel: {hotel_code}")
        print("Identifiers to create:", len(audits_to_create))

        # Save to datastore
        keys = [None for x in audits_to_create]
        datastore_communicator.save_multiple_entities('BookingRequestLog', keys, audits_to_create, hotel_code="admin-hotel:")
    except Exception as e:
        print(e)



def get_siteminder_hotels(force_recalculate=False, cache_file='siteminder_hotels.json'):

    # Check if cache file exists and use it if not forcing recalculation
    if os.path.exists(cache_file) and not force_recalculate:
        try:
            with open(cache_file, 'r') as f:
                siteminder_hotels = set(json.load(f))
                print(f"Loaded {len(siteminder_hotels)} SiteMinder hotels from cache file: {cache_file}")
                return siteminder_hotels
        except Exception as e:
            print(f"Error loading cache file: {e}")
            print("Proceeding with recalculation...")
    
    siteminder_hotels = set()
    unique_hotel_codes = [x for x in get_all_hotels().keys()]
    total_hotels = len(unique_hotel_codes)
    
    print(f"Processing {total_hotels} unique hotels to check for SiteMinder integration...")
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(tqdm(
            executor.map(check_hotel_siteminder, unique_hotel_codes),
            total=total_hotels,
            desc="Checking hotels",
            unit="hotel"
        ))
        
        # Add valid results (non-None values) to the set
        for result in results:
            if result:
                siteminder_hotels.add(result)
    
    print(f"Found {len(siteminder_hotels)} hotels with SiteMinder integration")
    
    # Save results to cache file
    try:
        with open(cache_file, 'w') as f:
            json.dump(list(siteminder_hotels), f)
            print(f"Saved SiteMinder hotels to cache file: {cache_file}")
    except Exception as e:
        print(f"Error saving to cache file: {e}")
    
    return siteminder_hotels



def main():
    """Main function to execute the script"""
    # Get SiteMinder hotels
    siteminder_hotels = get_siteminder_hotels(force_recalculate=False, cache_file="siteminder_hotels.csv")

    siteminder_hotels = ['casual-olas-sansebastian']

    # Process hotels in parallel
    print(f"Processing {len(siteminder_hotels)} SiteMinder hotels to add missing identifiers...")
    
    with ThreadPoolExecutor(max_workers=20) as executor:
        list(tqdm(
            executor.map(add_missing_identifiers_in_siteminder, siteminder_hotels),
            total=len(siteminder_hotels),
            desc="Processing hotels",
            unit="hotel"
        ))
    
    print("All hotels processed successfully.")



if __name__ == "__main__":

    # add_missing_identifiers_in_siteminder("port-elche")
    main()


