import concurrent
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_reservations(from_date, hotel_code):
    bookings = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('timestamp', '>', from_date)], hotel_code=hotel_code)
    return bookings


def _check_booking(reservation, hotel_code):
    is_call_center = reservation.get('agent', False)
    if is_call_center:
        if not reservation.get('source', '') or reservation['source'] != 'Callcenter':
            reservation['source'] = 'Callcenter'
            datastore_communicator.save_entity(reservation, hotel_code=hotel_code)
            print(f'Booking {reservation.get("identifier")} at {hotel_code} is callcenter')


def worker(hotel_code):
    bookings = get_reservations('2024-07-09', hotel_code)
    for booking in bookings:
        _check_booking(booking, hotel_code)


if __name__ == '__main__':

    all_hotels = get_all_valid_hotels()

    for hotel in all_hotels:
        if not 'url' in hotel:
            print(hotel)

    with ThreadPoolExecutor(max_workers=20) as executor:
        # Submit all tasks and get future objects
        all_hotels = get_all_valid_hotels()

        all_hotels = [x for x in all_hotels]

        futures = [executor.submit(worker, x['applicationId']) for x in all_hotels]

        # Use tqdm to create a progress bar
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(all_hotels)):
            pass  # We don't need to do anything here, tqdm updates automatically
