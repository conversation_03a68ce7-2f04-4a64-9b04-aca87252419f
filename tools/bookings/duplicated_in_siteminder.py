import os
import sys
import pandas as pd

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

'''

El fichero de duplicados se genera con la siguiente query en Bigquery en el proyecto analysis-seeker:

WITH QualifyingIdentifiers AS (
    -- Step 1: Identify identifiers that are duplicated across different hotels (globally)
    -- AND have at least one booking with a startDate after '2025-05-08'
    SELECT
        identifier        
    FROM
        `analysis-seeker.bi_dataset.RESERVATIONS`
    GROUP BY
        identifier
    HAVING
        COUNT(DISTINCT hotel_code) > 1 -- Ensures the identifier is present in more than one hotel
        AND MAX(CASE WHEN startDate > '2025-05-08' THEN 1 ELSE 0 END) = 1 -- Ensures at least one booking for this identifier is after '2025-05-08'
)
-- Step 2: For these qualifying identifiers, select their identifier and
-- the hotel_code from bookings that actually have startDate > '2025-05-08'
SELECT DISTINCT
    r.identifier,
    r.hotel_code,
    r.timestamp,
    r.startDate
FROM
    `analysis-seeker.bi_dataset.RESERVATIONS` r
INNER JOIN
    QualifyingIdentifiers qi ON r.identifier = qi.identifier
ORDER BY
    r.identifier, r.hotel_code; -- Optional: for ordered results    
'''


def get_siteminder_hotels(df):
    """
    Get hotel codes from the dataframe that use SiteMinder integration
    """
    siteminder_hotels = set()
    unique_hotel_codes = df['hotel_code'].unique()
    
    for hotel_code in unique_hotel_codes:
        hotel = get_hotel_by_application_id(hotel_code)
        if hotel:
            siteminder_config = get_integration_configuration_of_hotel(hotel, 'siteminder')
            if siteminder_config:
                siteminder_hotels.add(hotel_code)
    
    print(siteminder_hotels)
    
    return siteminder_hotels

def main():
    # Read the duplicated bookings
    df = pd.read_csv('tools/bookings/duplicated.csv')
    
    # Get hotels using SiteMinder from the bookings
    siteminder_hotels = get_siteminder_hotels(df)
    
    # Filter bookings for SiteMinder hotels
    siteminder_bookings = df[df['hotel_code'].isin(siteminder_hotels)]
    
    # Save to new CSV file
    siteminder_bookings.to_csv('tools/bookings/duplicated_siteminder.csv', index=False)
    
    print(f"Found {len(siteminder_bookings)} bookings for {len(siteminder_hotels)} SiteMinder hotels")

if __name__ == "__main__":
    main()
