import json

from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def get_casual_bookings():

    all_valid_hotels = [x for x in get_all_valid_hotels() if 'casual' in x['applicationId']]

    min_date_time = '2023-01-01T00:00:00'
    max_date_time = '2023-12-31T23:59:59'

    results = ['Hotel code, identificador, precio, nuevo total, cancelacion con gastos']

    for hotel in all_valid_hotels:
        hotel_code = hotel['applicationId']
        print(f'Hotel code: {hotel_code}')
        reservations = get_reservations_of_hotel(hotel, min_date_time, max_date_time)

        for reservation in reservations:

            if not reservation.get('cancelled'):
                continue

            if 'paraty' in reservation.get('email'):
                continue

            ammended_price = reservation.get('amendedPrice') or ''

            results.append(f'{hotel_code}, {reservation.get("identifier")}, {reservation.get('price')}, {ammended_price}, {reservation.get("cancellationWithExpenses", False)}')

    # Save results to a csv file
    with open('casual_bookings.csv', 'w') as f:
        for line in results:
            f.write(line + '\n')



def get_reservations(hotel_code):
    return [x['identifier'] for x in datastore_communicator.get_using_entity_and_params('Reservation',  projections=['identifier'], hotel_code=hotel_code)]




if __name__ == '__main__':
    # print(get_reservations('hotel-puentereal'))

    get_casual_bookings()