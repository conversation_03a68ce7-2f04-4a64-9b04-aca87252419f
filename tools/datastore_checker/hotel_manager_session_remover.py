import datetime

from paraty_commons_3.datastore import datastore_communicator


ENTITY = 'AdminUserSession'

def remove_sessions_older_than_specific_date():

    all_user_entries_to_delete = datastore_communicator.get_using_entity_and_params(ENTITY, [], keys_only=False, return_cursor=True, hotel_code="admin-hotel")

    user_entries_to_delete = []

    min_valid_timestamp = datetime.datetime.now(datetime.timezone.utc).timestamp() * 1000

    current_timestamp = int(min_valid_timestamp)

    total = 0
    for current_user_entry in all_user_entries_to_delete:

        num_days_ago = (current_timestamp - current_user_entry['timestamp']) / 86400000

        if num_days_ago < 2:
            print(f"Keeping session {current_user_entry['userName']}: {num_days_ago} days ago")
            continue

        print(f"Deleting session {current_user_entry['userName']} {num_days_ago} days ago")

        user_entries_to_delete.append(current_user_entry.key)
        if user_entries_to_delete and len(user_entries_to_delete) == 500:
            datastore_communicator.delete_entity_multi(user_entries_to_delete, "admin-hotel")
            user_entries_to_delete = []
            print(f"Deleted 500 '{ENTITY}'")
            total += 500

        # if total > 100000:
        #     return

    if user_entries_to_delete:
        datastore_communicator.delete_entity_multi(user_entries_to_delete, "admin-hotel")
        print(f"Deleted %s {ENTITY}" % len(user_entries_to_delete))

    print("Finished cleaning cache")

if __name__ == '__main__':
    remove_sessions_older_than_specific_date()
