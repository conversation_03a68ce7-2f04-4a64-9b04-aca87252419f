from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.redis.redis_communicator import build_redis_client


def clean_cache():

    ENTITY_NAME = 'EntryValidTimeStamp2'
    PROJECT_AND_NAMESPACE = 'admin-hotel3:'

    all_sessions_to_delete = datastore_communicator.get_using_entity_and_params(ENTITY_NAME, keys_only=True, return_cursor=True, hotel_code=PROJECT_AND_NAMESPACE)

    sessions_to_delete = []
    for session in all_sessions_to_delete:

        # TODO, CHANGE Here in case you want to filter something
        if not str(session.key.name).startswith('compressed_'):
            continue

        sessions_to_delete.append(session.key)
        if sessions_to_delete and len(sessions_to_delete) == 500:
            datastore_communicator.delete_entity_multi(sessions_to_delete, PROJECT_AND_NAMESPACE)
            sessions_to_delete = []
            print(f"Deleted 500 {ENTITY_NAME}")

    if sessions_to_delete:
        datastore_communicator.delete_entity_multi(sessions_to_delete, PROJECT_AND_NAMESPACE)
        print(f"Deleted %s {ENTITY_NAME}" % len(sessions_to_delete))

    print("Finished cleaning cache")

def full_cache_clean(hotel_code):

    hotel_application = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
    hotel_id = hotel_application.get('id')
    TIMESTAMP_ENTITY = 'EntryValidTimeStamp2'
    PROJECT_AND_NAMESPACE = 'admin-hotel3:'

    REDIS_EUROPE = '***********'
    REDIS_US = '************'

    REDIS_HOST = REDIS_US

    redis_client = build_redis_client(host=REDIS_HOST, password='nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ', port=6666)

    all_timestamp_to_delete = datastore_communicator.get_using_entity_and_params(TIMESTAMP_ENTITY, search_params=[('applicationId', '=', hotel_id)], hotel_code=PROJECT_AND_NAMESPACE)
    for entry in all_timestamp_to_delete:

        entry_key = entry.key.id_or_name

        redis_client.delete(entry_key)

        datastore_communicator.delete_entity(TIMESTAMP_ENTITY, entry.key.id_or_name, PROJECT_AND_NAMESPACE)
        print(f"Deleted {TIMESTAMP_ENTITY} {entry.key.id_or_name}")

    CACHE_ENTITY = 'DatastoreCachedEntry3'
    all_cache_to_delete = datastore_communicator.get_using_entity_and_params(CACHE_ENTITY, search_params=[('applicationId', '=', hotel_id)], hotel_code=PROJECT_AND_NAMESPACE)
    for entry in all_cache_to_delete:
        datastore_communicator.delete_entity(CACHE_ENTITY, entry.key.id_or_name, PROJECT_AND_NAMESPACE)
        print(f"Deleted {CACHE_ENTITY} {entry.key.id_or_name}")


if __name__ == '__main__':
    full_cache_clean('ona-rosas')
    # clean_cache()