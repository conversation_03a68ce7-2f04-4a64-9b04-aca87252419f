import json
from datetime import datetime
from paraty_commons_3.datastore import datastore_communicator


def _clean_pms_result():

    project_and_namespace = 'omnibeespull-adapter:'
    entity = 'PmsResponses'
    min_valid_date = '2024-03-01'

    query = [('timestamp', '<', min_valid_date)]

    all_user_entries_to_delete = datastore_communicator.get_using_entity_and_params(entity, query, keys_only=False, return_cursor=True, hotel_code=project_and_namespace)

    user_entries_to_delete = []

    total = 0
    for current_entity in all_user_entries_to_delete:

        try:
            booking_date = current_entity.get('pms_response').get('HotelStaysType').get('HotelStays')[-1].get('Price').get('End')
        except Exception as e:
            # Delete it because it has errors
            pass

        if booking_date > '2024-03-25':
            continue

        user_entries_to_delete.append(current_entity.key)
        if user_entries_to_delete and len(user_entries_to_delete) == 100:
            datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
            user_entries_to_delete = []
            print(f"Deleted 100 '{entity} for {project_and_namespace}'")
            total += 100

    if user_entries_to_delete:
        datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
        print(f"Deleted %s '{entity}'" % len(user_entries_to_delete))


def clean_cache(project_and_namespace, entity, query):

    all_user_entries_to_delete = datastore_communicator.get_using_entity_and_params(entity, query, keys_only=True, return_cursor=True, hotel_code=project_and_namespace)

    user_entries_to_delete = []

    total = 0
    for current_user_entry in all_user_entries_to_delete:

        user_entries_to_delete.append(current_user_entry.key)
        if user_entries_to_delete and len(user_entries_to_delete) == 500:
            datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
            user_entries_to_delete = []
            print(f"Deleted 500 '{entity} for {project_and_namespace}'")
            total += 500

    if user_entries_to_delete:
        datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
        print(f"Deleted %s '{entity}'" % len(user_entries_to_delete))

    print("Finished cleaning cache")

if __name__ == '__main__':

    min_date = datetime(2023, 10, 1)
    query = [('timestamp', '<', min_date)]

    _clean_pms_result()
    # clean_cache('hotelads-adapter:', 'EndpointCallAuditEvent', query)
    # clean_cache('siteminder-adapter:', 'EndpointCallAuditEvent', query)