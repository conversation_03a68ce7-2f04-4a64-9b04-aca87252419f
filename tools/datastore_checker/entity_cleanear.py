from paraty_commons_3.datastore import datastore_communicator


def clean_cache(entity_name):

    ENTITY_NAME = entity_name
    PROJECT_AND_NAMESPACE = 'admin-hotel3:'

    # all_sessions_to_delete = datastore_communicator.get_using_entity_and_params(ENTITY_NAME, [('timestamp', '<', '2021')], keys_only=True, return_cursor=True, hotel_code=PROJECT_AND_NAMESPACE)
    all_sessions_to_delete = datastore_communicator.get_using_entity_and_params(ENTITY_NAME, [], keys_only=True, return_cursor=True, hotel_code=PROJECT_AND_NAMESPACE)

    sessions_to_delete = []
    for session in all_sessions_to_delete:

        sessions_to_delete.append(session.key)
        if sessions_to_delete and len(sessions_to_delete) == 500:
            datastore_communicator.delete_entity_multi(sessions_to_delete, PROJECT_AND_NAMESPACE)
            sessions_to_delete = []
            print(f"Deleted 500 {ENTITY_NAME}")

    if sessions_to_delete:
        datastore_communicator.delete_entity_multi(sessions_to_delete, PROJECT_AND_NAMESPACE)
        print(f"Deleted %s {ENTITY_NAME}" % len(sessions_to_delete))

    print("Finished cleaning cache")

if __name__ == '__main__':
    # clean_cache('UsageLogEntry')
    clean_cache('DatastoreCachedEntry3')