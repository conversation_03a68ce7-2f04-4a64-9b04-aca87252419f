import random
import datetime

from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import get_project_and_namespace, get_project_and_namespace_from_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


ENTITY = 'UserSession'

def clean_cache(project_and_namespace):

    specific_date = datetime.datetime(2024, 1, 31, tzinfo=datetime.timezone.utc)

    all_user_entries_to_delete = datastore_communicator.get_using_entity_and_params(ENTITY, [], keys_only=False, return_cursor=True, hotel_code=project_and_namespace)

    user_entries_to_delete = []

    total = 0
    for current_user_entry in all_user_entries_to_delete:

        if current_user_entry['timestamp'] > specific_date:
            continue

        user_entries_to_delete.append(current_user_entry.key)
        if user_entries_to_delete and len(user_entries_to_delete) == 500:
            datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
            user_entries_to_delete = []
            print(f"Deleted 500 '{ENTITY} for {project_and_namespace}'")
            total += 500

        # if total > 100000:
        #     return

    if user_entries_to_delete:
        datastore_communicator.delete_entity_multi(user_entries_to_delete, project_and_namespace)
        print(f"Deleted %s {ENTITY}" % len(user_entries_to_delete))

    print("Finished cleaning cache")

if __name__ == '__main__':

    all_valid_hotels = get_all_valid_hotels()
    # all_valid_hotels = [x for x in all_valid_hotels if 'best-' in x['applicationId']]


    hotels_to_clean = []
    for current_hotel in all_valid_hotels:

        project, namespace = get_project_and_namespace_from_hotel(current_hotel)

        hotels_to_clean.append((f'{project}:{namespace}',))

    execute_in_parallel(clean_cache, hotels_to_clean, max_concurrency=30)

    # clean_cache('hotel-puentereal:')