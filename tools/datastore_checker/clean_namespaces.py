import logging
import random

from google.cloud import datastore

from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils

# See https://cloud.google.com/datastore/docs/concepts/metadataqueries

ENTITIES_TO_REMOVE = ['CacheTimestamp', 'StrongCacheEntry', 'UserEntry', '_ah_SESSION', 'CachedAvailabilityPeriod']

'''
Note that the datastore stats are updated every 24-48 hours, and usually around 10:50 GMT+1.

Based on the logs it seems that even if you clean it, there are some calls such as /transfer-bank (and probably others) that add Entities while checking each namespace

So the recommendation is to execute this script around 10:00 in the morning until everything is clean.

'''

def _get_all_hotel_projects():
    all_hotels = hotel_manager_utils.get_all_hotels()

    result = set()

    for hotel in all_hotels.values():
        project, namespace = datastore_utils.get_project_and_namespace_from_hotel(hotel)
        if project:  # and 'ona-' in project:
            result.add(project)

    return result


def _get_all_kinds(project_id, namespace):

    client = datastore.Client(project=project_id, namespace=namespace)
    query = client.query(kind="__kind__")
    query.keys_only()

    kinds = [entity.key.id_or_name for entity in query.fetch() if not entity.key.id_or_name.startswith("__")]
    return kinds

def _namespace_needs_to_be_cleaned(project_id, namespace):

    print("Checking namespace %s at projectId: %s" % (namespace, project_id))

    kinds = _get_all_kinds(project_id, namespace)

    if len(kinds) < 5 and len(kinds) > 0:
        print('Kinds: %s' % kinds)
        return True
    else:
        print('Found Kinds: %s' % len(kinds))
        return False

def chunks(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]

def _delete_all_in_namespace(project_id, namespace, entity_name):

    client = datastore.Client(project=project_id, namespace=namespace)

    query = client.query(kind=entity_name)
    query.keys_only()
    keys = [x.key for x in query.fetch()]
    my_chunks = chunks(keys, 100)
    for chunk in my_chunks:
        client.delete_multi(chunk)
    print('Deleted %s entities of kind %s' % (len(keys), entity_name))

def _clean_namespace(project_id, namespace):

    print("Cleaning namespace %s at projectId: %s" % (namespace, project_id))

    kinds = _get_all_kinds(project_id, namespace)

    print('Kinds: %s' % kinds)

    for entity_name in ENTITIES_TO_REMOVE:
        _delete_all_in_namespace(project_id, namespace, entity_name)


def _get_all_namespaces(project_id):

    # For help authenticating your client, visit
    # https://cloud.google.com/docs/authentication/getting-started
    client = datastore.Client(project=project_id)

    # All namespaces
    query = client.query(kind="__namespace__")
    query.keys_only()

    all_namespaces = list(query.fetch())
    return [x.key.name for x in all_namespaces]


def _clean_project(project_id):

    print("Processing project: %s" % project_id)

    all_namespaces = _get_all_namespaces(project_id)

    for current_namespace in all_namespaces:
        print("------------------------")
        if _namespace_needs_to_be_cleaned(project_id, current_namespace):
            print("Namespace should be removed %s" % current_namespace)
            _clean_namespace(project_id, current_namespace)
        else:
            print("Namespace is clean %s" % current_namespace)

if __name__ == '__main__':

    all_projects = list(_get_all_hotel_projects())
    random.shuffle(all_projects)

    # for project_id in all_projects:
    #     _clean_project(project_id)

    params = [(project_id,) for project_id in all_projects]
    concurrency_utils.execute_in_parallel(_clean_project, params, 10)