import json

a = "{\"numfactu\": \"\", \"original_referer\": \"https://www.landmarhotels.com/\", \"real_promotions_keys\": [\"ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgICk8dr8CwyiARBsYW5kbWFyLWdpZ2FudGVz\", \"ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgID0zaWbCQyiARBsYW5kbWFyLWdpZ2FudGVz\", \"ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgIDM7sPVCQyiARBsYW5kbWFyLWdpZ2FudGVz\"], \"datatransdata\": \"yDSIlTiQfZQ+VNXBQcjr39KG+r3t9n5rBGNsV1T64QivS65Xq6xvuBLwhlAvY0RGxRcBafc+bJi7Yyg6E53uIWOzkSLZg4Ii7yN6WgVjfB8LdS0Vna8WoYQlEv5Hs8AFmsTFMgbjKYjJyyDp2xhs5w8ida04NdqYVKn4xgOk4q2PwVqbHGOTk9CyZaam0Sl1PFDqjP7cqe8LBS1ZPZvTQunbJClg6B28Cw9TdZNe+R1i2SM97tsqOrW095mQC+dTUyzPUAsBKX22OJO8iwfEyZYvju7jy/PRg6YGyWRwUUkWyndF3n/fArXteELTOi3mCLInUfx/Yaf9G3zuqrUwv/nslVAfMUwh9RWoIwuX30fPUJZgg0cqCQry4FQXpqtGwIiUSpoUftwr1aOXsIJjE38IM/z3JOdK/rI6jf6CDefm98w+tOFjg8yD6sMq/r2+2o8pfvjjDuHcGGhct+ZKxPVcgP7oM8CmtC618rdT86DLHoecr5IGGI21TYV3FfpujMChSSFbM8Vjs5Ei2YOCItjOUnFLBNxW3Mss4W1Ukylr1PcrcIEDNtxoMycvIpefgkp/ZutH71fghfHJ7NgWQR/trq65cBOxhQldpkWUEk+jN0eln9KPgaty1P4UJfpawdnexRea11cG95X/t2iPC3u/9+1CcGj3yH6vvFLG5aQ93Db9FpaQb8m8emrhhSgxUT+YW1JWt1mMwKFJIVszxesEQIuyhJup+hNq/SgedH3Js9yPppARd2u8oPi5KKX74Tst2kVoXqjjEUUNFNHh6MIpDNpLoaeP/p0S0Xwwl9xU3RX3SoQ11fR2dWsR02fHPJemEIO0XCwy5qWzFJjW4BawHmeH5sK24TLoagwPkifdlTI2CLNjra9YAETvVJHEuex9vvl/C8Z9KXytSHuaQ+7CB47IVVzWa9V6j4y1Y6cRtGdvgMJM/4kCSq8cLrhNj6P/eauqESVrAl5nn5GX+7aczJnbarIlf1ftPijDJBoPTRiIH6E2QpJ75qmSm6xB/36d3afsULM=\", \"cc_datas\": \"iMuO/PbRFyrpfX8a3JGGLh09MXRem+eLATMilqq7gN+8qXDlJ/gUj/w05RNlNnyReu+vfsglUQM=\", \"geo_location_info\": {\"latitud\": \"0.000000\", \"longitud\": \"0.000000\", \"X-AppEngine-Region\": \"?\", \"X-AppEngine-City\": \"?\", \"X-AppEngine-CityLatLong\": \"0.000000,0.000000\", \"ip_address\": \"2001:67c:2628:647:12::314\", \"X-AppEngine-Country\": \"NL\"}, \"birthday\": \"\", \"personalId\": \"\", \"prices_per_day\": {\"1: ahJlfnNlY3VyZS1ib29raW5nMzByFQsSCFJvb21UeXBlGICAgMT-yOMLDKIBEGxhbmRtYXItZ2lnYW50ZXM\": {\"total\": 959.88, \"23/07/2022\": [\"310.00\", \"31.00\", \"279.00\"], \"22/07/2022\": [\"270.00\", \"27.00\", \"243.00\"], \"25/07/2022\": [\"267.00\", \"48.06\", \"218.94\"], \"24/07/2022\": [\"267.00\", \"48.06\", \"218.94\"]}}, \"pci_integration\": \"True\", \"session_search_info\": {\"timestamp\": \"2022-07-03 18:15:53\", \"id\": \"4dc85519-19b5-40d6-86a2-a4f187d61f4c\"}, \"check-allow-notifications\": true, \"numflight\": \"\", \"modification_timestamp\": \"2022-07-03 18:16:47\", \"original_rooms\": {\"1\": {\"kids\": 0, \"id\": \"ahJlfnNlY3VyZS1ib29raW5nMzByFQsSCFJvb21UeXBlGICAgMTChfIIDKIBEGxhbmRtYXItZ2lnYW50ZXM\", \"babies\": 0, \"adults\": 2}}}"

b = json.loads(a)
print(b)
print(json.dumps(b))
{"numfactu": "", "original_referer": "https://www.landmarhotels.com/", "real_promotions_keys": ["ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgICk8dr8CwyiARBsYW5kbWFyLWdpZ2FudGVz", "ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgID0zaWbCQyiARBsYW5kbWFyLWdpZ2FudGVz", "ahJlfnNlY3VyZS1ib29raW5nMzByFgsSCVByb21vdGlvbhiAgIDM7sPVCQyiARBsYW5kbWFyLWdpZ2FudGVz"], "datatransdata": "yDSIlTiQfZQ+VNXBQcjr39KG+r3t9n5rBGNsV1T64QivS65Xq6xvuBLwhlAvY0RGxRcBafc+bJi7Yyg6E53uIWOzkSLZg4Ii7yN6WgVjfB8LdS0Vna8WoYQlEv5Hs8AFmsTFMgbjKYjJyyDp2xhs5w8ida04NdqYVKn4xgOk4q2PwVqbHGOTk9CyZaam0Sl1PFDqjP7cqe8LBS1ZPZvTQunbJClg6B28Cw9TdZNe+R1i2SM97tsqOrW095mQC+dTUyzPUAsBKX22OJO8iwfEyZYvju7jy/PRg6YGyWRwUUkWyndF3n/fArXteELTOi3mCLInUfx/Yaf9G3zuqrUwv/nslVAfMUwh9RWoIwuX30fPUJZgg0cqCQry4FQXpqtGwIiUSpoUftwr1aOXsIJjE38IM/z3JOdK/rI6jf6CDefm98w+tOFjg8yD6sMq/r2+2o8pfvjjDuHcGGhct+ZKxPVcgP7oM8CmtC618rdT86DLHoecr5IGGI21TYV3FfpujMChSSFbM8Vjs5Ei2YOCItjOUnFLBNxW3Mss4W1Ukylr1PcrcIEDNtxoMycvIpefgkp/ZutH71fghfHJ7NgWQR/trq65cBOxhQldpkWUEk+jN0eln9KPgaty1P4UJfpawdnexRea11cG95X/t2iPC3u/9+1CcGj3yH6vvFLG5aQ93Db9FpaQb8m8emrhhSgxUT+YW1JWt1mMwKFJIVszxesEQIuyhJup+hNq/SgedH3Js9yPppARd2u8oPi5KKX74Tst2kVoXqjjEUUNFNHh6MIpDNpLoaeP/p0S0Xwwl9xU3RX3SoQ11fR2dWsR02fHPJemEIO0XCwy5qWzFJjW4BawHmeH5sK24TLoagwPkifdlTI2CLNjra9YAETvVJHEuex9vvl/C8Z9KXytSHuaQ+7CB47IVVzWa9V6j4y1Y6cRtGdvgMJM/4kCSq8cLrhNj6P/eauqESVrAl5nn5GX+7aczJnbarIlf1ftPijDJBoPTRiIH6E2QpJ75qmSm6xB/36d3afsULM=", "cc_datas": "iMuO/PbRFyrpfX8a3JGGLh09MXRem+eLATMilqq7gN+8qXDlJ/gUj/w05RNlNnyReu+vfsglUQM=", "geo_location_info": {"latitud": "0.000000", "longitud": "0.000000", "X-AppEngine-Region": "?", "X-AppEngine-City": "?", "X-AppEngine-CityLatLong": "0.000000,0.000000", "ip_address": "2001:67c:2628:647:12::314", "X-AppEngine-Country": "NL"}, "birthday": "", "personalId": "", "prices_per_day": {"1: ahJlfnNlY3VyZS1ib29raW5nMzByFQsSCFJvb21UeXBlGICAgMT-yOMLDKIBEGxhbmRtYXItZ2lnYW50ZXM": {"total": 959.88, "23/07/2022": ["310.00", "31.00", "279.00"], "22/07/2022": ["270.00", "27.00", "243.00"], "25/07/2022": ["267.00", "48.06", "218.94"], "24/07/2022": ["267.00", "48.06", "218.94"]}}, "pci_integration": "True", "session_search_info": {"timestamp": "2022-07-03 18:15:53", "id": "4dc85519-19b5-40d6-86a2-a4f187d61f4c"}, "check-allow-notifications": true, "numflight": "", "modification_timestamp": "2022-07-03 18:16:47", "original_rooms": {"1": {"kids": 0, "id": "ahJlfnNlY3VyZS1ib29raW5nMzByFQsSCFJvb21UeXBlGICAgMTChfIIDKIBEGxhbmRtYXItZ2lnYW50ZXM", "babies": 0, "adults": 2}}}
