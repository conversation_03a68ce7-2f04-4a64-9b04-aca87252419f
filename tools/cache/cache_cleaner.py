import datetime

import pytz

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.redis import redis_communicator
from paraty_commons_3.redis.redis_communicator import build_redis_client

'''
This file allows you to clean hotel managers cache if required
i.e. Old cache instances that are no longer used
'''

# Modify this in case you want to remove younger cache entries, i.e. 0 will remove everything
MAX_DAYS = 30


def delete_timestamps_from_redis(hotel_code, manager_version):
    print(f"======================REDIS {hotel_code} at {manager_version} ======================")
    hotel = get_hotel_by_application_id(hotel_code)
    hotel_id = hotel['id']

    host = '************'
    if manager_version == 'admin-hotel3':
        host = '**************'

    redis_communicator.pool = None
    redis_client = build_redis_client(host=host, port=6666, password="nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ")
    timestamps = redis_client.get(f"cache_timestamps_{hotel_id}")
    print(timestamps is not None)
    redis_client.delete(f"cache_timestamps_{hotel_id}")


def delete_expired_entities(hotel_code, manager_version, entry_type):
    print(f"====================== {hotel_code} {manager_version} ======================")

    hotel = get_hotel_by_application_id(hotel_code)
    hotel_id = hotel['id']

    cached_entities = datastore_communicator.get_using_entity_and_params("EntryValidTimeStamp2", [('entryType', '=', entry_type)], hotel_code=manager_version)

    utc_now = datetime.datetime.now(pytz.utc)

    entities_to_delete = []

    entities_to_delete = cached_entities
    # for entity in cached_entities:
    #     java_timestamp = entity.get('timestamp')
    #     # Convert Java timestamp to Python datetime object
    #     dt = datetime.datetime.fromtimestamp(java_timestamp / 1000.0)
    #
    #     # Convert the datetime object to UTC
    #     dt_utc = dt.astimezone(pytz.utc)
    #
    #     age = utc_now - dt_utc
    #
    #     if age > datetime.timedelta(days=MAX_DAYS):
    #         # print({'deleting entity': entity.key.name, 'age': age})
    #         entities_to_delete.append(entity.key)

    print(f"Deleting {len(entities_to_delete)} entities")
    datastore_communicator.delete_entity_multi(entities_to_delete, hotel_code=manager_version)

    print(f"===============================================================")


if __name__ == '__main__':

    all_hotels = [x['applicationId'] for x in hotel_manager_utils.get_all_valid_hotels()]

    # Uncomment this if you want to test with a single hotel
    # all_hotels = ['ona-internacional']

    for hotel_code in all_hotels:
        # delete_timestamps_from_redis(hotel_code, 'admin-hotel')
        # delete_timestamps_from_redis(hotel_code, 'admin-hotel3')
        # delete_expired_entities(hotel_code, 'admin-hotel')
        delete_expired_entities(hotel_code, 'admin-hotel', entry_type='restrictionsExtraData_')
        # delete_expired_entities(hotel_code, 'admin-hotel3:', entry_type='finalPriceDays2_')
