import datetime
import pytz

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.redis import redis_communicator
from paraty_commons_3.redis.redis_communicator import build_redis_client

'''
This file generates a report of what is cached for a given hotel
'''


def get_timestamps_from_redis(hotel_code, manager_version):
    print(f"======================REDIS {hotel_code} at {manager_version} ======================")
    hotel = get_hotel_by_application_id(hotel_code)
    hotel_id = hotel['id']

    host = '************'
    if manager_version == 'admin-hotel3':
        host = '**************'

    redis_communicator.pool = None
    redis_client = build_redis_client(host=host, port=6666, password="nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ")
    timestamps = redis_client.get(f"cache_timestamps_{hotel_id}")
    print(timestamps is not None)


def get_cache_report(hotel_code, manager_version):

    print(f"====================== {manager_version} ======================")

    hotel = get_hotel_by_application_id(hotel_code)
    hotel_id = hotel['id']

    cached_entities = datastore_communicator.get_using_entity_and_params("EntryValidTimeStamp2", [('applicationId', '=', hotel_id)], hotel_code=manager_version)

    utc_now = datetime.datetime.now(pytz.utc)

    for entity in cached_entities:
        java_timestamp = entity.get('timestamp')
        # Convert Java timestamp to Python datetime object
        dt = datetime.datetime.fromtimestamp(java_timestamp / 1000.0)

        # Convert the datetime object to UTC
        dt_utc = dt.astimezone(pytz.utc)

        age = utc_now - dt_utc

        print({'entity': entity.key.name, 'age': age})

    print(f"===============================================================")


if __name__ == '__main__':

    # all_hotels = [x['applicationId'] for x in hotel_manager_utils.get_all_valid_hotels()]

    all_hotels = ['fay-victoria']

    for hotel_code in all_hotels:
        get_timestamps_from_redis(hotel_code, 'admin-hotel')
        get_timestamps_from_redis(hotel_code, 'admin-hotel3')
        get_cache_report(hotel_code, 'admin-hotel')
        get_cache_report(hotel_code, 'admin-hotel3')