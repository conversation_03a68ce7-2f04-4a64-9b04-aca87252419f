from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.utils.cache_seeker import cache_seeker_utils
import redis

redis_client = redis.StrictRedis(host='***********', port=6666, password='nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ')

@timeit
def _test_set_data(func_to_call, host, num_tests=500):
    '''Create multiple calls to set to the cache
    '''
    cache_seeker_utils.CACHE_SEEKER_URL = host
    for i in range(num_tests):
        current_key = f'test {i}'
        print(func_to_call(current_key, 'test'))

@timeit
def _test_get_data(func_to_call, host, num_tests=500):
    '''Create multiple calls to get to the cache
    '''
    cache_seeker_utils.CACHE_SEEKER_URL = host
    for i in range(num_tests):
        current_key = f'test {i}'
        print(func_to_call(current_key))


def _test_cache_seeker(host, num_tests=500):
    '''Create multiple calls to get and set to the cache
    '''

    # Cache seeker
    _test_set_data(cache_seeker_utils.set_data, host, num_tests)
    _test_get_data(cache_seeker_utils.get_data, host, num_tests)

    # Redis
    _test_set_data(redis_client.set, host, num_tests)
    _test_get_data(redis_client.get, host, num_tests)



if __name__ == '__main__':
    # To test concurrency execute this script multiple times
    _test_cache_seeker('https://cache-seeker.appspot.com', 10)
