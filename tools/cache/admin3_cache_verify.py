import datetime
import json
import pytz
import chardet

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_all_valid_hotels
from paraty_commons_3.redis import redis_communicator
from paraty_commons_3.redis.redis_communicator import build_redis_client


def compare_timestamps_from_redis(hotel_code):
    print(f"======================REDIS {hotel_code} ======================")
    hotel = get_hotel_by_application_id(hotel_code)
    hotel_id = hotel['id']

    host_admin = '************'
    host_admin3 = '**************'

    redis_communicator.pool = {}
    redis_client = build_redis_client(host=host_admin, port=6666, password="nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ")
    redis_client_admin3 = build_redis_client(host=host_admin3, port=6666, password="nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ")
    timestamps = redis_client.get(f"cache_timestamps_{hotel_id}")
    timestamps_admin3 = redis_client_admin3.get(f"cache_timestamps_{hotel_id}")
    print("////////////// admin-hotel ////////////////")
    print(timestamps)
    print("////////////// admin-hotel3 ////////////////")
    print(timestamps_admin3)

    if timestamps and timestamps_admin3:
        timestamps = timestamps.decode('ISO-8859-1')
        timestamps_admin3 = timestamps_admin3.decode('ISO-8859-1')

        admin_json = json.loads(timestamps[timestamps.find("{"):])
        admin3_json = json.loads(timestamps_admin3[timestamps_admin3.find("{"):])

        for key in admin_json.keys():
            if key in admin3_json:
                timestamp1 = admin_json[key]
                timestamp2 = admin3_json[key]

                time_diff = abs((timestamp1 - timestamp2) / (1000 * 60))

                print(f"Time diff in {key}: {time_diff} min")
            else:
                print(f"Key '{key}' not present in admin3_json")


def compare_timestamps_for_entry_valid(hotel_id):

    admin_entities = datastore_communicator.get_using_entity_and_params("EntryValidTimeStamp2", search_params=[("applicationId", "=", hotel_id)], hotel_code="admin-hotel:")
    admin3_entities = datastore_communicator.get_using_entity_and_params("EntryValidTimeStamp2", search_params=[("applicationId", "=", hotel_id)], hotel_code="admin-hotel3:")

    for entity in admin_entities:
        for entity3 in admin3_entities:
            if entity.key.name == entity3.key.name:
                time_diff = abs((entity["timestamp"] - entity3["timestamp"]) / (1000 * 60))
                if time_diff:
                    print(f"Admin: {entity['timestamp']} Admin3: {entity3['timestamp']} Time diff: {time_diff} in {entity.key.flat_path}")
                break


if __name__ == "__main__":
    #compare_timestamps_for_entry_valid(4001)
    # hotels = get_all_valid_hotels()
    # for hotel in hotels:
    compare_timestamps_from_redis("sno-formigal")
