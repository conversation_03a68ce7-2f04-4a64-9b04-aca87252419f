import logging

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils


def _get_all_monthly_entity(entity_name, hotel_code):
    all_entities = datastore_communicator.get_using_entity_and_params(entity_name, hotel_code=hotel_code)
    return all_entities


def _clean_entity(entity_name, hotel):
    all_monthly_entities = _get_all_monthly_entity(entity_name, hotel['applicationId'])
    sorted_data = {}
    for entity in all_monthly_entities:

        date = entity['date']
        multirate = entity['multirateKey']

        key = '%s_%s' % (date, multirate)
        if not key in sorted_data:
            sorted_data[key] = []

        sorted_data[key].append(entity)

    for k,v in sorted_data.items():
        if len(v) > 1:
            # Compare to see if they are the same
            # Remove duplicates
            canonical_content = v[0]['content']
            logging.info("Cleaning %s entities", len(v))

            keys_to_delete = [x.key for x in v[1:]]
            datastore_communicator.delete_entity_multi(keys_to_delete, hotel_code=hotel['applicationId'])
            # for copy in v[1:]:
            #     datastore_communicator.delete_entity(entity_name, copy.key.id, hotel_code=hotel['applicationId'])



if __name__ == '__main__':
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    for hotel in all_hotels:
        if 'urban' in hotel['applicationId']:
            _clean_entity("MultiRateRoomsMonth", hotel)
