import datetime
import json
import os

import requests

from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel, get_integration_configuration_of_hotel
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from tools.billing.total_price_calculator import calculate_total_price

def _get_all_callcenter_agents():
    all_users = datastore_communicator.get_using_entity_and_params('ParatyUser', search_params=[], hotel_code='admin-hotel')

    result = []

    for user in all_users:
        if 'Ring2travel @@ True' in user.get('configurationMap', []):
            result.append(user['name'])

    return result

def _get_all_port_hotels():
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    return [x for x in all_hotels if 'porthotels' in x['url']]


def get_all_bookings(hotel_code, start_date, end_date):

    all_bookings = get_reservations_of_hotel({'applicationId': hotel_code}, start_date, end_date)

    result = []
    for booking in all_bookings:
        result.append(booking)

    return result


def _calculate_commissions(hotel_code, all_bookings):
    all_identifiers = [x['identifier'] for x in all_bookings if x and x.get('identifier')]

    chunked_identifiers = list(chunks(all_identifiers, 50))

    # Calculate commission for each booking
    result = {}
    for current_chunk in chunked_identifiers:
        response = requests.post(f'https://stats-seeker-dot-admin-hotel.appspot.com/commissions?hotel_code={hotel_code}', data=' '.join(current_chunk))
        commissions = response.json()

        for current_identifier in current_chunk:
            if current_identifier in commissions['commissions']:
                result[current_identifier] = commissions['commissions'][current_identifier]['amount']


    return result


def chunks(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]

def _get_tripadvisor_config(hotel_code):

    hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
    result = get_integration_configuration_of_hotel(hotel, 'tripadvisor')

    if not result:
        return {}

    configs = {}
    for config in result[0]['configurations']:
        key, value = config.split('@@')
        configs[key.strip()] = value.strip()

    return configs


def _merge_csv_files(folder_name):
    all_files = os.listdir(folder_name)

    with open(f'{folder_name}/all.csv', 'w') as f:
        f.write('hotel_code, hotel name, lat, long, city, country, price, commission, source, date, start date, end date')
        for file in all_files:
            with open(f'{folder_name}/{file}', 'r') as f2:
                f.write(f2.read())


def _process_hotel(hotel, today, start_date, end_date):
    try:
        hotel_code = hotel['applicationId']
        if os.path.exists(f'{today}/{hotel_code}.csv'):
            return

        tripadvsor_config = _get_tripadvisor_config(hotel_code)
        latitude = tripadvsor_config.get('latitude', '')
        longitude = tripadvsor_config.get('longitude', '')
        country = tripadvsor_config.get('country', '')
        city = tripadvsor_config.get('city', '')

        all_bookings = get_all_bookings(hotel_code, start_date, end_date)
        commissions = _calculate_commissions(hotel_code, all_bookings)

        with open(f'{today}/{hotel_code}.csv', 'w') as f:
            # f.write('hotel_code, hotel name, lat, long, city, country, price, commission, source, date, start date, end date')
            for booking in all_bookings:
                try:
                    price = calculate_total_price(booking, include_additional_services=True)
                    commission = commissions.get(booking['identifier'], 0.0)
                    f.write(f'{hotel_code}, {hotel["name"]}, {latitude}, {longitude}, {city}, {country}, {price}, {commission}, {booking["source"]}, {booking["timestamp"]}, {booking["startDate"]}, {booking["endDate"]}\n')
                except Exception as e:
                    print(f'Error processing {hotel["applicationId"]}: {e}')

    except Exception as e:
        print(make_traceback())
        print(f'Error processing {hotel["applicationId"]}: {e}')

def _build_csv(start_date, end_date):
    all_hotels = hotel_manager_utils.get_all_valid_hotels()

    # Create directory if not exists
    today = '2023-01-23' #  datetime.datetime.now().strftime('%Y-%m-%d')
    if not os.path.exists(today):
        os.makedirs(today)

    params = []
    for hotel in all_hotels:
        params.append((hotel, today, start_date, end_date))
    concurrency_utils.execute_in_parallel(_process_hotel, params, 5)



def _build_gdpr_confirmation_don_pancho():
    all_bookings = get_all_bookings("hotel-don-pancho", "2021-01-01", "2023-12-31")
    csv = 'identifier, email, timestamp, start_date, end_date, accept_conditions, notifications\n'

    for booking in all_bookings:

        if booking['startDate'] < '2023-05-11':
            continue

        identifer = booking['identifier']
        extra_info = json.loads(booking.get('extraInfo', {}))
        accept_conditions = extra_info.get('accept-conditions-and-policies', 'off')
        gdpr_confirmation = extra_info.get('check-allow-notifications', 'off')
        timestamp = booking['timestamp']
        start_date = booking['startDate']
        end_date = booking['endDate']
        email = booking['email']

        csv += f'{identifer}, {email}, {timestamp}, {start_date}, {end_date}, {accept_conditions}, {gdpr_confirmation}\n'

    with open('don-pancho-gdpr.csv', 'w') as f:
        f.write(csv)

    return



if __name__ == '__main__':
    # _build_csv('2022-01-01', '2022-12-31')
    # _merge_csv_files('2023-01-23')

    _build_gdpr_confirmation_don_pancho()