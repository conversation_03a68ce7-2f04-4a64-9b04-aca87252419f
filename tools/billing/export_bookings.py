from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from tools.billing.total_price_calculator import calculate_total_price

port_agents = set()


def _get_all_callcenter_agents():
    all_users = datastore_communicator.get_using_entity_and_params('ParatyUser', search_params=[], hotel_code='admin-hotel')

    result = []

    for user in all_users:
        if 'Ring2travel @@ True' in user.get('configurationMap', []):
            result.append(user['name'])

    return result

port_agents.update(_get_all_callcenter_agents())

def _get_all_port_hotels():
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    return [x for x in all_hotels if 'porthotels' in x['url']]


def get_all_bookings(hotel_code):

    all_bookings = get_reservations_of_hotel({'applicationId': hotel_code}, '2020-06-01', '2022-01-01')

    result = []
    for booking in all_bookings:
        if booking['agent'] in port_agents and booking['startDate'] >= '2021-01-01' and booking['startDate'] < '2022-01-01':
            result.append(booking)

    return result

def _build_csv():
    all_hotels = _get_all_port_hotels()
    total = 0
    for hotel in all_hotels:
        hotel_code = hotel['applicationId']
        all_bookings = get_all_bookings(hotel_code)
        total_hotel = 0

        with open(f'{hotel_code}.csv', 'w') as f:
            f.write('fecha_entrada,agente,localizador,precio,commision\n')
            for booking in all_bookings:
                price = calculate_total_price(booking, include_additional_services=True)
                commission = price * 0.03
                f.write(f'{booking["startDate"]},{booking["agent"]},{booking["identifier"]},{price}, {commission}\n')
                total += commission
                total_hotel += commission

        # Round total_hotel to 2 decimals
        total_hotel = round(total_hotel, 2)
        print(f'{hotel_code}: {total_hotel}')

    total = round(total, 2)
    print(f'Total: {total}')



if __name__ == '__main__':
    _build_csv()