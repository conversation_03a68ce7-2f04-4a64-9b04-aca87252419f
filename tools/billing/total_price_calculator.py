import json


def _calculate_price_default(reservation, include_additional_services):

	if not reservation:
		return None

	total = 0.0
	total += float(reservation['price'])

	if include_additional_services and reservation.get('priceSupplements'):
		total += float(reservation['priceSupplements'])

	return total


def _is_ammended_price_valid(ammended_price, expected_price):

	try:
		ammended_price_float = float(ammended_price)

		#If there is no more than 50% of change from expected_price we consider it valid
		percentage_diff = ammended_price_float/expected_price
		return (percentage_diff < 1.5) and (percentage_diff > 0.5)


	except:
		#Invalid number, we need to check it manually
		return False


def _obtain_total_price_from_ammened(reservation, include_additional_services):

	ammended_price = reservation.get('amendedPrice', '')
	incidents = reservation.get('incidents', '')
	expected_price = _calculate_price_default(reservation, include_additional_services)

	#Normal modification
	if _is_ammended_price_valid(ammended_price, expected_price):
		return float(ammended_price)

	#i.e. Some clients just add it without no purpose
	if incidents and (not ammended_price and 'OK' in incidents.lower()):
		return expected_price

	return expected_price


def calculate_total_price(reservation, include_additional_services=True):
	'''
	Note that this is not trivial, it depends if there are is an ammended price.
	'''

	#Cancelled reservations are considered to have 0 value.
	if reservation.get('cancelled') == 'true':
		return 0.0

	#With bono functionalities we need to bill the original price
	if 'original_price_before_discount' in reservation.get('extraInfo'):
		extra_info = json.loads(reservation.get('extraInfo'))
		reservation['price'] = "%.2f" % extra_info.get('original_price_before_discount')


	if reservation.get('amendedPrice') or reservation.get('incidents'):
		return _obtain_total_price_from_ammened(reservation, include_additional_services)

	#Nothing strange, so let's just calculate it normally
	return _calculate_price_default(reservation, include_additional_services)
