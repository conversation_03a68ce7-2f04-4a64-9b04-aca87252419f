import time
import threading
import cachetools

'''
This cache is useful when you don't want to store data forever, but you want to store it for a while.
And you want to use the least frequently used algorithm to remove data from the cache.

# Usage example
lfu_cache = LFUCacheWithTTL(maxsize=3, ttl=10)

lfu_cache['a'] = 1
lfu_cache['b'] = 2
lfu_cache['c'] = 3

print(lfu_cache['a'])  # Output: 1

time.sleep(11)  # Wait for the TTL to expire

try:
    print(lfu_cache['a'])
except KeyError as e:
    print(e)  # Output: Key 'a' has expired

'''

class LFUCacheWithTTL:
    def __init__(self, maxsize, ttl):
        self.cache = cachetools.LFUCache(maxsize=maxsize)
        self.expiration = {}
        self.lock = threading.Lock()
        self.ttl = ttl

    def __getitem__(self, key):
        with self.lock:
            self._check_expiration(key)
            return self.cache[key]

    def __setitem__(self, key, value):
        with self.lock:
            self.cache[key] = value
            self.expiration[key] = time.time() + self.ttl

    def _check_expiration(self, key):
        if key in self.expiration:
            if time.time() > self.expiration[key]:
                del self.cache[key]
                del self.expiration[key]
                raise KeyError(f"Key '{key}' has expired")