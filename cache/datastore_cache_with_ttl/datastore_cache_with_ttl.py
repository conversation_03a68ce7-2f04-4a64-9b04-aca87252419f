import logging
from datetime import datetime, timedelta

from google.cloud import ndb

from paraty import Config

'''
This is a cache that uses the datastore as a backend.
It uses a TTL to expire the entries.

To use this, activate the TTL DAta retention policy at Google Cloud.
See: https://console.cloud.google.com/datastore/databases/-default-/ttl?project=trivago-adapter

'''


class CacheEntryWithTTL(ndb.Model):
    value = ndb.PickleProperty(compressed=True)
    timestamp = ndb.DateTimeProperty(auto_now_add=True)
    ttl = ndb.DateTimeProperty()


# This is to avoid caching the results of the queries, too many and too large to fit in memory
CacheEntryWithTTL._use_cache = False


def get_value(key):
    entry = CacheEntryWithTTL.get_by_id(key)
    if not entry:
        return None

    if entry.ttl < datetime.now():
        logging.info("Entry is expired, deleting it")
        entry.key.delete()
        return None

    return entry.value


def set_value(key, value, ttl=24 * 60 * 60):
    '''
    Default TTL is 24 hours
    '''

    ttl_datetime = datetime.now() + timedelta(seconds=ttl)
    CacheEntryWithTTL(id=key, value=value, ttl=ttl_datetime).put()


if __name__ == '__main__':
    ndb_client = ndb.Client(project=Config.PROJECT)
    with ndb_client.context():
        set_value('test2', 'test Value', 60)
        print(get_value('test2'))
