'''
See https://stackoverflow.com/questions/56113604/how-to-group-related-request-log-entries-gae-python-3-7-standard-env
NOTE: Modified to support %s backward compatibility

To use this:

Make sure you include the following line in requirements.txt:
- google-cloud-logging

- Instead of import logging, import the following:
from paraty_commons_3.logging.my_gae_logging import logging

- Use logging as usual.

- In GAE log viewer,
1. search for the text related to your search
2. Click on the log, and click on trace (i.e. trace: "projects/webspeed-seeker/traces/92b9e73c3068ae1b92bffe47b3ce2eac").
3. Click on Show Matching entries in the popup that appears
4. A new filter will appear with all the logs related to that request

'''
import logging

import logging as python_logging
import os
import sys

from flask import request
try:
	from google.cloud import logging as gcp_logging
	from google.cloud.logging_v2 import Resource
except:
	print("This project won't be able to use logging")
from functools import wraps

# From GCP logging lib for Python2.7


CRITICAL = 50
FATAL = CRITICAL
ERROR = 40
WARNING = 30
WARN = WARNING
INFO = 20
DEBUG = 10
NOTSET = 0


_levelNames = {
	CRITICAL: 'CRITICAL',
	ERROR: 'ERROR',
	WARNING: 'WARNING',
	INFO: 'INFO',
	DEBUG: 'DEBUG',
	NOTSET: 'NOTSET',
	'CRITICAL': CRITICAL,
	'ERROR': ERROR,
	'WARN': WARNING,
	'WARNING': WARNING,
	'INFO': INFO,
	'DEBUG': DEBUG,
	'NOTSET': NOTSET,
}


def get_trace_id():
	trace_str = ''
	try:
		trace_id = request.headers.get('X-Cloud-Trace-Context', 'no_trace_id').split('/')[0]
		trace_str = "projects/{project_id}/traces/{trace_id}".format(
			project_id=os.getenv('GOOGLE_CLOUD_PROJECT'),
			trace_id=trace_id)
	except:
		pass
	return trace_str

MAX_LOG_LENGTH = 50000

def chunker(seq, size):
	return (seq[pos:pos + size] for pos in range(0, len(seq), size))

def split_if_required(method):
	@wraps(method)
	def safe_chunked(*args, **kw):
		try:
			my_chunks = chunker(str(args[1]), MAX_LOG_LENGTH)
			for chunk in my_chunks:
				current_args = args[0], chunk, *args[2:]
				method(*current_args, **kw)
		except Exception as e:
			args[0].logger.log_text("Error adding log: %s" % e, resource=args[0].resource, severity=_levelNames.get(ERROR), trace=get_trace_id())

	return safe_chunked


def _render_text(text, *args):
	try:
		result = text % args
		return result
	except:
		return str(text)

class Logging:
	def __init__(self):
		self._logger = None

	@property
	def logger(self):
		if self._logger is not None:
			return self._logger

		log_client = gcp_logging.Client()

		# This is the resource type of the log
		log_name = 'appengine.googleapis.com%2Fstdout'

		# Inside the resource, nest the required labels specific to the resource type

		self._logger = log_client.logger(log_name)
		return self._logger

	@property
	def resource(self):
		resource = Resource(
			type="gae_app",
			labels={
				'module_id': os.getenv('GAE_SERVICE'),
				'project_id': os.getenv('GOOGLE_CLOUD_PROJECT'),
				'version_id': os.getenv('GAE_VERSION')
			}
		)
		return resource

	@split_if_required
	def log(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, trace=get_trace_id())

	@split_if_required
	def debug(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(DEBUG), trace=get_trace_id())

	@split_if_required
	def info(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(INFO), trace=get_trace_id())

	@split_if_required
	def warning(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(WARNING), trace=get_trace_id())

	@split_if_required
	def warn(self, text, *args):
		return self.warning(text, *args)

	@split_if_required
	def exception(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(ERROR), trace=get_trace_id())


	def basicConfig(level=logging.INFO):
		#TODO
		pass

	@split_if_required
	def error(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(ERROR), trace=get_trace_id())

	@split_if_required
	def critical(self, text, *args):
		text = _render_text(text, *args)
		self.logger.log_text(text, resource=self.resource, severity=_levelNames.get(CRITICAL), trace=get_trace_id())


if os.getenv('GAE_VERSION') and 'gcp_logging' in sys.modules:  # check if running under gcp env
	logging = Logging()
	logging.info("Using custom Logging!")
else:
	# when not running under gcp env, use standard python_logging
	logging = python_logging
	logger = logging.getLogger()
	logger.setLevel(logging.INFO)
	logging.info("Using default Logging")


if __name__ == '__main__':
	# _render_text("hola: %s, %s", 'hola', 'adios')
	# _render_text("hola")
	Logging().info("hola: %s, %s", 'hola', 'adios')
	Logging().info("hola")