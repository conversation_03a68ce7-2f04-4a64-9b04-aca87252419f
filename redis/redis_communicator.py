import redis
from redis import Redis

'''
Things to change when you create a new redis server from bitnami (at marketplace):
- See /opt/bitnami/redis/etc/redis.conf
bind 0.0.0.0 # This allow remote connections
timeout 300  # Automatically close connections after they are 5 minutes idle  (Be careful with this or the machine blocks).
maxclients 1000  # To make sure we accept enough connections
maxmemory-policy allkeys-lfu  # Evict last frecuent used keys to leave space for new ones

'''

REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"
EUROPE_REDIS = "************"
AMERICA_REDIS = "*************"
HOTELS_PORT = 6666


# Create a connection pool
pool = {}


def build_redis_client(host, password=None, port=6379):
    if host not in pool:
        pool[host] = redis.ConnectionPool(host=host, port=port, db=0, password=password, socket_connect_timeout=1, socket_timeout=4, retry_on_timeout=True)

    return Redis(connection_pool=pool[host])


def build_redis_client_hotel(hotel_code):
    host = _get_hotel_redis_host(hotel_code)
    return build_redis_client(host=host, password=REDIS_PASSWORD, port=HOTELS_PORT)


def _get_hotel_redis_host(hotel_code):
    from paraty_commons_3.datastore.datastore_utils import get_location_prefix
    hotel_location_prefix = get_location_prefix(hotel_code)

    if hotel_location_prefix.startswith('s'):
        _redis_host = AMERICA_REDIS
    else:
        _redis_host = EUROPE_REDIS

    return _redis_host



if __name__ == '__main__':
    redis_client = build_redis_client(host='*************', port=6379, password='v5N5SFvjozPx')

    key = 'your_keyjj8'
    value = 'your_value9'
    ttl_seconds = 60  # Set the TTL to 60 seconds

    # Set the value with the specified TTL
    redis_client.setex(key, ttl_seconds, value)
    redis_client.set(key, value)

    # Retrieve the value from Redis
    retrieved_value = redis_client.get(key)
    print(f'Retrieved value: {retrieved_value.decode("utf-8")}')
